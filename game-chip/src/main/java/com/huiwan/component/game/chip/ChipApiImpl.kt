package com.huiwan.component.game.chip

import android.content.Context
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.context.holder.ContextHolder
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.game.chip.bean.LittleGameBankruptcyProtection
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.user.UserService
import com.three.http.core.KtResultSuccess
import com.wejoy.littlegame.ChipExchangeType
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_MAIN
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_MATCH
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_REPLAY
import com.wejoy.littlegame.GAME_SCENE_VOICE_MAIN
import com.wejoy.littlegame.GameScene
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wepie.lib.api.plugins.track.ITrackExt
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.helper.dialog.progress.IProgressDialog
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale

class ChipApiImpl : ChipApi {

    private val _chipFlow = MutableStateFlow(UserService.get().selfUser.value?.chipCoin ?: 0L)
    val flow: StateFlow<Long> = _chipFlow

    init {
        UserService.get().selfUser.observeForever {
            MainScope().launch {
                _chipFlow.emit(it.chipCoin)
            }
        }
    }

    override fun updateChip() {
        UserService.get().refreshSelfInfo(null)
    }

    override fun gameChipFlow(): StateFlow<Long> = flow

    override fun handleChipNotEnough(
        context: Context, @GameScene scene: Int, @ChipExchangeType source: Int,
        info: LittleGameSimpleInfo, next: () -> Unit
    ) {
        ContextHolder.of(context, IProgressDialog::class.java).showProgressDialogDelay()
        MainScope().launch {
            doCheckChip(context, scene, source, info)?.take(1)?.collect {
                when (it) {
                    0 -> return@collect
                    1 -> next.invoke()
                }
            }
        }
    }

    override fun handleChipNotEnough(
        @GameScene scene: Int,
        @ChipExchangeType source: Int,
        info: LittleGameSimpleInfo,
        next: () -> Unit
    ) {
        val context = ActivityTaskManager.getInstance().topActivity ?: return
        handleChipNotEnough(context, scene, source, info, next)
    }

    override fun handleChipLimit(context: Context, info: LittleGameSimpleInfo): Flow<Int> =
        GameChipLimitDialog.show(context, info)

    override fun handleChipLimit(info: LittleGameSimpleInfo): Flow<Int>? {
        val context = ActivityTaskManager.getInstance().topActivity ?: return null
        return handleChipLimit(context, info)
    }

    override fun formatChip(num: Long): String {
        return if (num < K) {
            num.toString()
        } else if (num < M) {
            "${str4((num.toFloat() / K))}K"
        } else if (num < B) {
            "${str4((num.toFloat() / M))}M"
        } else if (num < T) {
            "${str4((num.toFloat() / B))}B"
        } else {
            "${num / B}B"
        }
    }

    override fun showProtectionDialog(
        context: Context, info: LittleGameSimpleInfo, data: LittleGameBankruptcyProtection
    ): Flow<Int> {
        val tips =
            StringBuilder(data.toastText.replace("{CoinNum}", data.receiveChip.toString()))
        tips.append("(${data.usedProtectionTimes + 1}/${data.totalProtectionTimes})")
        return GameChipProtectionDialog.show(context, info, tips.toString(), data.receiveChip)
    }

    private fun str4(num: Float): String {
        val df = NumberFormat.getNumberInstance(Locale("en", "US")) as DecimalFormat
        df.applyPattern("#.#")
        return df.format(num)
    }

    private suspend fun doCheckChip(
        context: Context, @GameScene scene: Int, @ChipExchangeType source: Int,
        info: LittleGameSimpleInfo
    ): Flow<Int>? {
        val ret = gameChipCheckProtectionInfo(info.gameType)
        ContextHolder.of(context, IProgressDialog::class.java).hideProgressDialog()
        if (ret is KtResultSuccess) {
            val data = ret.data
            return if (data.toast) {
                showProtectionDialog(context, info, data)
            } else {
                var matchInfo: GameConfig.MatchInfo? = null
                ConfigHelper.getInstance().gameConfig.getGameConfig(info.gameType).matchInfoList
                    .filter { it.currencyType == GameConfig.CURRENCY_CHIP }.forEach {
                        if (it.gameMode == info.gameMode && it.betLevel == info.betLevel
                            && (it.roomMode == info.mode || it.matchMode == info.mode)
                        ) {
                            matchInfo = it
                        }
                    }
                if (matchInfo != null) {
                    ToastUtil.show(
                        ResUtil.getStr(R.string.game_chip_not_enough_tips, matchInfo!!.coinDesc)
                    )
                }
                val referScreenName =
                    if (scene and GAME_SCENE_LITTLE_GAME_MATCH == GAME_SCENE_LITTLE_GAME_MATCH) {
                        TrackScreenName.GAME_PAGE_ENTRY
                    } else if (scene and GAME_SCENE_LITTLE_GAME_MAIN == GAME_SCENE_LITTLE_GAME_MAIN) {
                        TrackScreenName.GAME_PAGE_ENTRY
                    } else if (scene and GAME_SCENE_VOICE_MAIN == GAME_SCENE_VOICE_MAIN) {
                        TrackScreenName.GAME_PAGE_ENTRY
                    } else if (scene and GAME_SCENE_LITTLE_GAME_REPLAY == GAME_SCENE_LITTLE_GAME_REPLAY) {
                        TrackScreenName.GAME_RESULT
                    } else {
                        info.referScreenName
                    }
                val ext = mutableMapOf<String, Any>()
                ext["refer_screen_name"] = referScreenName
                ext["gameid"] = info.gameid
                if (info is ITrackExt) {
                    ext.putAll(info.trackExt)
                }
                ApiService.of(HwApi::class.java).showGameChipExchangeDialog(
                    info.gameType, info.gameMode, info.mode, info.betLevel,
                    ext
                )
            }
        } else {
            ToastUtil.show(ret.failedDesc)
            return null
        }
    }

    companion object {
        private const val K = 1000L
        private const val M = K * 1000L
        private const val B = M * 1000L
        private const val T = B * 1000L
    }
}