package com.wepie.lib.googlepay;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;
import android.text.format.DateUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.android.billingclient.api.AccountIdentifiers;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ConsumeResponseListener;
import com.android.billingclient.api.GetBillingConfigParams;
import com.android.billingclient.api.PendingPurchasesParams;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryPurchasesParams;
import com.google.android.gms.common.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.three.http.callback.EmptyDataCallback;
import com.three.http.callback.Result;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackEvent;
import com.wepie.liblog.main.HLog;

import java.lang.ref.SoftReference;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

class GooglePayClient implements PurchasesUpdatedListener {

    private static final String KEY_CURRENCY = "ggp_currency";
    private static final String KEY_LAST_WP_ORDER_ID = "ggp_last_wp_order_id";
    private static final String TAG_BUY = "buy";
    private static final String TAG_NO_HANDLE = "un-handle";
    private static final String TAG_RESTORE = "restore";
    private static final int RECONNECT_WHAT = 1000;
    private static String playStoreVersion = null;  // 不要直接拿来用，要调用getPlayStoreVersion()获取
    private final BillingClient client;
    private int billingReconnectCount = 0;
    private final int maxReconnectCount = 10;
    private long lastCheckUnHandlePurchaseTime = 0;
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == RECONNECT_WHAT) {
                connect();
            }
        }
    };
    private final List<SoftReference<DataCallback<IapApi.PayResult>>> callbackList = new ArrayList<>();
    private final Map<String, IDetails> skuMap = new ConcurrentHashMap<>();
    private int connectionCode = 0;
    private String connectionMsg = "";
    private volatile boolean connected = false;
    private volatile String currency;

    private volatile IDetailMode mode;
    private String cancelTempOrderId = null;

    public GooglePayClient(Context context) {
        this.client = BillingClient.newBuilder(context)
                .setListener(this)
                .enablePendingPurchases(PendingPurchasesParams.newBuilder().enableOneTimeProducts().build())
                .build();
        currency = PrefUtil.getInstance().getString(KEY_CURRENCY, "");
        GooglePayLog.d("current currency: {}", currency);
    }

    @Override
    public void onPurchasesUpdated(@NonNull BillingResult billingResult, @Nullable List<Purchase> list) {
        GooglePayLog.log("onPurchasesUpdated, result: {}, {}, listNotNull={}", billingResult.getResponseCode(), billingResult.getDebugMessage(), list);
        if (!TextUtils.isEmpty(cancelTempOrderId)) {
            GooglePayUtil.trackPay(cancelTempOrderId, billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK ?
                    null : "pay fail,code:" + billingResult.getResponseCode() + " msg:" + billingResult.getDebugMessage());
        }
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && list != null) {
            for (Purchase purchase : list) {
                handlePurchase(purchase, TAG_BUY);
            }
            cancelTempOrderId = "";
        } else {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
                invokeCallbackFailed(-1, R.string.ggp_user_cancelled);
                cancelOrderPay();
            } else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED) {
                if (list != null) {
                    for (Purchase purchase : list) {
                        handlePurchase(purchase, TAG_BUY);
                    }
                } else {
                    checkUnhandledPurchase(true);
                    invokeCallbackFailed(-2, ResUtil.getStr(R.string.ggp_error_code, billingResult.getResponseCode()));
                    ToastUtil.show(R.string.ggp_check_un_finished_order);
                }
            } else {
                invokeCallbackFailed(-2, ResUtil.getStr(R.string.ggp_error_code, billingResult.getResponseCode()));
            }
        }
    }

    private void handlePurchase(Purchase purchase, String tag) {
        GooglePayLog.log("handlePurchase, orderId: {}, real state: {}, skuList: {}, tag: {}, ack: {}, oriJson: {}",
                purchase.getOrderId(), purchase.getPurchaseState(), purchase.getProducts(), tag, purchase.isAcknowledged(), purchase.getOriginalJson());
        if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED || purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
            AccountIdentifiers identifiers = purchase.getAccountIdentifiers();
            if (identifiers == null) {
                GooglePayLog.log("handlePurchase, error no wp order id: {}", purchase.getOrderId());
                invokeCallbackFailed(-1, R.string.ggp_error_wp_order_non);
                return;
            }
            String userId = identifiers.getObfuscatedAccountId();
            String wpOrderId = identifiers.getObfuscatedProfileId();

            if (!TAG_RESTORE.equals(tag)) {
                if (TextUtils.isEmpty(wpOrderId)) {
                    wpOrderId = PrefUserUtil.getInstance().getString(KEY_LAST_WP_ORDER_ID, "");
                    GooglePayLog.log("handlePurchase wp order id empty and use sp wp order id: {}", wpOrderId);
                }
            } else {
                if (!String.valueOf(LoginHelper.getLoginUid()).equals(userId)) {
                    //环境切换需校验UID
                    return;
                }
            }

            final String uid = userId;
            final String oid = wpOrderId;
            getPurchaseSkuInfo(getPurchaseSku(purchase), details -> {
                String token = purchase.getPurchaseToken();
                BigDecimal price = new BigDecimal(details.getPriceAmountMicros());
                BigDecimal unit = new BigDecimal(1000_000);
                String priceStr = price.divide(unit, 2, RoundingMode.CEILING).toPlainString();
                String currency = details.getCurrencyCode();
                GooglePayLog.log("handlePurchase, consumeServer!");
                GooglePlayOrderApi.consumeServer(oid, getPurchaseSku(purchase), token, priceStr, currency,
                        new com.three.http.callback.DataCallback<Object>() {
                            @SuppressLint("ApplySharedPref")
                            @Override
                            public void onSuccess(Result<Object> result) {
                                if (!TAG_RESTORE.equals(tag)) {
                                    GooglePayLog.log("consumeServer gpOrderId:{}, wpOrderId:{}, tag: {}, uid:{}, success", purchase.getOrderId(), oid, tag, uid);
                                    PrefUserUtil.getInstance().setString(KEY_LAST_WP_ORDER_ID, "");
                                    consumePurchase(purchase, oid, tag);
                                } else {
                                    GooglePayLog.log("consumeServer restore ok {}", purchase.getOrderId());
                                }
                            }

                            @Override
                            public void onFail(int code, String msg) {
                                GooglePayLog.log("consumeServer gpOrderId:{}, wpOrderId:{}, tag: {}, uid:{}, failed, code: {}, msg: {} ", purchase.getOrderId(), oid, tag, uid, code, msg);
                                if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING && TAG_BUY == tag) {
                                    ToastUtil.show(R.string.ggp_waiting_pay_tip);
                                    invokeCallbackFailed(code, "");
                                } else if (purchase.getPurchaseState() != Purchase.PurchaseState.PENDING) {
                                    invokeCallbackFailed(code, msg);
                                }
                            }
                        });
            });
        } else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
            GooglePayLog.log("purchase pending, {}", purchase.getOrderId());
            ToastUtil.show(R.string.ggp_waiting_pay_tip);
        } else {
            GooglePayLog.log("purchase state unknown, {}", purchase.getOrderId());
        }
    }

    private void getPurchaseSkuInfo(String sku, DataCallback<IDetails> callback) {
        IDetails skuDetails = skuMap.get(sku);
        if (skuDetails != null) {
            callback.onCall(skuDetails);
            return;
        }
        if (!client.isReady() || mode == null) {
            invokeCallbackFailed(-1, R.string.ggp_connect_error);
            return;
        }

        mode.querySkuDetailsAsync(Collections.singletonList(sku), BillingClient.SkuType.INAPP, new DataCallback<List<IDetails>>() {
            @Override
            public void onCall(List<IDetails> list) {
                if (CollectionUtils.isEmpty(list)) {
                    invokeCallbackFailed(-4, R.string.ggp_query_sku_failed);
                    return;
                }
                IDetails details = list.get(0);
                updateSkuMap(details);
                callback.onCall(list.get(0));
            }

            @Override
            public void onFailed(int code, String msg) {
                invokeCallbackFailed(-4, R.string.ggp_query_sku_failed);
            }
        });
    }

    private void consumePurchase(Purchase purchase, String wpOrderId, String tag) {
        ConsumeParams consumeParams = ConsumeParams.newBuilder()
                .setPurchaseToken(purchase.getPurchaseToken())
                .build();
        ConsumeResponseListener listener = (billingResult, purchaseToken) -> {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                if (TAG_NO_HANDLE.equals(tag) || TAG_RESTORE.equals(tag)) {
                    ToastUtil.show(R.string.ggp_un_finished_order_fixed);
                }
            }
            GooglePayLog.log("consumePurchase: {}, tag = {}, msg: {}, skuList: {}", billingResult.getResponseCode(), tag, billingResult.getDebugMessage(), getPurchaseSkuDesc(purchase));
            IapApi.PayResult result = new IapApi.PayResult(wpOrderId, getPurchaseSku(purchase));
            result.msg = ResUtil.getStr(R.string.ggp_success);
            invokeCallbackSuccess(result);
        };

        client.consumeAsync(consumeParams, listener);
    }

    private String getPurchaseSku(Purchase purchase) {
        List<String> list = purchase.getProducts();
        if (list.isEmpty()) {
            return "";
        }
        return list.get(0);
    }

    private String getPurchaseSkuDesc(Purchase purchase) {
        List<String> list = purchase.getProducts();
        return list.toString();
    }

    void checkUnhandledPurchase(boolean ignoreTime) {
        checkUnhandledPurchase(ignoreTime, false);
    }

    void checkUnhandledPurchase(boolean ignoreTime, boolean lessLog) {
        long start = SystemClock.elapsedRealtime();
        if (!connected) {
            if (!lessLog) {
                GooglePayLog.d("checkUnhandledPurchase not connected");
            }
            return;
        }
        if (!ignoreTime) {
            if (SystemClock.elapsedRealtime() - lastCheckUnHandlePurchaseTime < 30_000L) {
                if (!lessLog) {
                    GooglePayLog.d("checkUnhandledPurchase too quick {}", SystemClock.elapsedRealtime() - lastCheckUnHandlePurchaseTime);
                }
                return;
            }
        }
        lastCheckUnHandlePurchaseTime = SystemClock.elapsedRealtime();
        QueryPurchasesParams params = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .build();
        client.queryPurchasesAsync(params, (result, list) -> handler.post(() -> {
            long end = SystemClock.elapsedRealtime();
            HLog.d("", "spent time {} to query", end - start);
            if (result.getResponseCode() != BillingClient.BillingResponseCode.OK) {
                GooglePayLog.log("checkUnhandledPurchase getResponseCode err {}, {}", result.getResponseCode(), result.getDebugMessage());
                return;
            }
            if (list.isEmpty()) {
                if (!lessLog) {
                    GooglePayLog.d("checkUnhandledPurchase list null or empty: {}", list);
                }
                return;
            }
            StringBuilder pendingOrder = new StringBuilder();
            for (Purchase purchase : list) {
                if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED
                        || purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                    handlePurchase(purchase, TAG_NO_HANDLE);
                } else {
                    pendingOrder.append(purchase.getOriginalJson())
                            .append("  ")
                            .append(purchase.getPurchaseState())
                            .append("\n");
                }
            }
            if (pendingOrder.length() > 0) {
                GooglePayLog.log("un-handled order: {}", pendingOrder);
            }
        }));
    }

    private void updateCurrentCurrency() {
        if (!TextUtils.isEmpty(currency) || mode == null) {
            return;
        }
        mode.querySkuDetailsAsync(Arrays.asList("coin600", "coin1800", "coin3000", "coin10000", "coin50000"),
                BillingClient.ProductType.INAPP, new DataCallback<List<IDetails>>() {
                    @Override
                    public void onCall(List<IDetails> list) {
                        if (list == null || list.isEmpty()) {
                            GooglePayLog.log("default sku empty");
                        } else {
                            IDetails details = list.get(0);
                            currency = details.getCurrencyCode();
                            PrefUtil.getInstance().setString(KEY_CURRENCY, currency);
                            for (IDetails detail : list) {
                                updateSkuMap(detail);
                            }
                        }
                    }

                    @Override
                    public void onFailed(int code, String msg) {
                        handler.postDelayed(GooglePayClient.this::updateCurrentCurrency, 1000);
                    }
                });
    }

    private void updateSkuMap(IDetails skuDetails) {
        skuMap.put(skuDetails.getId(), skuDetails);
        GooglePayLog.d("update sku map: {}", skuDetails.getId());
    }

    public String getCurrency() {
        return currency;
    }

    private long startConnectTime = 0;
    private final Runnable connectTooLongRunner = () -> GooglePayLog.log("connect too long up to: {}", (SystemClock.elapsedRealtime() - startConnectTime));

    void connect() {
        startConnectTime = SystemClock.elapsedRealtime();
        handler.postDelayed(connectTooLongRunner, 10_000);
        handler.removeMessages(RECONNECT_WHAT);
        try {
            client.startConnection(new BillingClientStateListener() {
                @Override
                public void onBillingSetupFinished(@NonNull BillingResult billingResult) {
                    handler.removeCallbacks(connectTooLongRunner);
                    connectionCode = billingResult.getResponseCode();
                    connectionMsg = billingResult.getDebugMessage();
                    boolean flag = connectionCode == BillingClient.BillingResponseCode.OK;
                    if (!flag) {
                        GooglePayLog.log("onBillingSetupFinished: {}, {}", billingResult.getResponseCode(), billingResult.getDebugMessage());
                    }
                    if (flag) {
                        connected = true;
                        billingReconnectCount = 0;
                        uploadProductDetailsSupportInfoWithCheck();
                        if (mode == null) {
                            BillingResult result = client.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS);
                            if (result.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                                mode = new ProductDetailMode(client);
                            } else {
                                mode = new SkuDetailMode(client);
                            }
                            skuMap.clear();
                            GooglePayLog.d("support product api:{} {}", result.getResponseCode(), result.getDebugMessage());
                        }
                        updateCurrentCurrency();
                        checkUnhandledPurchase(false);
                    } else if (connectionCode == BillingClient.BillingResponseCode.SERVICE_DISCONNECTED) {
                        connected = false;
                        handleReconnect(10 * DateUtils.MINUTE_IN_MILLIS);
                    } else if (connectionCode == BillingClient.BillingResponseCode.BILLING_UNAVAILABLE) {
                        connected = false;
                        handleReconnect(10 * DateUtils.MINUTE_IN_MILLIS);
                    } else if (connectionCode == BillingClient.BillingResponseCode.DEVELOPER_ERROR && client.isReady()) {
                        connected = true;
                        updateCurrentCurrency();
                        checkUnhandledPurchase(false);
                    } else {
                        connected = false;
                        handleReconnect(10 * DateUtils.MINUTE_IN_MILLIS);
                    }
                }

                @Override
                public void onBillingServiceDisconnected() {
                    GooglePayLog.log("onBillingServiceDisconnected");
                    handler.removeCallbacks(connectTooLongRunner);
                    connected = false;
                    handleReconnect(1000);
                }
            });
        } catch (IllegalStateException exception) {
            GooglePayLog.log("onBillingConnectIllegalStateException: {}", exception.getMessage());
        }
    }

    private void handleReconnect(long delayMillis) {
        billingReconnectCount++;
        if (billingReconnectCount <= maxReconnectCount) {
            handler.sendEmptyMessageDelayed(RECONNECT_WHAT, delayMillis);
        }
    }

    public void doGooglePay(Activity activity, WespyGoods wespyGoods, Map<String, String> extParam, DataCallback<IapApi.PayResult> callback) {
        if (connectionCode != BillingClient.BillingResponseCode.OK) {
            if (callback != null) {
                callback.onFailed(-2, ResUtil.getStr(R.string.ggp_connect_code_error, connectionCode, connectionMsg));
            }
            connect();
            GooglePayUtil.track("connect", wespyGoods, "GMS connect failed:" + connectionCode);
            return;
        }
        if (!connected || mode == null) {
            if (callback != null) {
                callback.onFailed(-2, ResUtil.getStr(R.string.ggp_connect_error));
            }
            GooglePayUtil.track("connect", wespyGoods, "GMS is disconnect");
            return;
        }
        if (callback != null) {
            synchronized (callbackList) {
                callbackList.add(new SoftReference<>(callback));
            }
        }
        GooglePayUtil.track("connect", wespyGoods);
        logBillingConfig();
        querySkuDetails(activity, wespyGoods, extParam);
    }


    private void logBillingConfig() {
        BillingClient client = this.client;
        if (client == null) {
            return;
        }
        GetBillingConfigParams getBillingConfigParams = GetBillingConfigParams.newBuilder().build();
        client.getBillingConfigAsync(getBillingConfigParams, (billingResult, billingConfig) -> {
            JsonObject res = new JsonObject();
            if (null != billingConfig) {
                res.addProperty("countryCode", billingConfig.getCountryCode());
            }
            res.addProperty("connection", connectionMsg);
            GooglePayLog.log("getBillingConfigAsync! billingResult={},  res={}", billingResult, res);
        });
    }

    private void querySkuDetails(Activity activity, WespyGoods goods, Map<String, String> extParam) {
        SoftReference<Activity> activitySoftReference = new SoftReference<>(activity);
        mode.querySkuDetailsAsync(Collections.singletonList(goods.gpProductId), goods.isGpInApp() ? BillingClient.ProductType.INAPP : BillingClient.ProductType.SUBS, new DataCallback<List<IDetails>>() {
            @Override
            public void onCall(List<IDetails> list) {
                if (CollectionUtils.isEmpty(list)) {
                    onFailed();
                    return;
                }
                IDetails skuDetails = list.get(0);
                GooglePayLog.log("query sku info success: {}, price: {}, currency: {}", skuDetails.getId(), skuDetails.getPrice(), skuDetails.getCurrencyCode());
                currency = skuDetails.getCurrencyCode();
                PrefUtil.getInstance().setString(KEY_CURRENCY, currency);
                updateSkuMap(skuDetails);
                createWPOrder(activitySoftReference, goods, extParam, skuDetails);
            }

            @Override
            public void onFailed(int code, String msg) {
                onFailed();
            }

            private void onFailed() {
                GooglePayUtil.track("create_order", goods, "query GMS's sku failed");
                invokeCallbackFailed(-4, R.string.ggp_query_sku_failed);
            }
        });
    }


    private void createWPOrder(SoftReference<Activity> softReference, WespyGoods goods, Map<String, String> extParam, IDetails skuDetails) {
        GooglePlayOrderApi.createGpOrder(goods, currency, extParam, new com.three.http.callback.DataCallback<OrderInfo>() {

            @Override
            public void onSuccess(Result<OrderInfo> result) {
                if (result.data != null) {
                    GooglePayLog.log("create wp order: {}, for goods: {}, {}", result.data.orderId, goods.goods_id, skuDetails.getId());
                    cancelTempOrderId = result.data.orderId;
                    IapApi iapApi = ApiService.of(IapApi.class);
                    Map<String, Object> trackEventParam = iapApi.getTrackEventParamCopy();
                    iapApi.clearTrackEventParam();
                    // 订单创建完毕后上传数据，该数据是调用showPay时传入的，即click对应的埋点数据，需要稍作修改
                    if (!trackEventParam.isEmpty()) {
                        trackEventParam.remove("btn_name");
                        trackEventParam.remove("btn_pos");
                        trackEventParam.put("order_id", result.data.orderId);
                        trackEventParam.put("goods_id", goods.goods_id);
                    }
                    TrackUtil.trackEvent(TrackEvent.CREATE_ORDER, trackEventParam);
                    startPay(softReference, skuDetails, goods, result.data);
                } else {
                    GooglePayLog.log("create wp order internal error: {}", goods.goods_id);
                    invokeCallbackFailed(-5, R.string.ggp_error_wp_order_non);
                    GooglePayUtil.track("create_order", goods, "create order fail:result is null");
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                GooglePayUtil.track("create_order", goods, "create order fail:" + msg);
                invokeCallbackFailed(code, msg);
            }
        });
    }


    @SuppressLint("ApplySharedPref")
    private void startPay(SoftReference<Activity> softReference, IDetails details, WespyGoods goods, OrderInfo orderInfo) {
        if (!connected || mode == null || softReference.get() == null) {
            GooglePayLog.log("google play disconnected, order cancel, {}", orderInfo.orderId);
            invokeCallbackFailed(-1, R.string.ggp_connect_error);
            GooglePayUtil.track("create_order", goods, orderInfo, "google play disconnected or order cancel");
            return;
        }
        String uid = String.valueOf(UserService.get().getLoginUid());
        String wpOrderId = orderInfo.orderId;
        PrefUserUtil.getInstance().setString(KEY_LAST_WP_ORDER_ID, wpOrderId);
        BillingResult result = mode.launchBillingFlow(softReference.get(), uid, wpOrderId, details);
        int rspCode = result.getResponseCode();
        GooglePayLog.log("launch billing flow for {}, wpOrderId:{}, res: {}, msg:{}", details.getId(), wpOrderId, rspCode, result.getDebugMessage());
        if (rspCode != BillingClient.BillingResponseCode.OK) {
            invokeCallbackFailed(-4, R.string.ggp_launch_error);
            GooglePayUtil.track("create_order", goods, orderInfo, "launch billing fail,reason:" + rspCode + " " + result.getDebugMessage());
        } else {
            GooglePayUtil.track("create_order", goods, orderInfo);
        }
    }

    @SuppressLint("ApplySharedPref")
    private void cancelOrderPay() {
        GooglePayLog.log("cancelOrderPay, orderId: {}", cancelTempOrderId);
        if (!TextUtils.isEmpty(cancelTempOrderId)) {
            GooglePlayOrderApi.cancelOrder(cancelTempOrderId, new EmptyDataCallback("GooglePay"));
            cancelTempOrderId = "";
        }
        PrefUserUtil.getInstance().setString(KEY_LAST_WP_ORDER_ID, "");
    }

    void invokeCallbackSuccess(IapApi.PayResult result) {
        handler.post(() -> {
            synchronized (callbackList) {
                Iterator<SoftReference<DataCallback<IapApi.PayResult>>> refIte = callbackList.iterator();
                while (refIte.hasNext()) {
                    SoftReference<DataCallback<IapApi.PayResult>> callbackSoftReference = refIte.next();
                    DataCallback<IapApi.PayResult> callback = callbackSoftReference.get();
                    if (callback != null) {
                        callback.onCall(result);
                    }
                    refIte.remove();
                }
            }
        });
    }

    void invokeCallbackFailed(int code, String msg) {
        GooglePayLog.log("invokeCallbackFailed! code={},msg={}", code, msg);
        handler.post(() -> {
            synchronized (callbackList) {
                Iterator<SoftReference<DataCallback<IapApi.PayResult>>> refIte = callbackList.iterator();
                while (refIte.hasNext()) {
                    SoftReference<DataCallback<IapApi.PayResult>> callbackSoftReference = refIte.next();
                    DataCallback<IapApi.PayResult> callback = callbackSoftReference.get();
                    if (callback != null) {
                        callback.onFailed(code, msg);
                    }
                    refIte.remove();
                }
            }
        });
    }

    void invokeCallbackFailed(int code, @StringRes int msg) {
        invokeCallbackFailed(code, ResUtil.getStr(msg));
    }

    void tryRestore() {
        if (!client.isReady()) {
            ToastUtil.show(R.string.ggp_connect_error);
            return;
        }
        QueryPurchasesParams params = QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.INAPP).build();
        client.queryPurchasesAsync(params, (billingResult, list) -> {
            if (billingResult.getResponseCode() != BillingClient.BillingResponseCode.OK) {
                GooglePayLog.log("tryRestore, error {}, {}", billingResult.getResponseCode(), billingResult.getDebugMessage());
                ToastUtil.show(R.string.ggp_query_history_failed);
                return;
            }
            if (list.isEmpty()) {
                GooglePayLog.log("tryRestore, history empty");
                ToastUtil.show(R.string.ggp_query_history_empty);
                return;
            }
            int count = list.size();
            if (count > 5) {
                count = 5;
            }
            ToastUtil.show(R.string.ggp_sync_history);
            GooglePayLog.log("tryRestore, history info: {}", list);
            for (int i = 0; i < count; i++) {
                Purchase purchase = list.get(i);
                handler.postDelayed(() -> handlePurchase(purchase, TAG_RESTORE), 1500L * i);
            }
            handler.postDelayed(() -> checkUnhandledPurchase(true), count * 1500);
        });
    }

    public boolean isConnected() {
        return connected;
    }

    /**
     * 埋点上传当前是否支持ProductDetails功能，connect成功后调用一次
     * 同一台设备上的同一个playStore版本号只上传一次，用SP记录
     */
    private void uploadProductDetailsSupportInfoWithCheck() {
        String playStoreVersion = getPlayStoreVersion();
        if (!PrefUtil.getInstance().getString("last_uploaded_play_store_version", "").equals(playStoreVersion)) {
            uploadProductDetailsSupportInfo(playStoreVersion);
            PrefUtil.getInstance().setString("last_uploaded_play_store_version", playStoreVersion);
        }
    }

    private void uploadProductDetailsSupportInfo(String playStoreVersion) {
        Map<String, String> map = new HashMap<>();
        Locale locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locale = Resources.getSystem().getConfiguration().getLocales().get(0);
        } else {
            locale = Resources.getSystem().getConfiguration().locale;
        }
        map.put("support_product_details",
                String.valueOf(client.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS).getResponseCode() == BillingClient.BillingResponseCode.OK));
        map.put("play_store_version", playStoreVersion);
        map.put("country", locale.getCountry());
        map.put("language", locale.getLanguage());
        HLog.aliLog(AliNetLogUtil.PORT.playStore, AliNetLogUtil.TYPE.normal, "", map);
    }

    /**
     * @return 正常版本号 或 unknown
     */
    @NonNull
    private String getPlayStoreVersion() {
        if (playStoreVersion != null) {
            return playStoreVersion;
        }
        try {
            PackageManager packageManager = LibBaseUtil.getApplication().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(LibBaseUtil.GOOGLE_PACKAGE_NAME, 0);
            playStoreVersion = packageInfo.versionName;
            GooglePayLog.log("try get playStore version: {}", playStoreVersion);
        } catch (PackageManager.NameNotFoundException e) {
            GooglePayLog.log("try get playStore version failed, NameNotFoundException");
        }
        if (playStoreVersion == null || playStoreVersion.isEmpty()) {
            playStoreVersion = "unknown";
        }
        return playStoreVersion;
    }
}
