package com.huiwan.configservice.editionentity;

import androidx.annotation.IntDef;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * date 2018/4/7
 * email <EMAIL>
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class GameConfig {

    public static final int CURRENCY_COIN = 0; //金币
    public static final int CURRENCY_CHIP = 1; //游戏币
    public static final int RANK_TYPE_EARNING = 1; // 排行榜 赢的游戏币数

    public static final int RANK_TYPE_COLLECTION_MONTHLY= 1; // 排行榜-收藏榜-月榜 棋子收藏值
    public static final int RANK_TYPE_COLLECTION_TOTAL = 2; // 排行榜-收藏榜-总榜 棋子收藏值


    private static final int CATEGORY_LITTLE_GAME = 3;
    private static final int CATEGORY_TABLE_GAME = 2;
    private static final int CATEGORY_UNITY_GAME = 1;

    public static final int MODEL_SUPPORT_TYPE_MATCH = 1; //模式支持匹配
    public static final int MODEL_SUPPORT_TYPE_CREATE = 2; //模式支持建房

    public static final int JACKAROO_MODE_BASIC = 1;
    public static final int JACKAROO_MODE_COMPLEX = 2;
    public static final int JACKAROO_MODE_1V1 = 12;

    public static final int JACKAROO_MODE_VIP_BASIC = 101;
    public static final int JACKAROO_MODE_VIP_COMPLEX = 102;
    public static final int JACKAROO_MODE_VIP_1V1 = 112;

    /**
     * 大于等于该值，则是 vip 队伍及房间
     * 否则是普通值
     */
    public static final int GAME_MODE_VIP_BASE = 100;

    @IntDef({CURRENCY_COIN, CURRENCY_CHIP})
    @Retention(RetentionPolicy.SOURCE)
    public @interface CurrencyType {
    }

    @SerializedName("match_res_url_an")
    private String matchResUrlAn = "";

    @SerializedName("game_type")
    private int gameType = 0;

    @SerializedName("rank_indicate_bg")
    private String rankIndicateBg = "0xFFFFFF";

    @SerializedName("share_url")
    private String shareUrl = "https://weplayapp.com";

    @SerializedName("match_res_url")
    private String matchResUrl = "";

    @SerializedName("can_background_download")
    private boolean canBackgroundDownload;
    @SerializedName("invite_title")
    private String inviteTitle = "";

    @SerializedName("rank_nav_bg_color")
    private String rankNavBgColor = "0xFFFF00";

    @SerializedName("share_desc")
    private String shareDesc = "";

    @SerializedName("guide_image_count")
    private int guideImageCount = 0;

    @SerializedName("room_bottom_bg")
    private String roomBottomBg = "0xFF00FF";

    @SerializedName("rank_indicate_line")
    private String rankIndicateLine = "0x00FFFF";

    @SerializedName("name")
    private String name = "";

    @SerializedName("share_title")
    private String shareTitle = "";

    @SerializedName("rank_rule")
    private String rankRule = ResUtil.getStr(R.string.cocos_rank_help_default_content);

    @SerializedName("invite_desc")
    private String inviteDesc = "";

    @SerializedName("rank_indicate_fore")
    private String rankIndicateFg = "0x0D458B";

    @SerializedName("match_list")
    private List<MatchInfo> matchInfoList = new ArrayList<>();//五子棋类型的布局

    @SerializedName("match_list_v2")
    private List<MatchGroupInfo> matchGroupList = new ArrayList<>();//炸弹猫类型的布局

    @SerializedName("is_little_game")
    private boolean hotUpdate;

    @SerializedName("icon_url")
    private String iconUrl = "";

    @SerializedName("horizontal_screen")
    private boolean horizontalScreen;//是否横屏

    @SerializedName("game_mode")
    private int playMode = PLAY_MODE_PK;// 1对抗类 2合作类 3抢唱

    @SerializedName("rank_indicate_title_normal")
    private String rankIndicateTitleNormal = "#ffffff";

    @SerializedName("share_reward_url")
    private String shareRewardUrl = "https://www.weplayapp.com";

    @SerializedName("short_name")
    private String shortName = "";

    @SerializedName("majiang_tw_create_setting")
    private MJCreateSetting mjCreateSetting;

    @SerializedName("help_url")
    private String helpUrl;

    @SerializedName("record_url")
    private String recordUrl;

    @SerializedName("center_show_bg")
    private List<String> centerShowBg = new ArrayList<>();

    // 游戏类型 1.unity  2.fixroom  3.littleGame  4.other(如麻将)
    @SerializedName("category")
    private int category = 0;

    @SerializedName("is_unity_game")
    public boolean unityGame = false;


    @SerializedName("support_cocos_match")
    private boolean supportCocosMatch = false;

    /**
     * 阿语服2.8.5 新增
     */
    @SerializedName("generate_home")
    public VsCenterLittleGameConfig generateHome = new VsCenterLittleGameConfig();

    public boolean isHorizontalScreen() {
        return horizontalScreen;
    }

    public String getMatchResUrlAn() {
        return matchResUrlAn;
    }

    public boolean isSupportCocosMatch() {
        return supportCocosMatch;
    }

    //    @Override
    public int getGameType() {
        return gameType;
    }

    public String getRankIndicateBg() {
        return rankIndicateBg;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public String getMatchResUrl() {
        return matchResUrl;
    }

    public String getInviteTitle() {
        return inviteTitle;
    }

    public String getRankNavBgColor() {
        return rankNavBgColor;
    }

    public String getShareDesc() {
        return shareDesc;
    }

    public int getGuideImageCount() {
        return guideImageCount;
    }

    public String getRoomBottomBg() {
        return roomBottomBg;
    }

    public String getRankIndicateLine() {
        return rankIndicateLine;
    }

    public String getName() {
        return name;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public String getRankRule() {
        return rankRule;
    }

    public String getInviteDesc() {
        return inviteDesc;
    }

    public String getRankIndicateFg() {
        return rankIndicateFg;
    }

    public List<MatchInfo> getMatchInfoList() {
        return matchInfoList;
    }

    public List<MatchInfo> getMatchInfoListForCreate() {
        List<MatchInfo> createMatchInfos = new ArrayList<>();
        for (int index = 0; index < matchInfoList.size(); index++) {
            MatchInfo matchInfo = matchInfoList.get(index);
            if (matchInfo.supportMode.isEmpty() || matchInfo.supportMode.contains(MODEL_SUPPORT_TYPE_CREATE)) {
                createMatchInfos.add(matchInfo);
            }
        }

        return createMatchInfos;
    }

    public List<MatchInfo> getMatchInfoListForMatch() {
        List<MatchInfo> createMatchInfos = new ArrayList<>();
        for (int index = 0; index < matchInfoList.size(); index++) {
            MatchInfo matchInfo = matchInfoList.get(index);
            if (matchInfo.supportMode.isEmpty() || matchInfo.supportMode.contains(MODEL_SUPPORT_TYPE_MATCH)) {
                createMatchInfos.add(matchInfo);
            }
        }

        return createMatchInfos;
    }

    public MatchInfo getLittleGameMatchInfo(int betLevel, int mode, int gameMode, int currencyType) {
        for (GameConfig.MatchInfo matchInfo : getMatchInfoList()) {
            boolean flag = matchInfo.getBetLevel() == betLevel
                    && (matchInfo.getRoomMode() == mode || matchInfo.getMatchMode() == mode)
                    && matchInfo.getGameMode() == gameMode
                    && matchInfo.getCurrencyType() == currencyType;
            if (flag) {
                return matchInfo;
            }
        }
        return null;
    }

    public boolean isHotUpdate() {
        return hotUpdate;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public MJCreateSetting getMjCreateSetting() {
        return mjCreateSetting;
    }

    public static class MatchInfo implements ILittleGameMatchInfo {

        @SerializedName("img_url")
        private String imgUrl = "";

        @SerializedName("title")
        private String title = "";

        @SerializedName("bet_level")
        private int betLevel = 1;

        @SerializedName("coin")
        private int coin = 10;

        @SerializedName("room_mode")
        private int roomMode = 4;

        @SerializedName("coin_desc")
        private String coinDesc = "";

        @SerializedName("match_mode")
        private int matchMode = 1;

        @SerializedName("game_mode")
        private int gameMode = 0;

        @SerializedName("max_coin_limit")
        private int maxCoinLimit = Integer.MAX_VALUE;

        @SerializedName("bottom_score")
        private int score = 0; // 底注, baloot 使用

        /**
         * 0表示金币结算，1表示游戏币结算
         */
        @SerializedName("game_currency_type")
        @CurrencyType
        private int currencyType = CURRENCY_COIN;
        @SerializedName("majiang_tw_match_setting")
        private MJMatchSetting mjMatchSetting;

        @SerializedName("support_mode")
        private List<Integer> supportMode = new ArrayList<>();

        @SerializedName("exp_multiplier")
        private String expMultiply = "";

        public String getImgUrl() {
            return imgUrl;
        }

        public String getTitle() {
            return title;
        }

        public String getExpMultiply() {
            return expMultiply;
        }

        @Override
        public int getBetLevel() {
            return betLevel;
        }

        @Override
        public boolean hasCocosGuide() {
            return false;
        }

        public int getCoin() {
            return coin;
        }

        @Override
        public int getRoomMode() {
            return roomMode;
        }

        public String getCoinDesc() {
            return coinDesc;
        }

        @Override
        public int getGameMode() {
            return gameMode;
        }

        public boolean isVipGameMode() {
            return gameMode >= GAME_MODE_VIP_BASE;
        }

        @Override
        public int getMatchMode() {
            return matchMode;
        }

        @Override
        public int getCurrencyType() {
            return currencyType;
        }

        public MJMatchSetting getMjMatchSetting() {
            return mjMatchSetting;
        }

        public List<Integer> getSupportMode() {
            return supportMode;
        }

        /**
         * 本地添加参数，用于记录当前信息的group标题
         */
        private String groupTitle = "";

        public void setGroupTitle(String groupTitle) {
            this.groupTitle = groupTitle;
        }

        public String getGroupTitle() {
            return groupTitle;
        }

        /**
         * 本地添加参数，用于记录当前是从创建还是匹配入口
         */
        private Boolean isCreate = false;

        @Override
        public Boolean getIsCreate() {
            return isCreate;
        }

        @Override
        public void setIsCreate(Boolean isCreateScene) {
            isCreate = isCreateScene;
        }

        public boolean isSupportCreate() {
            if (supportMode == null || supportMode.isEmpty()) {
                return true;
            }

            return supportMode.contains(MODEL_SUPPORT_TYPE_CREATE);
        }

        public int getId() {
            String builder = betLevel + "_" + roomMode + "_" +
                    currencyType + "_" + gameMode;
            return builder.hashCode();
        }

        public int getMaxCoinLimit() {
            return maxCoinLimit;
        }

        public int getScore() {
            return score;
        }
    }

    public static class MatchGroupInfo {
        @SerializedName("title")
        private String title = "";
        @SerializedName("sub_title")
        private String subTitle = "";
        @SerializedName("title_color")
        private String titleColor = "";
        @SerializedName("sub_title_color")
        private String subTitleColor = "";
        @SerializedName("mode_list")
        private List<MatchInfo> matchInfoList = new ArrayList<>();
        @SerializedName("button_url")
        private String buttonUrl = "";
        @SerializedName("guide_urls")
        private List<String> guideUrlList = new ArrayList<>();
        @SerializedName("support_mode")
        private List<Integer> supportMode = new ArrayList<>();

        public String getTitle() {
            return title;
        }

        public String getSubTitle() {
            return subTitle;
        }

        public String getTitleColor() {
            return titleColor;
        }

        public String getSubTitleColor() {
            return subTitleColor;
        }

        public List<MatchInfo> getMatchInfoList() {
            return matchInfoList;
        }

        public List<MatchInfo> getMatchInfoListForCreate() {
            List<MatchInfo> createMatchInfos = new ArrayList<>();
            for (int index = 0; index < matchInfoList.size(); index++) {
                MatchInfo matchInfo = matchInfoList.get(index);
                matchInfo.setGroupTitle(getTitle());
                if (matchInfo.supportMode.isEmpty() || matchInfo.supportMode.contains(MODEL_SUPPORT_TYPE_CREATE)) {
                    createMatchInfos.add(matchInfo);
                }
            }

            return createMatchInfos;
        }

        public List<MatchInfo> getMatchInfoListForMatch() {
            List<MatchInfo> createMatchInfos = new ArrayList<>();
            for (int index = 0; index < matchInfoList.size(); index++) {
                MatchInfo matchInfo = matchInfoList.get(index);
                if (matchInfo.supportMode.isEmpty() || matchInfo.supportMode.contains(MODEL_SUPPORT_TYPE_MATCH)) {
                    createMatchInfos.add(matchInfo);
                }
            }

            return createMatchInfos;
        }

        public String getButtonUrl() {
            return buttonUrl;
        }

        public List<String> getGuideUrlList() {
            return guideUrlList;
        }

        public List<Integer> getSupportMode() {
            return supportMode;
        }
    }

    public static final int PLAY_MODE_PK = 1;
    public static final int PLAY_MODE_COOPERATE = 2;
    public static final int PLAY_MODE_SING = 3;

    public int getPlayMode() {
        return playMode;
    }

    public String getRankIndicateTitleNormal() {
        return rankIndicateTitleNormal;
    }

    public String getShareRewardUrl() {
        return shareRewardUrl;
    }

    public List<String> getCenterShowBg() {
        return centerShowBg;
    }

    public String getShortName() {
        return shortName;
    }

    public List<MatchGroupInfo> getMatchGroupList() {
        return matchGroupList;
    }

    public String getHelpUrl() {
        return helpUrl;
    }

    public void setHelpUrl(String helpUrl) {
        this.helpUrl = helpUrl;
    }

    public String getRecordUrl() {
        return recordUrl;
    }

    public void setRecordUrl(String recordUrl) {
        this.recordUrl = recordUrl;
    }

    public boolean isLittleGame() {
        return category == CATEGORY_LITTLE_GAME;
    }

    public boolean isUnityGame() {
        return category == CATEGORY_UNITY_GAME;
    }

    public boolean isTableGame() {
        return category == CATEGORY_TABLE_GAME;
    }

    public boolean isCanBackgroundDownload() {
        return canBackgroundDownload;
    }

    public void setCanBackgroundDownload(boolean canBackgroundDownload) {
        this.canBackgroundDownload = canBackgroundDownload;
    }

    public static boolean isJackarooVip(int gameType, int gameMode) {
        return gameMode >= GAME_MODE_VIP_BASE;
    }
}
