package com.huiwan.configservice.editionentity;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.modelAbTest.AbTest;
import com.huiwan.configservice.modelAbTest.ClientExtraConfig;

// Created by bigwen on 2018/9/19.
public class AbTestConfig {

    @SerializedName("version_num")
    public int version;
    @SerializedName("md5_version")
    private String versionMD5 = "";
    @SerializedName("abtest")
    public AbTest abTest = new AbTest();
    @SerializedName("client_extra_config")
    public ClientExtraConfig extraConfig = new ClientExtraConfig();

    public int getVersion() {
        return version;
    }

    public String getVersionMD5(){
        return versionMD5;
    }

    // 以下方法直接从华语服复制过来，jk暂时用不到

    public boolean getBooleanFromSwitch(String key) {
        return getBooleanFromSwitch(key, false);
    }

    public boolean getBooleanFromSwitch(String key, boolean defaultValue) {
        if (extraConfig == null) {
            return defaultValue;
        }

        String s = extraConfig.getClientSwitch().get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Boolean.parseBoolean(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }

    public int getIntFromSwitch(String key) {
        return getIntFromSwitch(key, 0);
    }

    public int getIntFromSwitch(String key, int defaultValue) {
        if (extraConfig == null) {
            return defaultValue;
        }

        String s = extraConfig.getClientSwitch().get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Integer.parseInt(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }

    public long getLongFromSwitch(String key) {
        return getLongFromSwitch(key, 0L);
    }

    public long getLongFromSwitch(String key, long defaultValue) {
        if (extraConfig == null) {
            return defaultValue;
        }

        String s = extraConfig.getClientSwitch().get(key);
        if (!TextUtils.isEmpty(s)) {
            try {
                return Long.parseLong(s);
            } catch (Exception e) {
                //ignore
            }
        }
        return defaultValue;
    }
}
