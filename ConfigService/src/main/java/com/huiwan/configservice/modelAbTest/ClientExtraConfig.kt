package com.huiwan.configservice.modelAbTest

import com.google.gson.annotations.SerializedName


/**
 * 从华语服复制过来的
 */
data class ClientExtraConfig(
    /**
     * 修复vap在三星部分机型上的兼容性问题
     * 是否在detach后停止eglSwapBuffers调用
     */
    @SerializedName("disable_swap_buffers")
    val disableEglSwapBuffersWhenDetach: Boolean = false,

    @SerializedName("android_client_switch")
    val clientSwitch: MutableMap<String, String> = mutableMapOf(),
    /**
     * 是否开启单进程数据库
     */
    @SerializedName("enable_single_db")
    val enableSingleDb: Boolean = false,
    /**
     * 是否替换三方的SP为MMKV
     */
    @SerializedName("use_third_sp_mmkv")
    val useThirdSpMMKV: Boolean = false,

    /**
     * 游玩卡flutter开关，默认策略为关闭（即使用原生）
     */
    @SerializedName("funcard_flutter_switch")
    val funCardFlutterSwitch: Boolean = false,

    // 上面的属性是华语服复制过来的，jk暂时用不到

    /**
     * 是否启用cocos游戏资源预加载
     */
    @SerializedName("enable_preload_cocos_asset")
    val enablePreloadCocosAsset: Boolean = false,

    /**
     * 是否启用cocosWebView的cache功能
     */
    @SerializedName("enable_cocos_wev_view_cache")
    val enableCocosWebViewCache: Boolean = false
)
