import com.google.gson.annotations.SerializedName
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.serialization.gson.gson
import kotlinx.coroutines.runBlocking
import org.w3c.dom.Element
import org.w3c.dom.NodeList
import java.io.BufferedReader
import java.io.InputStreamReader
import java.text.DateFormat
import javax.xml.parsers.DocumentBuilderFactory
import kotlin.collections.component1
import kotlin.collections.component2
import kotlin.collections.set

apply(from = "../base_lib.gradle")

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        //noinspection UseTomlInstead
        classpath("io.ktor:ktor-client-okhttp:3.0.0")
        classpath("io.ktor:ktor-serialization-gson:3.0.0")
        classpath("io.ktor:ktor-client-content-negotiation:3.0.0")
        classpath("org.apache.logging.log4j:log4j-slf4j-impl:2.14.1")
    }
}

dependencies {
//Update current-version translation to maven, Consider using it later
//    api(libs.wejoy.language)
}

val fileId = rootProject.ext.get("TRANSLATION_ID")
tasks.register("downloadTranslation") {
    group = "pan"

    fun loadXmlEntity(path: File): Language {
        val documentBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder()
        val language = Language(path.absolutePath)
        if (path.exists()) {
            val document = documentBuilder.parse(path.inputStream())
            language.addStringNode(document.getElementsByTagName("string"))
            language.addArrayNode(document.getElementsByTagName("string-array"))
        }
        return language
    }

    fun getCurrentGitBranch(): String {
        val process = ProcessBuilder("git", "rev-parse", "--abbrev-ref", "HEAD")
            .redirectErrorStream(true)
            .start()

        BufferedReader(InputStreamReader(process.inputStream)).use { reader ->
            return reader.readLine().trim()
        }
    }

    fun getFormatList(s: String): List<Pair<String, String>> {
        val list = mutableListOf<Pair<String, String>>()
        var i = 1
        var startIndex = s.indexOf("{#")
        do {
            val endIndex = s.indexOf("#}", startIndex)
            if (endIndex < 0) {
                return list
            }
            val format = s.substring(startIndex, endIndex + 2)
            val newFormat = "%$i\$s"
            list.add(Pair(format, newFormat))
            startIndex = s.indexOf("{#", endIndex + 2)
            i++
        } while (startIndex >= 0)
        return list
    }

    doLast {
        val branchName = getCurrentGitBranch()
        println("Current Git branch: $branchName")
        if (!branchName.contains("-dev-")) {
            println("Please use branch name like 'xxx-dev-xxx'")
            return@doLast
        }
        val patch: Languages =
            HTTPClient.post("https://translate.weplayapp.com/api/public/export_trans_multi?file_id=$fileId&file_type=5&operator_email=panrunqiu%40wepie.com")
        val hans = patch.chs
        val formatMap = mutableMapOf<String, List<Pair<String, String>>>()
        hans.forEach { (k, v) ->
            if (v is String && v.contains("{#")) {
                formatMap[k] = getFormatList(v)
            }
        }

        //使用反射获取patch的每个字段，并获取AndroidFileName注解内容
        patch::class.java.declaredFields.filter { !it.name.contains("\$") }.map {
            it.isAccessible = true
            val annotation = it.getAnnotation(AndroidFileName::class.java)?:throw Exception("no annotation")
            val value = it.get(patch) as Language
            annotation.value to value
        }.plus("" to patch.en).forEach { (name, value) ->
            println(name)
            val ori = loadXmlEntity(project.file("build/generated/res/resValues/debug/values${name}/strings.xml").apply { parentFile?.mkdirs() })
            value.forEach { (k, v) ->
                if (v is String) {
                    var tmp = v as String
                    formatMap[k]?.forEach {
                        tmp = tmp.replace(it.first, it.second)
                    }
                    ori.put(k, tmp)
                } else {
                    ori.put(k, v)
                }
            }
            ori.save()
        }
        println(project.file("build/generated/res/resValues/debug/values/strings.xml").readText())
    }
}

class Languages(
    @AndroidFileName("-ar")
    @SerializedName("ar")
    val ar: Language = Language(),
    @AndroidFileName("-en")
    @SerializedName("en")
    val en: Language = Language(),
    @AndroidFileName("-hi")
    @SerializedName("hi")
    val hi: Language = Language(),
    @AndroidFileName("-tr")
    @SerializedName("tr")
    val tr: Language = Language(),
    @AndroidFileName("-zh")
    @SerializedName("zh-Hans")
    val chs: Language = Language(),
)

class HTTPClient {
    companion object {
        val client = HttpClient(OkHttp) {
            engine {
                config {
                    readTimeout(60, TimeUnit.SECONDS)
                }
            }
            install(ContentNegotiation) {
                gson {
                    setDateFormat(DateFormat.LONG)
                    setPrettyPrinting()
                }
            }
        }

        fun HttpRequestBuilder.headers(headers: Map<String, String>) {
            headers.forEach { header(it.key, it.value) }
        }

        inline fun<reified T> post(url: String, headers: Map<String, String> = emptyMap()): T = runBlocking {
            client.post(url) {
//                contentType(ContentType.Application.Json)
//                headers(headers)
            }.body()
        }
    }
}

class Language(private val mPath: String = "") : MutableMap<String, Any> by HashMap() {
    fun addStringNode(elementsByTagName: NodeList) {
        putAll(elementsByTagName.toMap())
    }

    fun addArrayNode(elementsByTagName: NodeList) {
        elementsByTagName.toArrayMap().forEach { (key, value) ->
            this[key] = value
        }
    }

    private fun String.escapeXml(): String {
        if (this.contains("<Data>")) return this
        return buildString {
            <EMAIL> { char ->
                when (char) {
                    '<' -> append("&lt;")
                    '>' -> append("&gt;")
                    '&' -> append("&amp;")
                    '"' -> append("&quot;")
                    '\'' -> append("\\\'")
                    else -> append(char)
                }
            }
        }
    }

    private fun NodeList.toMap(): MutableMap<String, String> {
        val map = mutableMapOf<String, String>()
        for (i in 0 until length) {
            val element = item(i) as Element
            map[element.getAttribute("name")] = element.textContent
        }
        return map
    }

    private fun NodeList.toArrayMap(): MutableMap<String, List<String>> {
        val map = mutableMapOf<String, List<String>>()
        for (i in 0 until length) {
            val element = item(i) as Element
            map[element.getAttribute("name")] = element.getElementsByTagName("item").toStringList{ it }
        }
        return map
    }

    private fun NodeList.toStringList(transform: (String) -> String): List<String> {
        val list = mutableListOf<String>()
        for (i in 0 until length) {
            val element = item(i) as Element
            list.add(transform(element.textContent))
        }
        return list
    }

    fun save() {
        if (mPath.isEmpty()) throw Exception("path is empty")
        val c = StringBuilder().apply {
            appendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>")
            appendLine("<!--This Content is generated from https://git.17zjh.com/sgg-android/weplay-libs/" +
                    "lib-<NAME_EMAIL>, please do not modify it manually.-->")
            appendLine("<resources>")
            keys.sorted().map { k -> k to get(k) }.forEach { (k, v) ->
                when (v) {
                    is String -> appendLine("\t<string name=\"${k}\">${v.trim().escapeXml()}</string>")
                    is List<*> -> appendLine(
                        "\t<string-array name=\"${k}\">${
                            v.joinToString(
                                separator = "\n",
                                prefix = "\n",
                                postfix = "\n"
                            ) { "\t\t<item>${it.toString().escapeXml()}</item>" }
                        }\t</string-array>"
                    )
                }
            }
            appendLine("</resources>")
        }
        File(mPath).writeText(c.toString())
    }
}

@Target(AnnotationTarget.FIELD)
annotation class AndroidFileName(val value: String)

afterEvaluate {
    tasks.getByName("generateDebugResValues") {
        finalizedBy("downloadTranslation")
    }
    rootProject.tasks.getByName("prepareKotlinBuildScriptModel") {
        dependsOn(":lib:language:downloadTranslation")
    }
    // disable gradle cache
    val ts2disable = listOf(
        "generateDebugResValues",
        "generateReleaseResValues",
        "packageDebugResources",
        "packageReleaseResources",
    )
    ts2disable.forEach {
        tasks.getByName(it) {
            outputs.upToDateWhen { false }
        }
    }
}