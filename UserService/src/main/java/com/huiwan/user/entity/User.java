package com.huiwan.user.entity;

import android.content.ContentValues;
import android.database.Cursor;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.annotations.SerializedName;
import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.util.CursorUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.constants.BaseConstants;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.store.database.WPModel;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.UserInterface;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.model.entity.family.FamilyLightInfo;
import com.wepie.wespy.model.entity.qualifying.QualifyingUserInfo;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_pu_msg_newFriend;
import com.wepie.wespy.net.tcp.packet.ChatPackets.userInfoChatServer;

import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class User extends WPModel implements UserInterface {
    private static final String TAG = "UserBean";

    public static final String REGISTER_TIME = "register_time";
    private static final long serialVersionUID = 1158358397998556439L;
    public static final int GENDER_DEFAULT = 0;
    public static final int GENDER_MALE = 1;
    public static final int GENDER_FEMALE = 2;

    public static final String TABLE_NAME = "user";
    public static final String PRIMARY_KEY_NAME = "uid";

    public static final String UID = "uid";
    public static final String EMAIL = "email";
    public static final String NICKNAME = "nickname";
    public static final String GENDER = "gender";
    public static final String HEADIMGURL = "headimgurl";
    public static final String SINA_UID = "sina_uid";
    public static final String BIRTHDAY = "birthday";
    public static final String AREA = "area";
    public static final String PROVINCE = "province";
    public static final String CITY = "city";
    public static final String SIGNATURE = "signature";
    public static final String WDID = "wdid";
    public static final String WDID_EDIT_NUM = "wdid_edit_num";
    public static final String VERSION = "version";
    public static final String PHONE = "phone";
    public static final String POINTS = "points";
    public static final String COIN = "coin";
    public static final String FLOWER = "flower";
    public static final String ADD_FRIEND_PRICE = "add_friend_price";
    public static final String WIN = "win";
    public static final String LOSE = "lose";
    public static final String LAST_LOGIN_TIME = "last_login_time";
    public static final String VIP_LEVEL = "vip_level";
    public static final String VIP_LEVEL_ORIGIN = "origin_vip";
    public static final String MATE_UID = "mate_uid";
    public static final String VIP = "vip";
    public static final String DISTANCE = "distance";
    public static final String AVATAR_SHOW = "avatar_show";
    public static final String REGISTER_TYPE = "register_type";
    public static final String TOTAL_CHARGE = "total_change";
    public static final String EXTRA = "extra_data";
    private static final String EXTRA_KEY_MEDAL = "wearing_medal";
    private static final String EXTRA_KEY_FAMILY_LIGHT = "family_light_board";
    private static final String EXTRA_KEY_CHIP_COIN = "chip_coin";
    private static final String EXTRA_KEY_LEVEL = "level";
    private static final String EXTRA_QF_INFO = "qualifying_info";
    private static final String EXTRA_COLLECTION_INFO = "collection_info";


    private static final String LUCKY_ID_LEVEL = "lucky_id_level";

    @SerializedName("points")
    public int points;
    @SerializedName("coin")
    public long coin;
    @SerializedName("flower")
    public int flower;
    @SerializedName("add_friend_price")
    public int add_friend_price;
    @SerializedName("win")
    public int win;
    @SerializedName("lose")
    public int lose;
    @SerializedName("last_login_time")
    public long last_login_time;
    @SerializedName("email")
    public String email;
    @SerializedName("gender")
    public int gender;
    @SerializedName("headimgurl")
    public String headimgurl;
    @SerializedName("uid")
    public int uid;
    @SerializedName("sina_uid")
    public Long sina_uid = 0L;
    @SerializedName("nickname")
    public String nickname;
    @SerializedName("sid")
    public String sid;
    @SerializedName("birthday")
    public String birthday;
    @SerializedName("area")
    public String area = "";
    @SerializedName("province")
    public String province = "";
    @SerializedName("city")
    public String city = "";
    @SerializedName("signature")
    public String signature;
    @SerializedName("version")
    public int version;
    @SerializedName("phone")
    public String phone = "";
    public static final String PHOTO_NUM = "photo_num";
    @SerializedName("photo_num")
    public int photo_num;
    public static final String SINGER_STATE = "singer_state";
    @SerializedName("singer_state")
    public int singer_state;
    @SerializedName("register_time")
    public String register_time;
    @SerializedName("total_charge")
    private long total_charge; //充值金币 ，不存数据库

    /**
     * 卧底id，只反序列化，不序列化.
     * 后台异步生成,该数据可能为空
     */
    @SerializedName("wdid")
    public String wdid = "";
    /**
     * 卧底id的修改次数？目前没有用
     */
    @SerializedName("wdid_edit_num")
    public Integer wdid_edit_num;
    @SerializedName("distance")
    public String distance = "";
    @SerializedName("achivement_not_show")
    private int achivement_not_show;//1 不显示 0 显示

    @SerializedName("is_ban")
    private int is_ban = 0;//是否被封禁，不存数据库
    @SerializedName("is_permanent_ban")
    private int isPermanentBan = 0; //是否被永封，不存数据库

    /**
     * 客服 vip 等级，根据
     */
    @SerializedName("vip_level")
    private int vip_level = 0;//vip等级，不存数据库
    /**
     * 客服后台显示的vip等级
     */
    @SerializedName("origin_vip")
    private int origin_vip = 0;

    @SerializedName("mate_uid")
    private int mate_uid;//伴侣uid，不存数据库
    @SerializedName("nameFirstLetter")
    public String nameFirstLetter;//本地字段

    /**
     * 会员等级
     */
    @SerializedName("vip")
    public int vip = 0;

    @SerializedName("avatar_show")
    public int avatarShow = 0; // WePlay 1.3.0 新增 是否在主页显示play秀 -1 未设置 0 关闭 1 已开启

    @SerializedName("register_type")
    public String registerType = "";

    @SerializedName(EXTRA_KEY_MEDAL)
    public List<IMedalApi.WearInfo> wearMedals = new CopyOnWriteArrayList<>();

    @SerializedName("lucky_number_level")
    public int luckyIdLevel = 0;

    @SerializedName(EXTRA_KEY_CHIP_COIN)
    public long chipCoin = 0;

    @SerializedName(EXTRA_KEY_FAMILY_LIGHT)
    public FamilyLightInfo familyLightInfo = new FamilyLightInfo();

    @SerializedName("level")
    private int level = 1;

    @SerializedName("qualifying_info")
    private QualifyingUserInfo qfInfo = new QualifyingUserInfo();

    @SerializedName("collection_info")
    public UserCollectionInfo collectionInfo = new UserCollectionInfo();

    @Override
    public String getNameFirstLetter() {
        return nameFirstLetter;
    }

    public void setNameFirstLetter(String nameFirstLetter) {
        this.nameFirstLetter = nameFirstLetter;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    @Override
    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    @Override
    public int getUid() {
        return uid;
    }

    public void setSina_uid(Long sina_uid) {
        this.sina_uid = sina_uid;
    }

    public boolean avatarShowInMain() {
        return avatarShow == 1;
    }

    @Override
    public String getNickname() {
        if (nickname == null) return "";
        return nickname.replace('\n', ' ').replace('\r', ' ');
    }

    @Override
    public String getRemarkName() {
        FriendInfo friendInfo = FriendInfoCacheManager.getInstance().getFriendInfoByUid(uid);
        if (friendInfo == null) {
            return getNickname();
        } else {
            return friendInfo.getRemarkName();
        }
    }

    public String getRemarkNameLimitLength() {
        String name = getRemarkName();
        return StringUtil.subName(name, 5);
    }

    public String getNicknameLimitLength() {
        String name = getRemarkName();
        return StringUtil.subName(name, 5);
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getArea() {
        return area;
    }

    public AreaConfig.Area getAreaInfo() {
        AreaConfig.Area ar = ConfigHelper.getInstance().getAreaConfig().getConfig().getArea(area);
        if (ar == null) {
            ar = new AreaConfig.Area();
            ar.area = area;
        }
        return ar;
    }

    public String getAreaName() {
        return getAreaInfo().name;
    }

    public String getAreaUrl() {
        return getAreaInfo().getAreaUrl();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getWdid() {
        if (wdid == null) {
            wdid = "";
        }
        return wdid;
    }

    public int getQfGrade() {
        if (qfInfo == null) return 0;
        return qfInfo.getGrade();
    }

    public int getQfStar() {
        if (qfInfo == null) return 0;
        return qfInfo.getStar();
    }

    public QualifyingUserInfo getQfInfo() {
        return qfInfo;
    }

    public UserCollectionInfo getCollectionInfo() {
        return collectionInfo;
    }
    public void setQfInfo(QualifyingUserInfo info) {
        this.qfInfo = info;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public void setWdid_edit_num(Integer wdid_edit_num) {
        this.wdid_edit_num = wdid_edit_num;
    }

    @Override
    public String getTableName() {
        return User.TABLE_NAME;
    }

    @Override
    public String getPrimaryKeyName() {
        return User.PRIMARY_KEY_NAME;
    }

    @Override
    public String getPrimaryKey() {
        return String.valueOf(this.uid);
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public long getCoin() {
        return coin;
    }

    public void setCoin(long coint) {
        this.coin = coint;
    }

    public int getAdd_friend_price() {
        return add_friend_price;
    }

    public void setAdd_friend_price(int add_friend_price) {
        this.add_friend_price = add_friend_price;
    }

    public int getFlower() {
        return flower;
    }

    public void setFlower(int flower) {
        this.flower = flower;
    }

    public int getWin() {
        return win;
    }

    public void setWin(int win) {
        this.win = win;
    }

    public int getLose() {
        return lose;
    }

    public void setLose(int lose) {
        this.lose = lose;
    }

    public long getLast_login_time() {
        return last_login_time;
    }

    public void setLast_login_time(long last_login_time) {
        this.last_login_time = last_login_time;
    }

    public void setPhoto_num(int photo_num) {
        this.photo_num = photo_num;
    }

    public int getPhoto_num() {
        return photo_num;
    }

    public void setSinger_state(int singer_state) {
        this.singer_state = singer_state;
    }

    public int getSinger_state() {
        return singer_state;
    }

    public boolean isBan() {
        return is_ban == 1;
    }

    public void setBan(boolean is_ban) {
        if (is_ban) {
            this.is_ban = 1;
        } else {
            this.is_ban = 0;
        }
    }

    public boolean isPermanentBan() {
        return isPermanentBan == 1;
    }

    public void setPermanentBan(boolean isPermanentBan) {
        if (isPermanentBan) {
            this.isPermanentBan = 1;
        } else {
            this.isPermanentBan = 0;
        }
    }

    public void setVipLevel(int level) {
        vip_level = level;
    }

    public int getVipLevel() {
        return vip_level;
    }

    public void setOriginVipLevel(int level) {
        origin_vip = level;
    }

    public int getOriginVipLevel() {
        return origin_vip;
    }

    public int getAchivement_not_show() {
        return achivement_not_show;
    }

    public void setAchivement_not_show(int achivement_not_show) {
        this.achivement_not_show = achivement_not_show;
    }

    public int getMateUid() {
        return mate_uid;
    }

    public void setMateUid(int uid) {
        mate_uid = uid;
    }

    @Override
    public WPModel fromCursor(Cursor cursor) {
        if (cursor.moveToNext()) {
            final User user = new User();
            user.setUid(CursorUtil.getInt(cursor, User.UID));
            user.setBirthday(CursorUtil.getString(cursor, User.BIRTHDAY));
            user.setCity(CursorUtil.getString(cursor, User.CITY));
            user.setEmail(CursorUtil.getString(cursor, User.EMAIL));
            user.setGender(CursorUtil.getInt(cursor, User.GENDER));
            user.setHeadimgurl(CursorUtil.getString(cursor, User.HEADIMGURL));
            user.setNickname(CursorUtil.getString(cursor, User.NICKNAME));
            user.setProvince(CursorUtil.getString(cursor, User.PROVINCE));
            user.setSignature(CursorUtil.getString(cursor, User.SIGNATURE));
            user.setSina_uid(CursorUtil.getLong(cursor, User.SINA_UID));
            user.setWdid(CursorUtil.getString(cursor, User.WDID));
            user.setWdid_edit_num(CursorUtil.getInt(cursor, User.WDID_EDIT_NUM));
            user.setVersion(CursorUtil.getInt(cursor, User.VERSION));
            user.setPhone(CursorUtil.getString(cursor, User.PHONE));
            user.setPhoto_num(CursorUtil.getInt(cursor, PHOTO_NUM));
            user.setSinger_state(CursorUtil.getInt(cursor, SINGER_STATE));

            user.setPoints(CursorUtil.getInt(cursor, User.POINTS));
            user.setCoin(CursorUtil.getLong(cursor, User.COIN));
            user.setFlower(CursorUtil.getInt(cursor, User.FLOWER));
            user.setAdd_friend_price(CursorUtil.getInt(cursor, User.ADD_FRIEND_PRICE));
            user.setWin(CursorUtil.getInt(cursor, User.WIN));
            user.setLose(CursorUtil.getInt(cursor, User.LOSE));
            user.setLast_login_time(CursorUtil.getLong(cursor, User.LAST_LOGIN_TIME));
            user.setVip(CursorUtil.getInt(cursor, User.VIP));
            user.setArea(CursorUtil.getString(cursor, User.AREA));
            user.setDistance(CursorUtil.getString(cursor, User.DISTANCE));
            user.setAvatarShow(CursorUtil.getInt(cursor, User.AVATAR_SHOW));
            user.updateExtra(CursorUtil.getString(cursor, User.EXTRA));
            user.luckyIdLevel = CursorUtil.getInt(cursor, User.LUCKY_ID_LEVEL);
            return user;
        }
        return null;
    }

    public String getExtra() {
        JsonObject jsonObject = new JsonObject();
        jsonObject.add(EXTRA_KEY_MEDAL, JsonUtil.getGson().toJsonTree(wearMedals));
        jsonObject.add(EXTRA_KEY_FAMILY_LIGHT, JsonUtil.getGson().toJsonTree(familyLightInfo));
        jsonObject.addProperty(EXTRA_KEY_CHIP_COIN, chipCoin);
        jsonObject.addProperty(EXTRA_KEY_LEVEL, level);
        jsonObject.add(EXTRA_QF_INFO, JsonUtil.getGson().toJsonTree(qfInfo));
        jsonObject.add(EXTRA_COLLECTION_INFO, JsonUtil.getGson().toJsonTree(collectionInfo));
        return jsonObject.toString();
    }

    public void updateExtra(String extra) {
        if (TextUtils.isEmpty(extra)) {
            return;
        }
        try {
            JsonObject jsonObject = JsonParser.parseString(extra).getAsJsonObject();
            List<IMedalApi.WearInfo> wearInfoList = JsonUtil.parseJsonArray(jsonObject.getAsJsonArray(EXTRA_KEY_MEDAL), IMedalApi.WearInfo.class);
            putMedals(wearInfoList);
            familyLightInfo = JsonUtil.parseJson(jsonObject.get(EXTRA_KEY_FAMILY_LIGHT).toString(), FamilyLightInfo.class);
            JsonElement chipCoinEle = jsonObject.get(EXTRA_KEY_CHIP_COIN);
            if (chipCoinEle != null) {
                chipCoin = chipCoinEle.getAsLong();
            }
            JsonElement levelEle = jsonObject.get(EXTRA_KEY_LEVEL);
            if (levelEle != null) {
                level = levelEle.getAsInt();
            }
            qfInfo = JsonUtil.parseJson(jsonObject.get(EXTRA_QF_INFO).toString(), QualifyingUserInfo.class);
            collectionInfo = JsonUtil.parseJson(jsonObject.get(EXTRA_COLLECTION_INFO).toString(), UserCollectionInfo.class);
        } catch (Exception e) {
            HLog.d(TAG, "error init extra data of user, {}", e);
        }
    }

    public void setAvatarShow(int avatarShow) {
        this.avatarShow = avatarShow;
    }

    public void setRegisterType(String registerType) {
        this.registerType = registerType;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getVip() {
        return vip;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public ContentValues toContentValues() {
        ContentValues values = new ContentValues();
        values.put(UID, uid);
        values.put(EMAIL, email);
        values.put(NICKNAME, nickname);
        values.put(GENDER, gender);
        values.put(HEADIMGURL, headimgurl);
        values.put(SINA_UID, sina_uid);
        values.put(BIRTHDAY, birthday);
        values.put(PROVINCE, province);
        values.put(CITY, city);
        values.put(SIGNATURE, signature);
        values.put(WDID, wdid);
        values.put(WDID_EDIT_NUM, wdid_edit_num);
        values.put(VERSION, version);
        values.put(PHONE, "");
        values.put(PHOTO_NUM, photo_num);
        values.put(SINGER_STATE, singer_state);

        values.put(POINTS, points);
        values.put(COIN, 0L);
        values.put(FLOWER, flower);
        values.put(ADD_FRIEND_PRICE, add_friend_price);
        values.put(WIN, win);
        values.put(LOSE, lose);
        values.put(LAST_LOGIN_TIME, last_login_time);
        values.put(VIP, vip);
        values.put(AREA, area);
        values.put(DISTANCE, distance);
        values.put(AVATAR_SHOW, avatarShow);
        values.put(EXTRA, getExtra());
        values.put(LUCKY_ID_LEVEL, luckyIdLevel);
        return values;
    }

    public static User fromProto(GeneratedMessageLite<?, ?> message) {
        User user = null;
        if (message != null) {
            chat_pu_msg_newFriend protMsg = (chat_pu_msg_newFriend) message;
            userInfoChatServer userInfo = protMsg.getExtraUserInfo();

            user = new User();
            user.setUid(userInfo.getUid());
            user.setEmail(userInfo.getEmail());
            user.setNickname(userInfo.getNickname());
            user.setGender(userInfo.getGender());
            user.setHeadimgurl(userInfo.getHeadimgurl());
            //user.setSina_uid(userInfo.getSinaUid());
            user.setBirthday(userInfo.getBirthday());
            user.setProvince("");
            user.setCity("");
            user.setSignature(userInfo.getSignature());
            user.setWdid(userInfo.getWdid());
            user.setWdid_edit_num(userInfo.getWdidEditNum());
            user.setPhoto_num(userInfo.getPhotoNum());
            user.setSinger_state(userInfo.getSingerState());
            user.setVersion(userInfo.getVersion());//version

            user.setPoints(userInfo.getPoints());
            user.setFlower(userInfo.getFlower());
            user.setCoin(userInfo.getCoin());
            user.setWin(userInfo.getWin());
            user.setLose(userInfo.getLose());
            user.setAdd_friend_price(userInfo.getAddFriendPrice());
        }
        return user;
    }


    @NonNull
    @Override
    public String toString() {
        return "User{" + "uid=" + uid +
                ", level=" + level +
                ", points=" + points +
                ", nickname='" + nickname + '\'' +
                ", chipCoin=" + chipCoin +
                ", coin=" + coin +
                ", flower=" + flower +
                ", vip_level=" + vip_level +
                ", vip=" + vip +
                ", hashCode=" + hashCode() +
                '}';
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    /**
     *
     * 根据经验点计算等级，保留方便搜索
     * @deprecated 4.10.5 开始用户等级直接从服务器获取，不再自己计算
     * @param point 经验点
     * @return 等级
     */
    @Deprecated(since = "4.10.5")
    public static int getLevel(int point) {
        return (int) Math.floor((Math.sqrt(3025 + 20L * point) - 55) / 10) + 1;
    }

    public boolean isSinger() {
        return singer_state == BaseConstants.SERVER_YES;
    }

    public String uid2code() {
        char[] chars = new char[]{
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
                'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
        };
        StringBuilder sb = new StringBuilder();
        int num = uid;
        do {
            int remain = num % 26;
            char c = chars[remain];
            sb.append(c);
            num = num / 26;
        } while (num > 0);

        StringBuilder sup = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            if (i >= sb.length()) sup.append("A");
        }
        return sup.toString() + sb.reverse();
    }

    public static int code2uid(String code) {
        char[] chars = new char[]{
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
                'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
        };
        char[] charArray = code.toUpperCase().toCharArray();
        int length = charArray.length;
        int uid = 0;
        for (int i = length - 1; i >= 0; i--) {//从最后一位开始
            int index = length - i;//第index位
            for (int position = 0; position < chars.length; position++) {//遍历字符数组找位置
                HLog.i("compare", "code2uid: " + charArray[i] + "  " + chars[position]);
                if (charArray[i] == chars[position]) {
                    uid = (int) Math.pow(26, index - 1) * position + uid;
                }
            }
        }
        return uid;
    }

    public boolean hasPhone() {
        return !TextUtils.isEmpty(phone);
    }

    public boolean hasGender() {
        return gender == GENDER_MALE || gender == GENDER_FEMALE;
    }

    public boolean isMale() {
        return gender == GENDER_MALE;
    }

    public boolean isFemale() {
        return gender == GENDER_FEMALE;
    }

    /**
     * -1则无值
     *
     */
    public float getDist() {
        try {
            return Float.parseFloat(distance);
        } catch (Exception e) {
            return -1;
        }
    }

    public long getRegisterTime() {
        try {
            return Timestamp.valueOf(register_time).getTime();
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public void setRegisterTime(String register_time) {
        this.register_time = register_time;
    }

    public long getTotalCharge() {
        return total_charge;
    }

    public void setTotalCharge(long charge) {
        this.total_charge = charge;
    }

    public boolean isVip() {
        return vip > 0;
    }

    public void reset() {
        uid = -1;
        coin = 0;
        points = 0;
        flower = 0;
        nickname = "";
        sid = "";
    }

    public void setFromOther(User user) {
        if (this == user) {
            return;
        }
        setNickname(user.getNickname());
        setGender(user.getGender());
        setHeadimgurl(user.getHeadimgurl());
        setArea(user.getArea());
        setProvince(user.getProvince());
        setCity(user.getCity());
        setSignature(user.getSignature());
        setVersion(user.getVersion());
        setPoints(user.getPoints());
        setFlower(user.getFlower());
        setAdd_friend_price(user.getAdd_friend_price());
        setWin(user.getWin());
        setLose(user.getLose());
        setSinger_state(user.getSinger_state());
        setAchivement_not_show(user.getAchivement_not_show());
        setBan(user.isBan());
        setPermanentBan(user.isPermanentBan());
        setVipLevel(user.getVipLevel());
        setOriginVipLevel(user.getOriginVipLevel());
        setCoin(user.getCoin());
        setBirthday(user.getBirthday());
        setMateUid(user.getMateUid());
        setRegisterTime(user.register_time);
        setVip(user.getVip());
        setPhone(user.getPhone());
        setWdid(user.getWdid());
        chipCoin = user.chipCoin;
        avatarShow = user.avatarShow;
        setRegisterType(user.registerType);
        setTotalCharge(user.getTotalCharge());
        putMedals(user.wearMedals);
        luckyIdLevel = user.luckyIdLevel;
        familyLightInfo = user.familyLightInfo;
        qfInfo = user.qfInfo;
        level = user.level;
    }

    private void putMedals(List<IMedalApi.WearInfo> list) {
        if (wearMedals != null) {
            wearMedals.clear();
        } else {
            wearMedals = new CopyOnWriteArrayList<>();
        }
        if (list != null && !list.isEmpty()) {
            wearMedals.addAll(list);
        }
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }
}



