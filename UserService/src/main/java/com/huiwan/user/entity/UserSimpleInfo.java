package com.huiwan.user.entity;

import static com.huiwan.user.entity.User.GENDER_FEMALE;
import static com.huiwan.user.entity.User.GENDER_MALE;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.util.StringUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.UserInterface;
import com.huiwan.user.pinyin.PinYinUtil;
import com.wepie.wespy.model.entity.family.FamilyLightInfo;
import com.wepie.wespy.model.entity.qualifying.QualifyingUserInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by bigwen on 2018/1/20.
 */

public class UserSimpleInfo implements UserInterface, Cloneable {
    /**
     * headimgurl : http://wespypicuser.afunapp.com/FgLnZ7kt4iYSIa5U-dAS2SgeGU_S
     * uid : 32685
     * nickname : pgone
     */

    public static final String UID = "uid";
    public static final String HEADIMGURL = "headimgurl";
    public static final String NICKNAME = "nickname";
    public static final String GENDER = "gender";

    @SerializedName("headimgurl")
    public String headimgurl = "";
    @SerializedName("uid")
    public int uid;
    @SerializedName("wdid")
    public String wdid = "";
    @SerializedName("nickname")
    public String nickname = "";
    @SerializedName("gender")
    public int gender;
    @SerializedName("flower")
    public int flower = 0;
    @SerializedName("wearing_medal")
    public List<IMedalApi.WearInfo> wearMedals = new ArrayList<>();

    /**
     * 会员等级
     */
    @SerializedName("vip")
    public int vip = 0;

    @SerializedName("area")
    public String area = ""; //地区代码

    @SerializedName("lucky_number_level")
    public int luckyIdLevel = 0;

    @SerializedName("family_light_board")
    public FamilyLightInfo familyLightInfo = new FamilyLightInfo();

    @SerializedName("points")
    public int points = 0;

    @SerializedName("level")
    public int level = 1;

    @SerializedName("qualifying_info")
    private QualifyingUserInfo qfInfo = new QualifyingUserInfo();

    @SerializedName("collection_info")
    public UserCollectionInfo collectionInfo = new UserCollectionInfo();

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    @Override
    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }

    @Override
    public int getUid() {
        return uid;
    }

    @Override
    public String getWdid() {
        return wdid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getWdId() {
        if (wdid == null) {
            wdid = "";
        }
        return wdid;
    }

    @Override
    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    @Override
    public String getRemarkName() {
        FriendInfo friendInfo = FriendInfoCacheManager.getInstance().getFriendInfoByUid(getUid());
        if (friendInfo != null) {
            return friendInfo.getRemarkName();
        } else {
            return getNickname();
        }
    }

    //限制6个字符
    public String getRemarkNameLimit6() {
        String name = getRemarkName();
        return StringUtil.subName(name, 6);
    }

    public boolean isFemale() {
        return gender == GENDER_FEMALE;
    }

    public boolean isMale() {
        return gender == GENDER_MALE;
    }

    public boolean hasGender() {
        return isFemale() || isMale();
    }

    @SerializedName("name_first_letter")
    private String nameFirstLetter = "";

    @Override
    public String getNameFirstLetter() {
        if (TextUtils.isEmpty(nameFirstLetter)) {
            nameFirstLetter = PinYinUtil.pinyinFirstLetterToString(getRemarkName()).toUpperCase(Locale.US);
        }
        return nameFirstLetter;
    }

    @Override
    public int getLevel() {
        return level;
    }

    public static UserSimpleInfo newDefault(int uid) {
        UserSimpleInfo simpleInfo = new UserSimpleInfo();
        simpleInfo.uid = uid;
        simpleInfo.nickname = "";
        return simpleInfo;
    }

    public int getVip() {
        return vip;
    }

    public int getFlower() {
        return flower;
    }

    public int getPoints() {
        return points;
    }

    public int getQfGrade() {
        if (qfInfo == null) return 0;
        return qfInfo.getGrade();
    }

    public int getQfStar() {
        if (qfInfo == null) return 0;
        return qfInfo.getStar();
    }

    public void setQfInfo(QualifyingUserInfo qfInfo) {
        this.qfInfo = qfInfo;
    }

    public String getAreaName() {
        AreaConfig.Area area = getArea();
        if (area == null) {
            return "";
        }
        return area.name;
    }

    @Nullable
    public AreaConfig.Area getArea() {
        if (TextUtils.isEmpty(area)) {
            return null;
        }
        return ConfigHelper.getInstance().getAreaConfig().getConfig().getArea(area);
    }

    public String getAreaUrl() {
        if (TextUtils.isEmpty(area)) {
            return "";
        }
        return ConfigHelper.getInstance().getAreaConfig().getConfig().getFlagBaseUrl() + area;
    }

    @Override
    public String toString() {
        return "UserSimpleInfo{" +
                "uid=" + uid +
                '}';
    }

    @NonNull
    @Override
    public UserSimpleInfo clone() {
        UserSimpleInfo simpleInfo = new UserSimpleInfo();
        simpleInfo.headimgurl = this.headimgurl;
        simpleInfo.uid = this.uid;
        simpleInfo.wdid = this.wdid;
        simpleInfo.nickname = this.nickname;
        simpleInfo.gender = this.gender;
        simpleInfo.flower = this.flower;
        simpleInfo.wearMedals = this.wearMedals;
        simpleInfo.vip = this.vip;
        simpleInfo.area = this.area;
        simpleInfo.luckyIdLevel = this.luckyIdLevel;
        simpleInfo.familyLightInfo = this.familyLightInfo;
        simpleInfo.qfInfo = this.qfInfo;
        simpleInfo.points = this.points;
        return simpleInfo;
    }
}
