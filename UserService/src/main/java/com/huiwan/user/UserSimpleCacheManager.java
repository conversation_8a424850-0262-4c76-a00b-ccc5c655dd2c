package com.huiwan.user;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.SparseArray;

import androidx.annotation.NonNull;

import com.huiwan.base.util.StringUtil;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.user.http.UserApi;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * simple接口服务器只返回user的部分字段，且该接口服务器有缓存
 * 适合非好友类型的用户请求，用户信息不保存本地的场景
 * <p>
 * UserSimpleCacheManager 请求前会先找 UserCacheManager 是否存在缓存，减少请求量
 */
public class UserSimpleCacheManager {

    public static final String TAG = UserSimpleCacheManager.class.getName();

    private static final long DELAY_TIME = 100;
    private static final int MESSAGE_REQUEST = 0x111;
    private static final int MESSAGE_READ_DB = 0x112;

    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == MESSAGE_REQUEST) {
                doRequests();
            } else if (msg.what == MESSAGE_READ_DB) {
                readFromDB();
            }
        }
    };

    private final SparseArray<UserSimpleInfo> mUserMap = new SparseArray<>();

    private SparseArray<List<UserSimpleInfoCallback>> mRequestList = new SparseArray<>();

    private SparseArray<List<UserSimpleInfoCallback>> mLocalList = new SparseArray<>();

    public void getUser(int uid, final UserSimpleInfoCallback callback) {
        if (uid <= 0) {
            callback.onUserInfoFailed("invalid uid 0");
            return;
        }
        ILife life = LifeUserSimpleInfoCallback.getLife(callback);
        if (life != null && life.isDestroyed()) {
            return;
        }
        if (!ThreadUtil.isMainThread()) {
            ThreadUtil.runOnUiThread(() -> getUserInner(uid, callback, life));
            FLog.e(new Throwable("Illegal Thread getUser"));
        } else {
            getUserInner(uid, callback, life);
        }
    }

    private void getUserInner(int uid, UserSimpleInfoCallback callback, ILife life) {
        UserSimpleInfo simpleInfo = mUserMap.get(uid);
        if (simpleInfo != null) {
            if (callback != null) callback.onUserInfoSuccess(simpleInfo);
            return;
        }
        List<UserSimpleInfoCallback> callbackList = mLocalList.get(uid);
        if (callbackList == null) {
            callbackList = new LinkedList<>();
            mLocalList.put(uid, callbackList);
        }
        if (callback != null) {
            callbackList.add(callback);
            if (life != null) {
                final List<UserSimpleInfoCallback> list = callbackList;
                life.onDestroy(() -> {
                    list.remove(callback);
                    return null;
                });
            }
        }
        if (!handler.hasMessages(MESSAGE_READ_DB)) {
            handler.sendEmptyMessageDelayed(MESSAGE_READ_DB, DELAY_TIME);
        }
    }

    public UserSimpleInfo getLocalInfo(int uid) {
        return mUserMap.get(uid);
    }

    public void updateSimpleInfoIfExist(int uid, int vip, String nickname, String headImgUrl) {
        UserSimpleInfo simpleInfo = mUserMap.get(uid);
        if (simpleInfo != null) {
            simpleInfo.vip = vip;
            simpleInfo.nickname = nickname;
            simpleInfo.headimgurl = headImgUrl;
        }
    }

    public void getUserList(List<Integer> uids, UserListSimpleInfoCallback callback) {
        List<Integer> lastUidList = new LinkedList<>();
        List<UserSimpleInfo> infoList = new LinkedList<>();
        int size = uids.size();
        for (int i = 0; i < size; i++) {
            int uid = uids.get(i);
            UserSimpleInfo userSimpleInfo = mUserMap.get(uid);
            if (userSimpleInfo == null) {
                lastUidList.add(uid);
            } else {
                infoList.add(userSimpleInfo);
            }
        }
        ILife life = LifeUserListSimpleInfoCallback.getLife(callback);
        getUserListFromDbOrServer(lastUidList, new LifeUserListSimpleInfoCallback(life) {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                infoList.addAll(userSimpleInfos);
                callback.onUserInfoSuccess(infoList);
            }

            @Override
            public void onUserInfoFailed(String description) {
                callback.onUserInfoFailed(description);
            }
        });
    }

    void getUserListFromServer(List<Integer> uidList, final UserListSimpleInfoCallback callback) {
        int totalSize = uidList.size();
        UserApi.getUserSimpleInfoList(uidList, new LifeDataCallback<List<UserSimpleInfo>>(LifeUserListSimpleInfoCallback.getLife(callback)) {
            @Override
            public void onSuccess(Result<List<UserSimpleInfo>> result) {
                if (result == null || result.data == null) {
                    onFail(-1, "result or result.data is null");
                    return;
                }
                List<UserSimpleInfo> data = result.data;
                int size = data.size();
                for (int i = 0; i < size; i++) {
                    saveUser(data.get(i));
                }
                if (totalSize != size) {
                    HLog.aliLog(AliNetLogUtil.PORT.http, AliNetLogUtil.TYPE.err, StringUtil.formatS("user_simple_info list size not equal: {} != {}", uidList, result.data));
                }
                callback.onUserInfoSuccess(data);
            }

            @Override
            public void onFail(int code, String msg) {
                callback.onUserInfoFailed(msg);
            }
        });
    }

    void clearMemMap() {
        mUserMap.clear();
    }

    void clearUidList(List<Integer> uidList) {
        for (int i = 0; i < uidList.size(); i++) {
            mUserMap.remove(uidList.get(i));
        }
    }

    UserSimpleInfo updateOrCreateUserSimpleInfo(User user) {
        int uid = user.uid;
        UserSimpleInfo simpleInfo = mUserMap.get(uid);
        if (simpleInfo == null) {
            simpleInfo = new UserSimpleInfo();
        }
        simpleInfo.gender = user.gender;
        simpleInfo.headimgurl = user.headimgurl;
        simpleInfo.nickname = user.nickname;
        simpleInfo.uid = user.uid;
        simpleInfo.wdid = user.wdid;
        simpleInfo.flower = user.flower;
        simpleInfo.vip = user.vip;
        simpleInfo.area = user.area;
        if (simpleInfo.wearMedals == null) {
            simpleInfo.wearMedals = new ArrayList<>();
        } else {
            simpleInfo.wearMedals.clear();
        }
        List<IMedalApi.WearInfo> list = user.wearMedals;
        if (list != null && !list.isEmpty()) {
            simpleInfo.wearMedals.addAll(list);
        }
        simpleInfo.luckyIdLevel = user.luckyIdLevel;
        simpleInfo.familyLightInfo = user.familyLightInfo;
        simpleInfo.setQfInfo(user.getQfInfo());
        simpleInfo.collectionInfo = user.collectionInfo;
        simpleInfo.points = user.points;
        simpleInfo.level = user.getLevel();
        mUserMap.put(uid, simpleInfo);
        return simpleInfo;
    }

    private void sendRequestMessage(int uid, final UserSimpleInfoCallback callback) {
        UserSimpleInfoCallback userSimpleInfoCallback = UserSimpleInfoCallbackWrap.get(callback);
        List<UserSimpleInfoCallback> callbackList = mRequestList.get(uid);
        if (callbackList == null) {
            callbackList = new LinkedList<>();
            mRequestList.put(uid, callbackList);
        }
        if (userSimpleInfoCallback != null) {
            callbackList.add(userSimpleInfoCallback);
        }
        if (!handler.hasMessages(MESSAGE_REQUEST)) {
            handler.sendEmptyMessageDelayed(MESSAGE_REQUEST, DELAY_TIME);
        }
    }

    private void doRequests() {
        int size = mRequestList.size();
        if (size == 0) {
            return;
        }
        if (size == 1) {
            final List<UserSimpleInfoCallback> callbackList = mRequestList.valueAt(0);
            int uid = mRequestList.keyAt(0);
            mRequestList.clear();
            reqUserFromServer(uid, new UserSimpleInfoCallback() {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    if (callbackList == null) {
                        FLog.e(new Throwable("doRequests onUserInfoSuccess callbackList is null, size = 1"));
                        return;
                    }
                    for (UserSimpleInfoCallback callback : callbackList) {
                        callback.onUserInfoSuccess(simpleInfo);
                    }
                }

                @Override
                public void onUserInfoFailed(String description) {
                    if (callbackList == null) {
                        FLog.e(new Throwable("doRequests onUserInfoFailed callbackList is null, size = 1"));
                        return;
                    }
                    for (UserSimpleInfoCallback callback : callbackList) {
                        callback.onUserInfoFailed(description);
                    }
                }
            });
            return;
        }
        final SparseArray<List<UserSimpleInfoCallback>> requestingList = mRequestList;
        mRequestList = new SparseArray<>();

        List<Integer> uidList = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            uidList.add(requestingList.keyAt(i));
        }
        getUserListFromServer(uidList, new UserListSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfoList) {
                notifyUserInfoSuccess(requestingList, userSimpleInfoList);
            }

            @Override
            public void onUserInfoFailed(String description) {
                notifyUserInfoFailed(requestingList, description);
            }
        });
    }

    private void readFromDB() {
        int size = mLocalList.size();
        if (size == 0) {
            return;
        }
        if (size == 1) {
            final List<UserSimpleInfoCallback> callbackList = mLocalList.valueAt(0);
            int uid = mLocalList.keyAt(0);
            mLocalList.clear();
            UserService.get().getCacheUserLocal(uid, new UserInfoLoadCallback() {
                @Override
                public void onUserInfoSuccess(User userInfo) {
                    UserSimpleInfo simpleInfo = updateOrCreateUserSimpleInfo(userInfo);
                    if (callbackList == null) {
                        FLog.e(new Throwable("doRequests onUserInfoSuccess callbackList is null, size = 1"));
                        return;
                    }
                    for (UserSimpleInfoCallback callback : callbackList) {
                        callback.onUserInfoSuccess(simpleInfo);
                    }
                }

                @Override
                public void onUserInfoFailed(String description) {
                    if (callbackList == null) {
                        FLog.e(new Throwable("doRequests onUserInfoFailed callbackList is null, size = 1"));
                        return;
                    }
                    reqUserFromServer(uid, new UserSimpleInfoCallback() {
                        @Override
                        public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                            for (UserSimpleInfoCallback callback : callbackList) {
                                callback.onUserInfoSuccess(simpleInfo);
                            }
                        }

                        @Override
                        public void onUserInfoFailed(String description) {
                            for (UserSimpleInfoCallback callback : callbackList) {
                                callback.onUserInfoFailed(description);
                            }
                        }
                    });
                }
            });
            return;
        }
        final SparseArray<List<UserSimpleInfoCallback>> localList = mLocalList;
        mLocalList = new SparseArray<>();

        List<Integer> uidList = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            uidList.add(localList.keyAt(i));
        }

        getUserListFromDbOrServer(uidList, new UserListSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                notifyUserInfoSuccess(localList, userSimpleInfos);
            }

            @Override
            public void onUserInfoFailed(String description) {
                notifyUserInfoFailed(localList, description);
            }
        });
    }

    public void notifyUserInfoSuccess(SparseArray<List<UserSimpleInfoCallback>> array, List<UserSimpleInfo> userSimpleInfoList) {
        for (UserSimpleInfo simpleInfo : userSimpleInfoList) {
            int index = array.indexOfKey(simpleInfo.uid);
            if (index >= 0) {
                List<UserSimpleInfoCallback> callbackList = array.valueAt(index);
                if (callbackList == null) {
                    FLog.e(new Throwable("doRequests onUserInfoSuccess 1 callbackList is null"));
                    array.removeAt(index);
                    continue;
                }
                for (UserSimpleInfoCallback callback : callbackList) {
                    callback.onUserInfoSuccess(simpleInfo);
                }
                array.removeAt(index);
            }
        }
        int size = array.size();
        for (int i = 0; i < size; i++) {
            int uid = array.keyAt(i);
            List<UserSimpleInfoCallback> callbackList = array.valueAt(i);
            if (callbackList == null) {
                FLog.e(new Throwable("doRequests onUserInfoSuccess 2 callbackList is null"));
                continue;
            }
            for (UserSimpleInfoCallback callback : callbackList) {
                callback.onUserInfoFailed("");
            }
            HLog.e(TAG, "no simple info returned by server, uid: " + uid);
        }
    }

    public void notifyUserInfoFailed(SparseArray<List<UserSimpleInfoCallback>> array, String description) {
        int size = array.size();
        for (int i = 0; i < size; i++) {
            List<UserSimpleInfoCallback> callbackList = array.valueAt(i);
            if (callbackList == null) {
                FLog.e(new Throwable("doRequests onUserInfoFailed callbackList is null"));
                continue;
            }
            for (UserSimpleInfoCallback callback : callbackList) {
                callback.onUserInfoFailed(description);
            }
        }
    }

    private void getUserListFromDbOrServer(List<Integer> uidList, UserListSimpleInfoCallback callback) {
        ILife life = LifeUserListSimpleInfoCallback.getLife(callback);
        List<Integer> lastUidList = new LinkedList<>(uidList);
        List<UserSimpleInfo> infoList = new LinkedList<>();
        Runnable runnable = () -> getUserListFromServer(lastUidList, new LifeUserListSimpleInfoCallback(life) {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                infoList.addAll(userSimpleInfos);
                callback.onUserInfoSuccess(infoList);
            }

            @Override
            public void onUserInfoFailed(String description) {
                callback.onUserInfoFailed(description);
            }
        });
        UserService.get().getCacheUserListLocal(uidList, new LifeUserInfoListLoadCallback(life) {
            @Override
            public void onUserInfoSuccess(List<User> list) {
                for (User user : list) {
                    infoList.add(updateOrCreateUserSimpleInfo(user));
                    lastUidList.remove((Integer) user.uid);
                }
                if (lastUidList.isEmpty()) {
                    callback.onUserInfoSuccess(infoList);
                } else {
                    runnable.run();
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                runnable.run();
            }
        });
    }

    public void getUserFromServer(int uid, final UserSimpleInfoCallback callback) {
        sendRequestMessage(uid, callback);
    }

    private void reqUserFromServer(int uid, @NonNull final UserSimpleInfoCallback callback) {
        UserApi.getUserSimpleInfo(uid, new LifeDataCallback<UserSimpleInfo>(LifeUserSimpleInfoCallback.getLife(callback)) {
            @Override
            public void onSuccess(Result<UserSimpleInfo> result) {
                saveUser(result.data);
                callback.onUserInfoSuccess(result.data);
            }

            @Override
            public void onFail(int code, String msg) {
                callback.onUserInfoFailed(msg);
            }
        });
    }

    public void saveUser(UserSimpleInfo user) {
        if (user == null) return;
        mUserMap.put(user.getUid(), user);
    }

    static List<User> parseToUserInfo(List<UserSimpleInfo> userSimpleInfos) {
        List<User> users = new LinkedList<>();

        for (UserSimpleInfo userSimpleInfo : userSimpleInfos) {
            User user = new User();
            user.uid = userSimpleInfo.uid;
            user.headimgurl = userSimpleInfo.headimgurl;
            user.nickname = userSimpleInfo.nickname;
            user.flower = userSimpleInfo.flower;
            user.vip = userSimpleInfo.vip;
            user.points = userSimpleInfo.points;
            user.luckyIdLevel = userSimpleInfo.luckyIdLevel;
            user.collectionInfo = userSimpleInfo.collectionInfo;
            users.add(user);
        }
        return users;
    }

    private static class UserSimpleInfoCallbackWrap implements UserSimpleInfoCallback {

        private UserSimpleInfoCallback mCallback;

        private UserSimpleInfoCallbackWrap(@NonNull ILife life, UserSimpleInfoCallback callback) {
            this.mCallback = callback;
            life.onDestroy(() -> {
                UserSimpleInfoCallbackWrap.this.mCallback = null;
                return null;
            });
        }

        @Override
        public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
            if (mCallback != null) {
                mCallback.onUserInfoSuccess(simpleInfo);
            }
        }

        @Override
        public void onUserInfoFailed(String description) {
            if (mCallback != null) {
                mCallback.onUserInfoFailed(description);
            }
        }

        public static UserSimpleInfoCallback get(UserSimpleInfoCallback callback) {
            ILife life = LifeUserSimpleInfoCallback.getLife(callback);
            if (life == null) {
                return callback;
            }
            return new UserSimpleInfoCallbackWrap(life, callback);
        }
    }
}