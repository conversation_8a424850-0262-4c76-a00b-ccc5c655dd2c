package com.wepie.webview.intercept

import android.os.SystemClock
import okio.Buffer
import okio.BufferedSource
import java.io.FilterInputStream
import java.io.InputStream

/**
 * webview读取文件流耗时较久,本地直接读取
 */
class WebAutoCloseInputStream : FilterInputStream {
    private val tag: String
    private val stream: InputStream

    private val current = SystemClock.elapsedRealtime()
    private val length: Int

    private constructor(
        tag: String,
        stream: InputStream,
        buffer: Buffer
    ) : super(buffer.inputStream()) {
        this.tag = tag
        this.stream = stream
        this.length = buffer.size.toInt()
    }

    override fun close() {
        super.close()
        runCatching {
            stream.close()
        }

        webViewLog(
            "WebView transmission %s %s %s", SystemClock.elapsedRealtime() - current,
            formatBodyLength(length.toLong()), tag
        )
    }

    private fun formatBodyLength(length: Long): String {
        if (length <= 1024) {
            return length.toString()
        }
        if (length < 1024 * 1024) {
            return "${length / 1024}K"
        }
        return "${length / 1024 / 1024}M"
    }

    companion object {
        fun create(tag: String, source: BufferedSource): InputStream {
            val buffer = Buffer()
            source.readAll(buffer)
            return WebAutoCloseInputStream(tag, source.inputStream(), buffer)
        }

        fun create(tag: String, stream: InputStream): InputStream {
            val buffer = Buffer()
            buffer.readFrom(stream)
            return WebAutoCloseInputStream(tag, stream, buffer)
        }
    }
}