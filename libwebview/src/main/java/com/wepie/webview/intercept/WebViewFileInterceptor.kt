package com.wepie.webview.intercept

import android.os.SystemClock
import android.webkit.WebResourceResponse
import com.wepie.webview.cachepack.WebHttpClient


class WebViewFileInterceptor : WebViewRequestInterceptor {

    override fun interceptRequest(request: WebRequest): WebResponse? {
        val url = request.url
        val current = SystemClock.elapsedRealtime()
        val response = WebHttpClient.load(url, request.headers()) ?: return null

        val isCache = response.networkResponse?.code == 304 || response.cacheResponse != null
        val body = response.body
        if (body == null) {
            val dur = SystemClock.elapsedRealtime() - current
            //设置了no-cache可能会导致数据为空的
            webViewLog("body is empty:%s %s %s", dur, url, response.headers)
            response.close()
            return null
        }
        try {
            var stream = body.byteStream()
            val length: Long
            if (isCache) {
                stream = WebAutoCloseInputStream.create(url, body.source())
                length = stream.available().toLong()
            } else {
                length = body.contentLength()
            }

            val dur = SystemClock.elapsedRealtime() - current
            webViewLog(
                "from ${if (isCache) "cache" else "server"}:%s %s %s %s %s %s",
                dur,
                response.protocol,
                response.code,
                formatBodyLength(length),
                url,
                response.headers.toMultimap()
            )
            val webResourceResponse = WebResourceResponse(request.getMimeType(), "utf-8", stream)
            var message = response.message
            if (message.isEmpty()) {
                message = "OK"
            }
            webResourceResponse.responseHeaders = multimapToSingle(response.headers.toMultimap())

            var code = response.code
            if (code == 304) {
                code = 200
            }
            webResourceResponse.setStatusCodeAndReasonPhrase(code, message)
            return WebResponse(request, webResourceResponse)
        } catch (e: Exception) {
            response.close()
            webViewLog("setStatusCodeAndReasonPhrase failed,code:${response.code} e: ${e.message}")
            return null
        }
    }

    private fun formatBodyLength(length: Long): String {
        if (length <= 1024) {
            return length.toString()
        }
        if (length < 1024 * 1024) {
            return "${length / 1024}K"
        }
        return "${length / 1024 / 1024}M"
    }
}