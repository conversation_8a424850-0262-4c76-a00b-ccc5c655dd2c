<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="296dp"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:clipChildren="false">

    <include
        android:id="@+id/main_chess_view"
        layout="@layout/main_chess_view"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/main_content_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/main_content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/voice_user_info_dialog_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_chess_view">

        <ImageView
            android:id="@+id/seat_dialog_report_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            app:srcCompat="@drawable/voice_dialog_report" />

        <LinearLayout
            android:id="@+id/user_info_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.huiwan.decorate.NameTextView
                android:id="@+id/ice_ball_nick"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxWidth="140dp"
                android:maxLines="1"
                android:textColor="@color/color_text_accent_dark"
                android:textSize="18dp"
                tools:text="大锅真帅大锅真帅大锅真帅大锅真帅大锅真帅" />

            <ImageView
                android:id="@+id/gender_iv"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="4dp"
                tools:src="@drawable/icon_male" />

            <TextView
                android:id="@+id/seat_location_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:paddingTop="2dp"
                android:textColor="@color/color_text_tertiary"
                android:textSize="12dp"
                tools:text="台湾" />

            <TextView
                android:id="@+id/seat_distance_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/color_text_tertiary"
                android:textSize="12dp"
                tools:text="· 100km" />

        </LinearLayout>

        <com.huiwan.decorate.UserIdView
            android:id="@+id/user_id_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp" />

        <LinearLayout
            android:id="@+id/user_level_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="4dp">

            <com.huiwan.decorate.vip.VipLabelView
                android:id="@+id/vip_label"
                android:layout_width="24dp"
                android:layout_height="24dp"
                tools:src="@drawable/vip_label_1" />

            <ImageView
                android:id="@+id/charm_iv"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="2dp"
                tools:src="@drawable/user_charm_level_1" />

            <com.huiwan.decorate.JackarooLevelView
                android:id="@+id/jackaroo_level_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="2dp" />

            <com.huiwan.widget.FamilyLightView
                android:id="@+id/family_light_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/medal_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ice_ball_score_lay"
            android:layout_width="match_parent"
            android:layout_height="53dp"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal">

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/grade_lay"
                android:layout_width="92dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/grade_iv"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/grade_no_qualifying" />

                    <LinearLayout
                        android:id="@+id/star_lay"
                        android:layout_width="wrap_content"
                        android:layout_height="16dp"
                        android:layout_marginStart="-8dp"
                        android:background="@drawable/grade_start_bg"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="10dp"
                        android:paddingEnd="6dp">

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/grade_star" />

                        <TextView
                            android:id="@+id/star_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:textColor="#FFED53"
                            android:textFontWeight="600"
                            android:textSize="10dp" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:includeFontPadding="false"
                    android:text="@string/user_level"
                    android:textColor="@color/color_text_tertiary"
                    android:textSize="12dp" />
            </LinearLayout>

            <View
                android:id="@+id/divider1"
                android:layout_width="1dp"
                android:layout_height="40dp"
                android:background="#E6E6EE" />

            <LinearLayout
                android:id="@+id/games_lay"
                android:layout_width="92dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ice_ball_play_times"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_text_accent_dark"
                    android:textSize="24dp"
                    tools:text="0" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:includeFontPadding="false"
                    android:text="@string/ice_ball_total_games"
                    android:textColor="@color/color_text_tertiary"
                    android:textSize="12dp" />
            </LinearLayout>

            <View
                android:id="@+id/divider2"
                android:layout_width="1dp"
                android:layout_height="40dp"
                android:background="#E6E6EE" />

            <LinearLayout
                android:id="@+id/win_rate_lay"
                android:layout_width="92dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ice_ball_win_rate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:maxLines="1"
                    android:textColor="@color/color_text_accent_dark"
                    android:textSize="24dp"
                    tools:text="00:00:00;000" />

                <TextView
                    android:id="@+id/level_explain_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="@string/win_rate"
                    android:textColor="@color/color_text_tertiary"
                    android:textSize="12dp" />

            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ice_ball_add_friend_lay"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginHorizontal="12dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:visibility="visible">

            <TextView
                android:id="@+id/ice_ball_flirt_bt"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/user_flirt_icon"
                android:gravity="center"
                android:text="@string/room_seat_info_dialog_flirt"
                android:textColor="@color/white"
                android:textSize="16dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/ice_ball_add_friend_bt"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/user_add_friend_icon"
                android:gravity="center"
                android:text="@string/common_add_friends"
                android:textColor="#ffffff"
                android:textSize="16dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/ice_ball_send_gift_bt"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/user_send_gift_icon"
                android:gravity="center"
                android:text="@string/ice_ball_send_gift"
                android:textColor="@color/white"
                android:textSize="16dp" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/seat_dialog_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_content_layout" />

    </LinearLayout>


    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/ice_ball_head_image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-32dp"
        app:layout_constraintBottom_toTopOf="@+id/main_content_layout"
        app:layout_constraintEnd_toEndOf="@+id/main_content_layout"
        app:layout_constraintStart_toStartOf="@+id/main_content_layout"
        tools:src="@drawable/default_head_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>