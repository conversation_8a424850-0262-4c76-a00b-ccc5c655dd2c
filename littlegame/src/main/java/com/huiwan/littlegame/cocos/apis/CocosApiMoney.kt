package com.huiwan.littlegame.cocos.apis

import com.google.gson.JsonObject
import com.huiwan.base.ktx.isNotEmpty
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.toTrackMap
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.lib.api.DataCallback
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.platform.ThreadUtil
import org.json.JSONObject

/**
 * 货币不足弹窗
 */
class CocosApiShowNotEnoughAlert : BaseCocosApi("ShowNotEnoughAlert") {
    override fun handleSync(jsonArg: JsonObject): CocosApiResult {
        val currencyType = JsonUtil.getInt(jsonArg, "currency_type", 0)
        val trackDataJe = jsonArg.get("track_data")
        val trackDataMap = if (trackDataJe != null && trackDataJe.isJsonObject) {
            trackDataJe.asJsonObject.toTrackMap()
        } else {
            emptyMap()
        }.toMutableMap()
        // 再取一下gameid和refer_screen_name双重保险吧，也不知道到这两个字段在不在track_data里
        if (!trackDataMap.containsKey("refer_screen_name")) {
            JsonUtil.getString(jsonArg, "refer_screen_name", "").let {
                if (it.isNotEmpty()) {
                    trackDataMap["refer_screen_name"] = it
                }
            }
        }
        if (!trackDataMap.containsKey("gameid")) {
            JsonUtil.getInt(jsonArg, "gameid", -1).let {
                if (it != -1) {
                    trackDataMap["gameid"] = it
                }
            }
        }
        val api = HwApi::class.impl()
        if (currencyType == GameConfig.CURRENCY_COIN) {
            api.showCoinNotEnoughDialog(true, trackDataMap)
        } else {
            api.showGameChipExchangeDialog(-1, -1, trackDataMap)
        }
        return CocosApiResult.ok()
    }
}

class CocosInnerPurchase : BaseCocosApi("Purchase") {

    override fun handleAsync(jsonArg: JsonObject, callback: (CocosApiResult) -> Unit) {
        try {
            val goodsId = JsonUtil.getInt(jsonArg, "goods_id")
            ThreadUtil.runOnUiThread {
                val json = JSONObject().apply { put("goods_id", goodsId) }
                HwApi::class.impl().showBuyDialog(jsonArg.toString(), object : DataCallback<Int> {
                    override fun onCall(data: Int) {
                        callback(CocosApiResult.ok(mapOf("data" to json)))
                    }

                    override fun onFailed(code: Int, msg: String) {
                        callback(CocosApiResult.failed(msg))
                    }
                })
            }
        } catch (e: Exception) {
            callback(CocosApiResult.failed("parse json failed:${e.message}"))
        }
    }
}