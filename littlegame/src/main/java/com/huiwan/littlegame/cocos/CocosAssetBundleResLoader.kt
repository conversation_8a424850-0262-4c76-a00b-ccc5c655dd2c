package com.huiwan.littlegame.cocos

import android.util.Log
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import androidx.annotation.WorkerThread
import com.huiwan.base.util.FileUtil
import com.huiwan.littlegame.cocos.CocosAssetBundleResLoader.CACHE_CONFIG_FILE_NAME
import com.huiwan.littlegame.cocos.CocosAssetBundleResLoader.baseFolder
import com.huiwan.store.file.FileCacheName
import com.huiwan.store.file.FileConfig
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.download.DownloadCallback
import com.wepie.download.DownloadUtil
import com.wepie.download.Downloader
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import com.wepie.webview.intercept.WebAutoCloseInputStream
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.util.Properties
import java.util.concurrent.ConcurrentHashMap

/**
 * 新增 asset bundle loader
 * 从 cdn 下载 zip。 解压到指定位置。
 * cocos 通过 asset_bundle 加载对应的数据。分文件进行请求
 *
 * 是 asset_bundle, 则拦截，并返回响应值。
 * cocos 游戏资源代理支持 asset_bundle.
 *
 * 格式为
 * http://asset_bundle/<prop_id>/<file_name>
 *     由 wpjk 调整回 http. 一台安卓6的手机使用 fetch, xmlHttpRequest 时不支持 wpjk.
 *     但 img 这样的标签里的 src 可以支持 wpjk
 * 适配到本地文件
 * [baseFolder]/<prop_id>/<file_name>
 */
object CocosAssetBundleResLoader {
    private const val TAG = "JackarooChessAssetRes"
    private const val REQ_SCHEMA = "http"
    private const val REQ_SCHEMA1 = "wpjk"
    private const val REQ_HOST = "asset_bundle"
    private const val CACHE_CONFIG_FILE_NAME = "__wp_ccs_cfg.properties"
    private const val MAX_BUFFERED_SIZE = 3000_000

    /**
     * 正在加载的 asset bundle
     * prop_id -> bundle_zip_url
     */
    private val loadingItems = ConcurrentHashMap<String, String?>()

    /**
     * <app_cache>/cocos_asset_bundle
     */
    private val baseFolder by lazy {
        // cache 目录可能会被系统删除，这里的缓存会存在内容和记录的配置对不上的问题，需要调整到 file 目录下。
        // 目录采用直接替换的方式，不做额外的兼容处理，
        // 如果要完整处理，需要兼容的场景太多
        // 问题则是部分用户可能存在一个没有被删除的 cache 目录。 以及多耗费一次皮肤流量加载问题。
        val f = File(FileConfig.getGameFolder(), "cocos_asset_bundle")
        FileUtil.mkdir(f.absolutePath)
        f
    }

    /**
     * <app_cache>/cocos_asset_bundle/__wp_ccs_cfg.properties
     */
    private val cfgProperties by lazy {
        val properties = Properties()
        initCfgProp(properties)
        properties
    }

    /**
     * 5.0.5 后更新, 是 asset_bundle, 则拦截，并返回响应值。
     * cocos 游戏资源代理支持 asset_bundle.
     * 格式为 (wpjk|http)://asset_bundle/<prop_id>/<file_name>
     * 适配到本地文件 [baseFolder]/<prop_id>/<file_name>
     */
    fun interceptWebRequest(request: WebResourceRequest): WebResourceResponse? {
        val url = request.url
        if (url.host != REQ_HOST) {
            // 不满足规则，不处理。不再限定为 wpjk 开头。
            return null
        }
        HLog.d(TAG, "req url start {}", url)
        val path = url.path ?: return null
        val file = if (path.startsWith("/")) {
            File(baseFolder.absolutePath + path)
        } else {
            File(baseFolder.absolutePath + File.separator + path)
        }
        if (!file.exists()) {
            // 找不到本地对应的文件，可能 zip 还未下载好。也可能解压失败了。
            HLog.d(TAG, HLog.USR, "req url not exists {}", url)
            return null
        }

        val len = file.length()
        val stream: InputStream = if (len < MAX_BUFFERED_SIZE) {
            // max buf 以下，使用缓存 buffer. 过大时，还是使用本身文件访问。
            WebAutoCloseInputStream.create(url.toString(), file.inputStream())
        } else {
            file.inputStream()
        }
        val rsp = WebResourceResponse("application/octet-stream", "", stream)
        rsp.responseHeaders = genCorsHeader()
        rsp.setStatusCodeAndReasonPhrase(200, "OK")
        HLog.d(TAG, "req url finished {}", url)
        return rsp
    }

    /**
     * 如果传入的名字符合 asset bundle uri ，则将其转换为文件路径
     * 如果不符合 asset bundle uri 的格式，则返回 null
     */
    @WorkerThread
    fun mapUriToFilePath(uri: String): String? {
        val prefix = "$REQ_SCHEMA://$REQ_HOST"
        if (uri.startsWith(prefix)) {
            return uri.replace(prefix, baseFolder.absolutePath)
        }
        val prefix2 = "$REQ_SCHEMA1://$REQ_HOST"
        if (uri.startsWith(prefix2)) {
            return uri.replace(prefix2, baseFolder.absolutePath)
        }
        return null
    }

    /**
     * 缓存内容并解压
     * 如果已缓存 ok, 直接返回，否则下载并缓存.
     * 如果正在缓存，此时也返回失败
     * @throws IOException::class 文件操作失败时抛出异常
     * @param force 是否强制加载
     * @param callback
     * return "zip" 代表成功， null 代表失败
     */
    @Throws(IOException::class)
    @WorkerThread
    suspend fun loadZipResItem(
        bundleName: String,
        resZipUrl: String,
        itemLogTag: String,
        force: Boolean = false,
        callback: DownloadCallback? = null
    ): String? {
        val properties = cfgProperties
        val cacheUrl = properties.getProperty(bundleName)
        if (cacheUrl == resZipUrl && !force) {
            // 已缓存对应的数据
            HLog.d(
                TAG, HLog.USR, "cacheItem {}[{}]: hit cache {}",
                itemLogTag, bundleName, resZipUrl
            )
            callback?.onSuccess(resZipUrl, "")
            return "zip"
        }
        val loading = loadingItems[bundleName]
        if (loading != null) {
            // 正在加载。此时直接返回失败
            callback?.onFail("already loading")
            HLog.d(TAG, HLog.USR, "already loading {} {}", loading, resZipUrl)
            return null
        }
        loadingItems[bundleName] = resZipUrl
        // 缓存失效时，先清理 cfg 及文件夹
        properties.setProperty(bundleName, "")
        try {
            val targetBaseFolder = File(baseFolder, bundleName)
            FileUtil.deleteDirectory(targetBaseFolder)
            FileUtil.deleteFile(
                DownloadUtil.getCacheFile(
                    resZipUrl, FileConfig.getOtherFileFullPath(FileCacheName.DOWN_DIR)
                )
            )
            FileUtil.mkdir(targetBaseFolder.absolutePath)
            downloadAndUnzip(resZipUrl, targetBaseFolder.absolutePath, callback)
            properties.setProperty(bundleName, resZipUrl)
            return "zip"
        } catch (e: Exception) {
            HLog.d(TAG, HLog.USR, "error {}", e)
            HLog.aliLog(
                AliNetLogUtil.PORT.HttpConfig, AliNetLogUtil.TYPE.err,
                "ErrorLoadCocosAssetZipResItem ($bundleName) [$resZipUrl]: ${
                    Log.getStackTraceString(e)
                }"
            )
            callback?.onFail(e.message)
            return null
        } finally {
            // 当次加载结束
            storeCfgProp()
            loadingItems.remove(bundleName)
        }
    }

    /**
     * 将 @param url 的 zip 文件下载下来，并解压到 @param targetUnzipDir 目录下。
     * 下载并解压成功，返回 Result.success(targetUnzipDir) 路径。
     * 否则返回 Result.failure(<失败原因>)
     */
    private suspend fun downloadAndUnzip(
        url: String, targetUnzipDir: String, callback: DownloadCallback? = null
    ) {
        val result = suspendCancellableCoroutine {
            Downloader.newBuilder()
                .setUrl(url)
                .setRetryTimes(3)
                .download(object : DownloadCallback {
                    override fun onSuccess(url: String?, path: String?) {
                        HLog.d(TAG, HLog.USR, "onSuccess {}", path)
                        it.resumeWith(Result.success(path ?: ""))
                    }

                    override fun onPercent(percent: Int, cur: Long, total: Long) {
                        super.onPercent(percent, cur, total)
                        callback?.onPercent(percent, cur, total)
                    }

                    override fun onFail(msg: String?) {
                        HLog.d(TAG, HLog.USR, "error load asset bundle {}", msg)
                        it.resumeWith(Result.failure(IOException(msg ?: "UnknownError")))
                    }
                })
        }

        if (result.isEmpty()) {
            callback?.onFail("path is null")
            return
        }

        try {
            FileInputStream(result).use { fis ->
                FileUtil.unpackSync(fis, targetUnzipDir)
            }
            HLog.d(
                TAG, HLog.USR,
                "preload asset bundle success {}", targetUnzipDir,
            )
            callback?.onSuccess(url, result)
        } catch (e: Exception) {
            FLog.e(e)
            callback?.onFail(e.toString())
        }
    }

    /**
     * 初始化 asset bundle prop, 文件 [baseFolder]/[CACHE_CONFIG_FILE_NAME]
     */
    private fun initCfgProp(properties: Properties) {
        val propFile = propFile()
        if (propFile.exists()) {
            try {
                propFile.inputStream().use {
                    properties.load(it)
                }
            } catch (e: Exception) {
                FLog.e(RuntimeException("CocosInitProp", e))
            }
        }
    }

    /**
     * 持久化 asset bundle prop
     */
    @WorkerThread
    private fun storeCfgProp() {
        try {
            val propFile = propFile()
            FileUtil.createFile(propFile)
            if (propFile.exists()) {
                propFile.outputStream().use {
                    cfgProperties.store(it, "")
                }
            }
        } catch (e: Exception) {
            FLog.e(RuntimeException("CocosInitProp", e))
        }
    }

    private fun propFile(): File {
        return File(baseFolder, CACHE_CONFIG_FILE_NAME)
    }


    /**
     * 支持跨域的 header
     */
    private fun genCorsHeader(): Map<String, String> {
        return mapOf(
            "access-control-allow-origin" to "*",
            "timing-allow-origin" to "*"
        )
    }

    /**
     * @return 是否已缓存 propId 对应的 asset bundle 资源
     */
    @WorkerThread
    fun hasCache(bundleName: String): Boolean {
        val result = cfgProperties.getProperty(bundleName)
        return !result.isNullOrEmpty()
    }

    @WorkerThread
    fun reset() {
        if (!propFile().exists()) {
            cfgProperties.clear()
        }
    }
}