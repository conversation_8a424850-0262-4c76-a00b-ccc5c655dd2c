package com.huiwan.littlegame.util

import com.huiwan.base.ActivityTaskManager
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.component.game.chip.bean.LittleGameBankruptcyProtection
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.ILittleGameMatchInfo
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.impl
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.littlegame.EXCHANGE_TYPE_MATCH
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_REPLAY
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wepie.lib.api.plugins.track.TrackApi
import com.wepie.wespy.model.entity.match.TeamInfo
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.json.JSONObject

object LittleGameUtil {

    @JvmStatic
    fun showChipProtectionDialog(jsonObject: JSONObject) {
        val activity = ActivityTaskManager.getInstance().topActivity ?: return

        val gameType = jsonObject.getInt("game_type")
        val betLevel = jsonObject.getInt("bet_level")
        val mode = jsonObject.getInt("mode") //可能是roomMode 也可能是matchMode

        val gameMode = jsonObject.getInt("game_mode")
        val data = LittleGameBankruptcyProtection()
        data.toast = true
        data.toastText = jsonObject.getString("toast_text")
        data.usedProtectionTimes = jsonObject.getInt("used_protection_times")
        data.totalProtectionTimes = jsonObject.getInt("total_protection_times")
        data.receiveChip = jsonObject.getLong("each_time_recv_chips")
        val info =
            LittleGameSimpleInfo(gameType, mode, gameMode, betLevel, GameConfig.CURRENCY_CHIP)

        ApiService.of(ChipApi::class.java).showProtectionDialog(activity, info, data)
    }

    @JvmStatic
    fun showChipExchangeDialog(jsonObject: JSONObject) {
        val activity = ActivityTaskManager.getInstance().topActivity ?: return

        val gameType = jsonObject.getInt("game_type")
        val betLevel = jsonObject.getInt("bet_level")
        val mode = jsonObject.getInt("mode") //可能是roomMode 也可能是matchMode

        val gameMode = jsonObject.getInt("game_mode")
        val gameid = jsonObject.optInt("gameid", -1)
        val referScreenName = jsonObject.optString("refer_screen_name", "")
        val info = LittleGameSimpleInfo(gameType, mode, gameMode, betLevel, GameConfig.CURRENCY_CHIP)
        info.gameid = gameid
        info.referScreenName = referScreenName

        ApiService.of(ChipApi::class.java).handleChipNotEnough(
            activity, GAME_SCENE_LITTLE_GAME_REPLAY, EXCHANGE_TYPE_MATCH, info
        ) {
            //do nothing
        }
    }

    @JvmStatic
    fun showChipLimitDialog(jsonObject: JSONObject, next: () -> Unit) {
        val activity = ActivityTaskManager.getInstance().topActivity ?: return
        val chip = jsonObject.getInt("chip_value")
        val gameType = jsonObject.getInt("game_type")
        val betLevel = jsonObject.getInt("bet_level")
        val mode = jsonObject.getInt("mode") //可能是roomMode 也可能是matchMode
        val gameMode = jsonObject.getInt("game_mode")
        val info =
            LittleGameSimpleInfo(gameType, mode, gameMode, betLevel, GameConfig.CURRENCY_CHIP)
        MainScope().launch {
            ApiService.of(ChipApi::class.java).handleChipLimit(activity, info)?.collectLatest {
                next.invoke()
            }
        }
    }

    fun trackMatchGameError(code: Int, gameType: Int, matchInfo: ILittleGameMatchInfo) {
        trackGameError(code, gameType, matchInfo.gameMode, matchInfo.matchMode, matchInfo.betLevel)
    }

    /**
     * 创房页
     */
    fun trackCreateGameError(code: Int, gameSimpleInfo: LittleGameSimpleInfo) {
        trackGameError(
            code, gameSimpleInfo.gameType, gameSimpleInfo.gameMode, -1, gameSimpleInfo.betLevel
        )
    }

    fun trackGameError(code: Int, gameType: Int, gameMode: Int, mode: Int, betLevel: Int) {
        val type = when (code) {
            RspHeadInfo.ERROR_CODE_CHIP_NOT_ENOUGH -> 1
            RspHeadInfo.ERROR_CODE_CHIP_REACH_MAX_LIMIT -> 2
            else -> -1
        }
        if (type < 0) {
            return
        }
        val localMode = if (gameMode > GameConfig.GAME_MODE_VIP_BASE) {
            gameMode - GameConfig.GAME_MODE_VIP_BASE
        } else {
            gameMode
        }
        val trackExt = mapOf<String, Any>(
            "type" to type,
            "game_type" to gameType,
            "mode_type" to "${gameType}${localMode}",
            "bet_level" to betLevel,
            "match_mode" to TeamInfo.trackMatchMode(gameMode, mode)
        )
        TrackApi::class.impl().trackEvent("InterceptEnter", trackExt)
    }
}