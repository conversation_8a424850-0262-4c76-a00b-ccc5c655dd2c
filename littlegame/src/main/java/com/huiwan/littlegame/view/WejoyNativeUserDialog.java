package com.huiwan.littlegame.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.ConstV3Info;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.GameType;
import com.huiwan.decorate.CharmManager;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.JackarooLevelView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.decorate.UserIdView;
import com.huiwan.decorate.vip.VipLabelView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.lib.api.plugins.friend.AddFriendApi;
import com.huiwan.littlegame.R;
import com.huiwan.littlegame.util.CommonUtil;
import com.huiwan.user.LifeUserInfoLoadCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserGameInfo;
import com.huiwan.user.http.UserApi;
import com.huiwan.widget.FamilyLightView;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackEvent;
import com.wepie.libimageloader.WpImageLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by bigwen on 2017/11/14.
 */
public class WejoyNativeUserDialog extends LinearLayout {

    private final Context mContext;
    private NameTextView mNickTv;
    private UserIdView mUserIdView;
    private TextView seatLocationTx;
    private TextView seatDistanceTx;
    private LinearLayout mAddFriendLay;
    private TextView mSendGiftBt;
    private TextView mAddFriendBt;
    private TextView mFlirtBt;
    private DecorHeadImgView mHeadImage;
    private ImageView seatGenderIcon;
    private TextView playTimes;
    private TextView winRate;
    private View gradeLay;
    private View gamesLay;
    private View winRateLay;
    private View divider1;
    private ImageView gradeIv;
    private View starLay;
    private TextView starTv;
    private Callback callback;
    private int gameType;
    private TextView levelExplainTv;
    private ImageView charmIv;
    private VipLabelView vipLabelView;
    private JackarooLevelView levelView;
    private ChessHolder mChessHolder;
    private ViewGroup mSeatOperateView;
    private int thisUid;
    private View reportView;
    private FamilyLightView familyLightView;
    private ViewGroup medalLay;
    private IMedalApi.IMedalListView medalListView;
    public static int SCENE_HOMEPAGE = 1; // HomePage
    public static int SCENE_CREATE_ROOM_PAGE = 2; // create room
    public static int SCENE_LITTLE_GAME_UNITY = 3; // unity小游戏
    private int scene = -1;
    private boolean isTrack = false;
    private OnClickListener jumpUserClickListener; // 点击头像时的拦截 click. 可用于拦截或者调整跳转逻辑

    public WejoyNativeUserDialog(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public WejoyNativeUserDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.wejoy_ice_ball_user_dialog, this);
        mNickTv = findViewById(R.id.ice_ball_nick);
        mUserIdView = findViewById(R.id.user_id_view);
        seatLocationTx = findViewById(R.id.seat_location_tx);
        seatDistanceTx = findViewById(R.id.seat_distance_tx);
        mAddFriendLay = findViewById(R.id.ice_ball_add_friend_lay);
        mSendGiftBt = findViewById(R.id.ice_ball_send_gift_bt);
        mAddFriendBt = findViewById(R.id.ice_ball_add_friend_bt);
        mFlirtBt = findViewById(R.id.ice_ball_flirt_bt);
        mHeadImage = findViewById(R.id.ice_ball_head_image);
        seatGenderIcon = findViewById(R.id.gender_iv);
        playTimes = findViewById(R.id.ice_ball_play_times);
        winRate = findViewById(R.id.ice_ball_win_rate);
        levelExplainTv = findViewById(R.id.level_explain_tv);
        charmIv = findViewById(R.id.charm_iv);
        vipLabelView = findViewById(R.id.vip_label);
        levelView = findViewById(R.id.jackaroo_level_view);
        reportView = findViewById(R.id.seat_dialog_report_btn);
        mHeadImage.setBorderWidth(ScreenUtil.dip2px(2f));
        mHeadImage.setBorderColor(-0x1);
        familyLightView = findViewById(R.id.family_light_view);
        medalLay = findViewById(R.id.medal_lay);
        gradeLay = findViewById(R.id.grade_lay);
        gamesLay = findViewById(R.id.games_lay);
        winRateLay = findViewById(R.id.win_rate_lay);
        divider1 = findViewById(R.id.divider1);
        gradeIv = findViewById(R.id.grade_iv);
        starLay = findViewById(R.id.star_lay);
        starTv = findViewById(R.id.star_tv);
        mChessHolder = new ChessHolder(this);
        mSeatOperateView = findViewById(R.id.seat_dialog_container);
    }

    public static void fixLinearLayoutSpacing(LinearLayout linearLayout, int dpSpacing) {
        int childCount = linearLayout.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = linearLayout.getChildAt(i);
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) child.getLayoutParams();

            // 首个 item 左边距 = 0
            lp.setMarginStart(i == 0 ? 0 : dpToPx(linearLayout.getContext(), dpSpacing));
            // 右边距 = 0（只留中间空隙）
            lp.setMarginEnd(0);

            child.setLayoutParams(lp);
        }
    }

    /**
     * dp 转 px
     */
    public static int dpToPx(Context context, int dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return (int) (dp * density + 0.5f);
    }


    private boolean isCooperateMode() {
        return ConfigHelper.getInstance().getGameConfig(gameType).getPlayMode() == GameConfig.PLAY_MODE_COOPERATE;
    }

    public void update(int uid, int gameType, int rid) {
        update(uid, gameType, null, rid, false, true);
    }

    public void setJumpUserClickListener(OnClickListener jumpUserClickListener) {
        this.jumpUserClickListener = jumpUserClickListener;
    }

    /**
     * @param uid
     * @param gameType
     * @param rid
     * @param fromVoiceRoom 弹窗是否来自于语音房，举报按钮点击后 ，举报来源是否 需要是语音房
     */
    public void update(int uid, int gameType, View seatOperateView, int rid, boolean fromVoiceRoom, boolean isGameSeat) {
        this.thisUid = uid;
        this.gameType = gameType;

        AtomicBoolean flag = new AtomicBoolean(false);
        UserService.get().getCacheUserLocal(uid, new LifeUserInfoLoadCallback(this) {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                if (!flag.get()) {
                    refresh(userInfo, seatOperateView, gameType, rid, isGameSeat);
                }
            }
        });
        UserService.get().getCacheUserFromServer(uid, new LifeUserInfoLoadCallback(this) {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                flag.set(true);
                refresh(userInfo, seatOperateView, gameType, rid, isGameSeat);
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
            }
        });
        UserApi.getUserGameInfo(gameType, uid, new LifeDataCallback<UserGameInfo>(this) {
            @Override
            public void onSuccess(Result<UserGameInfo> result) {
                playTimes.setText(result.data.getPlayCount());

                if (isCooperateMode()) {
                    winRate.setText(result.data.getMostLevel());
                } else if (gameType == GameType.GAME_TYPE_SHEEP) {
                    if (result.data.getTodayPassTime() == 0) {
                        winRate.setText(ResUtil.getStr(R.string.cocos_sheep_unpassed));
                    } else {
                        winRate.setText(TimeUtil.getMinuteSecond(result.data.getTodayPassTime()));
                    }
                } else {
                    winRate.setText(result.data.getWinRatio());
                }
            }

            @Override
            public void onFail(int code, String msg) {

            }
        });
        updateWinText();
        reportView.setVisibility(uid == CommonUtil.getLoginUid() ? View.INVISIBLE : View.VISIBLE);
        reportView.setOnClickListener(v -> {
            if (fromVoiceRoom) {
                ApiService.of(HwApi.class).gotoReportVoiceRoomUserActivity(getContext(), uid, gameType, rid);
            } else {
                ApiService.of(HwApi.class).gotoReportGameUserActivity(getContext(), uid, gameType, rid);
            }
            if (callback != null) {
                callback.onDismiss();
            }
        });
    }

    private void updateGradeInfo(final User userInfo) {
        int star = userInfo.getQfStar();
        ConstV3Info.QualifyingGrade qfGrade = ConfigHelper.getInstance().getConstV3Info().findQFGrade(userInfo.getQfGrade());
        if (qfGrade == null || TextUtil.isEmpty(qfGrade.icon)) {
            gradeIv.setImageDrawable(ResUtil.getDrawable(R.drawable.grade_no_qualifying));
        } else {
            WpImageLoader.load(qfGrade.icon, gradeIv);
        }
        if (star > 0) {
            starLay.setVisibility(VISIBLE);
            starTv.setText(String.valueOf(star));
        } else {
            starLay.setVisibility(GONE);
        }
    }

    private void updateWinText() {
        if (isCooperateMode()) {
            levelExplainTv.setText(ResUtil.getStr(R.string.cocos_native_dialog_highest_level));
        } else if (gameType == GameType.GAME_TYPE_SHEEP) {
            levelExplainTv.setText(ResUtil.getStr(R.string.cocos_native_dialog_highest_score));
        } else {
            levelExplainTv.setText(ResUtil.getStr(R.string.win_rate));
        }
    }

    private void refresh(final User userInfo, View seatOperateView, final int gameType, final int rid, boolean isGameSeat) {
        mHeadImage.showUserHeadWithDecoration(userInfo.uid);
        mNickTv.setUserName(userInfo);
        mUserIdView.setUser(userInfo);
        if (seatOperateView != null) {
            ViewParent parent = seatOperateView.getParent();
            if (parent instanceof ViewGroup) {
                ((ViewGroup) parent).removeView(seatOperateView);
            }
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
            mSeatOperateView.addView(seatOperateView, lp);
        }
        if (gameType == GameType.GAME_TYPE_JACKAROO) {
            mChessHolder.updateChessUI(userInfo);
        }
        if (!isGameSeat) {
            mFlirtBt.setVisibility(VISIBLE);
        }
        if (userInfo.hasGender()) {
            seatGenderIcon.setVisibility(VISIBLE);
            seatGenderIcon.setImageResource(userInfo.isMale() ? R.drawable.icon_male : R.drawable.icon_female);
        } else {
            seatGenderIcon.setVisibility(GONE);
        }

        seatLocationTx.setVisibility(GONE);
//        if (TextUtil.isEmpty(userInfo.getAreaName())) {
//            seatLocationTx.setText(R.string.unknown_region);
//        } else {
//            seatLocationTx.setText(userInfo.getAreaName());
//        }

        updateGradeInfo(userInfo);
        if (userInfo.uid == CommonUtil.getLoginUid()) {
            seatDistanceTx.setVisibility(GONE);
        } else {
            float dist = userInfo.getDist();
            if (dist >= 0) {
                seatDistanceTx.setVisibility(VISIBLE);
                seatDistanceTx.setText(" · " + StringUtil.formDist(dist));
            } else {
                seatDistanceTx.setVisibility(GONE);
            }
        }

        ApiService.of(AddFriendApi.class).isFriend(userInfo.uid, data -> {
            if (data || CommonUtil.getLoginUid() == userInfo.uid) {
                showIsFriend();
            } else {
                showNotFriend();
            }
        });

        mAddFriendBt.setOnClickListener(view -> {
            if (callback != null) callback.onAddFriend(userInfo.uid, userInfo.add_friend_price);
        });

        mSendGiftBt.setOnClickListener(v -> {
            if (callback != null) callback.onSendGift(userInfo.uid);
        });

        mHeadImage.setOnClickListener(v -> {
            if (isCooperateMode()) return;
            if (jumpUserClickListener != null) {
                jumpUserClickListener.onClick(v);
                return;
            }
            ApiService.of(HwApi.class).gotoUserInfoDetailActivityFromGame(getContext(), userInfo.uid, gameType, null);
            callback.onDismiss();
        });
        CharmManager.checkShowShortCharm(charmIv, userInfo.getFlower());
        vipLabelView.setVipLevel(userInfo.getVip());
        levelView.setLevel(userInfo.getLevel());
        PressUtil.addPressEffect(mAddFriendBt);
        PressUtil.addPressEffect(mSendGiftBt);

        userInfo.familyLightInfo.initPropInfo();
        familyLightView.updateFamilyLightView(userInfo.familyLightInfo, false);
        showMedalIcon(userInfo.wearMedals);
        trackViewUser(gameType, rid);
    }


    private void trackViewUser(int gameType, int rid) {
        if (isTrack) {
            return;
        }
        isTrack = true;
        int level = LittleGame.getGameInfo().getBetLevel();
        Map<String, Object> map = new HashMap<>();
        map.put("game_type", gameType);
        map.put("to_uid", thisUid);
        map.put("rid", rid);
        map.put("has_avatar", 0);
        if (level >= 0) {
            map.put("mode_type", String.valueOf(gameType) + level);
        }
        TrackUtil.trackEvent(TrackEvent.VIEW_USER_CARD, map);
    }

    private void showIsFriend() {
        if (ScreenUtil.isLandScape(mContext)) {
            showIsFriendNotSendGift();
        } else {
            showSendGift();
        }
    }

    private void showNotFriend() {
        if (ScreenUtil.isLandScape(mContext)) {
            showAddFriend();
        } else {
            showAddFriendWithSendGift();
        }
    }

    private void showSendGift() {
//        mSendGiftBt.getLayoutParams().width = RelativeLayout.LayoutParams.MATCH_PARENT;
        mAddFriendLay.setVisibility(VISIBLE);
        fixLinearLayoutSpacing(mAddFriendLay, 8);
        mAddFriendBt.setVisibility(GONE);
        setSendGiftBt(false, VISIBLE, GONE);
        refreshFriendBtn(true);
    }

    private void showIsFriendNotSendGift() {
        mAddFriendBt.getLayoutParams().width = RelativeLayout.LayoutParams.MATCH_PARENT;
        mAddFriendLay.setVisibility(VISIBLE);
        fixLinearLayoutSpacing(mAddFriendLay, 8);
        mAddFriendBt.setVisibility(VISIBLE);
        setSendGiftBt(false, GONE, GONE);
        if (thisUid == LoginHelper.getLoginUid()) {
            mAddFriendLay.setVisibility(GONE);
        } else {
            refreshFriendBtn(false);
        }
    }

    private void showAddFriend() {
        mAddFriendBt.getLayoutParams().width = RelativeLayout.LayoutParams.MATCH_PARENT;
        mAddFriendLay.setVisibility(VISIBLE);
        fixLinearLayoutSpacing(mAddFriendLay, 8);
        mAddFriendBt.setVisibility(VISIBLE);
        setSendGiftBt(true, GONE, GONE);
        refreshFriendBtn(true);
    }


    private void showAddFriendWithSendGift() {
//        mAddFriendBt.getLayoutParams().width = ScreenUtil.dip2px(124);
//        mSendGiftBt.getLayoutParams().width = ScreenUtil.dip2px(124);
        mAddFriendLay.setVisibility(VISIBLE);
        fixLinearLayoutSpacing(mAddFriendLay, 8);
        setSendGiftBt(true, VISIBLE, VISIBLE);
        mAddFriendBt.setVisibility(VISIBLE);
        refreshFriendBtn(true);
    }

    private void setSendGiftBt(boolean showAddFriend, int giftVisibilty, int centerVisibilty) {
        // 羊了个羊 主页屏蔽送礼按钮
        if (gameType == GameType.GAME_TYPE_SHEEP && scene == SCENE_HOMEPAGE) {
            giftVisibilty = GONE;
            centerVisibilty = GONE;
            if (!showAddFriend) {
                mAddFriendLay.setVisibility(GONE);
            }
        }
//        centerView.setVisibility(centerVisibilty);
        mSendGiftBt.setVisibility(giftVisibilty);
    }


    private void refreshFriendBtn(boolean addFriend) {
        if (addFriend) {
            mAddFriendBt.setText(R.string.common_add_friends);
            mAddFriendBt.setTextColor(Color.WHITE);
            mAddFriendBt.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
            mAddFriendBt.setEnabled(true);
        } else {
            mAddFriendBt.setBackground(null);
            mAddFriendBt.setText(R.string.common_was_friends);
            mAddFriendBt.setTextColor(ResUtil.getColor(R.color.color_text_tertiary));
            mAddFriendBt.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            mAddFriendBt.setEnabled(false);
        }
    }

    public void refreshAddFriendSuccess() {
        mAddFriendLay.setVisibility(View.GONE);
        showIsFriend();
    }

    private void showMedalIcon(List<IMedalApi.WearInfo> wearMedals) {
        if (wearMedals.isEmpty()) {
            medalLay.setVisibility(View.GONE);
            return;
        }
        medalLay.setVisibility(View.VISIBLE);
        if (medalListView == null) {
            medalListView = ApiService.of(IMedalApi.class).genMedalListView(mContext);
            if (medalListView != null) {
                View line = new View(mContext);
                line.setBackgroundResource(R.color.color_text_disabled);
                ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ScreenUtil.dip2px(1F), ScreenUtil.dip2px(10F));
                medalLay.addView(line, params);
                ViewUtil.setLeftMargins(line, ScreenUtil.dip2px(2F));
                View view = medalListView.getView();
                medalLay.addView(view);
                ViewUtil.setLeftMargins(view, ScreenUtil.dip2px(2F));
                medalListView.updateIconSize(ScreenUtil.dip2px(22f));
            }
        }
        if (medalListView != null) {
            medalListView.updateMedalList(wearMedals);
        }
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onAddFriend(int uid, int addPrice);

        void onSendGift(int uid);

        void onDismiss();
    }

    public void setScene(int scene) {
        this.scene = scene;
    }
}