package com.huiwan.littlegame.view

import android.view.View
import android.view.View.VISIBLE
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.ktx.dp
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.ChessSkinShowView
import com.huiwan.littlegame.R
import com.huiwan.user.entity.User
import com.huiwan.widget.SimpleOutlineProvider


class ChessHolder(root: View) {

    private val chessLay: View = root.findViewById(R.id.main_chess_view)
    private val collectionTv: TextView = root.findViewById(R.id.chess_collection_tv)
    private val chessBg: ImageView = root.findViewById(R.id.chess_bg)
    private val chessIv: ChessSkinShowView = root.findViewById(R.id.chess_iv)

    init {
        chessBg.outlineProvider = SimpleOutlineProvider(12f.dp.toFloat())
    }

    fun updateChessUI(user: User) {
        chessLay.visibility = VISIBLE
        collectionTv.text = user.collectionInfo.collectionValue.toString()
        val propItem = ConfigHelper.getInstance().propConfig
            .getPropItem(user.collectionInfo.getInUseChessPropId())
        chessIv.bind(propItem)
    }
}