package com.huiwan.component.gift.send.ar

import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.huiwan.base.util.NinePatchUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.component.gift.R
import com.huiwan.configservice.model.gift.Gift
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.user.UserService
import com.huiwan.user.entity.User
import com.wejoy.weplay.ex.lifecycle.observe
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName

class GiftViewArBottom(private val giftViewAr: GiftViewAr) {
    private val sendTvBgIv = giftViewAr.findViewById<ImageView>(R.id.send_tv_bg_iv)
    private val sendTv = giftViewAr.findViewById<TextView>(R.id.send_tv)
    private val sendNumTv = giftViewAr.findViewById<TextView>(R.id.send_num_tv)
    private val sendNumIv = giftViewAr.findViewById<ImageView>(R.id.send_num_iv)
    private val sendNumLayBg = giftViewAr.findViewById<ImageView>(R.id.gift_num_bg_lay_bg_iv)
    private val giftNumLay = giftViewAr.findViewById<ViewGroup>(R.id.gift_num_bg_lay)
    private val context = giftViewAr.context
    private val coinIv = giftViewAr.findViewById<ImageView>(R.id.coin_iv)
    private val coinTv = giftViewAr.findViewById<TextView>(R.id.coin_tv)
    private val coinAddIv = giftViewAr.findViewById<ImageView>(R.id.coin_add_iv)
    private val privateView =
        giftViewAr.findViewById<GiftBottomPrivateView>(R.id.bottom_private_view)
    private val listener = View.OnClickListener { checkClick(it) }

    private var canSendGift = false
    private var selectedGift: Gift? = null

    init {
        initGiftNumBg()
    }

    private fun initGiftNumBg() {
        coinTv.setOnClickListener(listener)
        coinAddIv.setOnClickListener(listener)
        sendTv.setOnClickListener(listener)
        giftNumLay.setOnClickListener(listener)

        canSend(true)

        UserService.get().selfUser.observe(giftViewAr.toLife()) {
            updateSelfCoinNum(it)
        }
        coinTv.text = UserService.get().selfUser.value?.coin?.toString()
    }

    private fun checkClick(v: View) {
        when (v) {
            coinAddIv, coinTv -> {
                val giftShowConfig = giftViewAr.config
                if (giftShowConfig != null && !giftShowConfig.portrait) {
                    ApiService.of(HwApi::class.java).gotoHorizontalPay(
                        context,
                        giftShowConfig.subSource,
                        giftShowConfig.gameType
                    )
                    // Jackaroo 没有单独的横屏游戏，暂不处理。
                } else {
                    val subCoinFirst = selectedGift?.isChipGift == true
                    ApiService.of(HwApi::class.java).gotoGoodsListActivity(context, subCoinFirst, mapOf("refer_screen_name" to TrackScreenName.PAGE_VOICE_SEND_GIFT))
                }
            }

            giftNumLay -> {
                sendNumIv.scaleY = 1F
                giftViewAr.onClickNum(sendNumTv)
            }

            sendTv -> giftViewAr.onClickSend()
            else -> {}
        }
    }

    fun isPrivate(): Boolean = privateView.isVisible && privateView.isPrivate

    fun updateNum(giftNum: Int) {
        sendNumTv.text = giftNum.toString()
    }

    fun showPrivate(isVisible: Boolean) {
        privateView.isVisible = isVisible
    }

    fun canSend(hasPerson: Boolean) {
        if (canSendGift == hasPerson) {
            return
        }
        canSendGift = hasPerson
        sendTv.isEnabled = hasPerson
        giftNumLay.isEnabled = hasPerson
        val numBgId: Int
        val sendBgId: Int
        if (hasPerson) {
            numBgId = R.drawable.gift_num_bg
            sendBgId = R.drawable.gift_send_bg
        } else {
            numBgId = R.drawable.gift_no_num_bg
            sendBgId = R.drawable.gift_not_send_bg
        }
        sendNumLayBg.setImageDrawable(
            NinePatchUtil.createDefaultNinePatchDrawable(context, numBgId)
        )
        sendTvBgIv.setImageDrawable(
            NinePatchUtil.createDefaultNinePatchDrawable(context, sendBgId)
        )
    }

    fun hideNumSelect() {
        sendNumIv.scaleY = -1F
    }

    fun onGiftSelected(gift: Gift?) {
        selectedGift = gift
        gift?.let {
            val isChipGift = gift.isChipGift
            val coinRes = if (isChipGift) R.drawable.game_chip else R.drawable.wejoy_coin_icon
            coinIv.setImageResource(coinRes)
            updateSelfCoinNum(UserService.get().loginUser)
        }
    }

    private fun updateSelfCoinNum(self: User) {
        val isChipGift = selectedGift?.isChipGift == true
        coinTv.text = if (isChipGift) self.chipCoin.toString() else self.coin.toString()
    }

    fun getSendBtnRect(): Rect = ViewUtil.getRectInWindown(sendTvBgIv)
}