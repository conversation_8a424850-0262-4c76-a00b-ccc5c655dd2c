package com.weplay.competition.create

import android.text.format.DateUtils
import android.view.View
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.IMMHelper
import com.huiwan.base.util.TimeUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.uploader.BucketType
import com.wepie.lib.api.uploader.IUploadCallback
import com.wepie.lib.api.uploader.SimpleFileUploader
import com.wepie.liblog.main.HLog
import com.wepie.libphoto.PhotoCallback
import com.wepie.libphoto.PhotoUtil
import com.weplay.competition.R
import com.weplay.competition.databinding.ActivityCompetitionCreateBinding
import com.weplay.competition.dialog.CompetitionSetRewardDialog
import com.weplay.competition.dialog.SelectEventTimeDialog
import com.weplay.competition.trackCreateBtnClick
import java.util.Calendar

private const val TAG = "CompetitionCreate"

/**
 * 顶部标题区 banner 图的高宽比
 */
private const val TITLE_BANNER_ASPECT_RATIO = 210f / 375f

/**
 * 创建赛事的事件触发以及简单的事件的处理
 */
internal class CompetitionCreateEventListeners(
    private val binding: ActivityCompetitionCreateBinding,
    private val activity: CompetitionCreateActivity,
    private val vm: CompetitionCreateViewModel
) {

    fun listen() {
        val listener = View.OnClickListener {
            binding.competitionNameEt.clearFocus()
            onClick(it)
        }
        listOf(
            binding.bodyLay,
            binding.backIv,
            binding.gradeLimitNoneLay,
            binding.gradeLimitMinLay,
            binding.startNowLay,
            binding.scheduledStartLay,
            binding.player8Lay,
            binding.player16Lay,
            binding.player32Lay,
            binding.changeBgLay,
            binding.rewardFirstLay,
            binding.rewardSecondLay,
            binding.diamondLay,
            binding.createBtn,
            binding.changePwdLay
        ).forEach { it.setOnClickListener(listener) }

        binding.pwdSetBt.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                activity.launchSelectPwd()
            } else {
                binding.changePwdLay.visibility = View.GONE
                vm.onSetPassword("")
            }
        }
    }

    private fun onClick(v: View) {
        when (v) {
            binding.bodyLay -> {
                IMMHelper.hideSoftInputExt(binding.competitionNameEt)
            }

            binding.backIv -> {
                ContextUtil.finishActivity(v)
            }

            binding.diamondLay -> {
                ApiService.of(HwApi::class.java).gotoGoodsListActivity(activity, false, mapOf("refer_screen_name" to TrackScreenName.CREATE_COMPETITION_PAGE))
            }

            binding.rewardFirstLay -> {
                binding.competitionNameEt.clearFocus()
                CompetitionSetRewardDialog().showDialog(
                    activity,
                    CompetitionSetRewardDialog.REWARD_TAG_1,
                    vm.state.value.firstReward
                )
            }

            binding.rewardSecondLay -> {
                if (vm.state.value.firstReward < 0) {
                    ToastUtil.show(R.string.competition_create_set_first_reward)
                    return
                }
                binding.competitionNameEt.clearFocus()
                CompetitionSetRewardDialog().showDialog(
                    activity,
                    CompetitionSetRewardDialog.REWARD_TAG_2,
                    vm.state.value.secondReward
                )
            }

            binding.gradeLimitNoneLay -> {
                // 设置赛事无限制
                vm.onSetRankNoLimit()
            }

            binding.gradeLimitMinLay -> {
                // 产品希望简化一下交互，
                // 点击时直接切换为上次选的有限制的那个，默认为列表中的第一个。
                vm.onSetRankHasLimit()
                // 最小段位限制弹窗
                MinCompetitionRankView.showDialog(
                    activity,
                    vm.state.value.gradeLimit.grade
                ) { gradeInfo ->
                    vm.onSetRankGradeLimit(gradeInfo)
                }
            }

            binding.startNowLay -> {
                vm.onSetEventTimeNoLimit()
            }

            binding.scheduledStartLay -> {
                // 选择详细时间
                SelectEventTimeDialog.showDialog(
                    activity,
                    vm.state.value.eventStartTimestampMs,
                    object : TimeSelectListener {
                        override fun onFinish(hour: Int, min: Int): Result<Unit> {
                            return vm.onSetDetailEventTime(transformTimeToExactly(hour, min))
                        }
                    })
            }

            binding.player8Lay -> {
                vm.onSetSeatsLimit(8)
            }

            binding.player16Lay -> {
                vm.onSetSeatsLimit(16)
            }

            binding.player32Lay -> {
                vm.onSetSeatsLimit(32)
            }

            binding.changePwdLay -> {
                activity.launchSelectPwd()
            }

            binding.changeBgLay -> {
                val callback = PhotoCallback { items ->
                    uploadBgRes(items?.firstOrNull()?.path)
                }
                PhotoUtil.launchSelectPhoto(
                    activity,
                    1,
                    TITLE_BANNER_ASPECT_RATIO,
                    true, // 产品希望直接进入剪裁页，当前暂不修改 photo util，这里传 true
                    callback
                )
            }

            binding.createBtn -> {
                trackCreateBtnClick()
                vm.onReqCreate()
            }
        }
    }

    private fun uploadBgRes(path: String?) {
        if (path.isNullOrEmpty()) {
            // 未选择合适的文件，不处理
            return
        }
        // 可能可以考虑一下，在创建时再上传文件
        activity.showProgressDialog("", false)
        SimpleFileUploader.upload(BucketType.roomBg, path, object : IUploadCallback {
            override fun onSuccess(localPath: String?, url: String?) {
                if (url?.startsWith("http") == true) {
                    vm.onSetTitleBanner(url)
                }
                activity.hideProgressDialog()
            }

            override fun onFailed(code: Int, msg: String?) {
                ToastUtil.show(msg)
                HLog.d(TAG, "error upload file: {}", msg)
                activity.hideProgressDialog()
            }
        })
    }

    /**
     * 将选中的小时和分钟转换为对应 00 秒 具体的时间戳
     * 如果早于当前时间，换成第二天
     */
    private fun transformTimeToExactly(hour: Int, minute: Int): Long {
        val calendar = Calendar.getInstance()
        val now = TimeUtil.getServerTime()
        calendar.timeInMillis = now
        calendar.set(Calendar.HOUR_OF_DAY, hour)
        calendar.set(Calendar.MINUTE, minute)
        // 按当天选择当前的时间
        val time = calendar.timeInMillis
        if (time < now) {
            // 选择的时间比当前要早。 加一天尝试判断
            calendar.timeInMillis = time + DateUtils.DAY_IN_MILLIS
        }
        calendar.set(Calendar.SECOND, 0)
        return calendar.timeInMillis
    }

}