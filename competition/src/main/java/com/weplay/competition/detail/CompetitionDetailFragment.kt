package com.weplay.competition.detail

import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.huiwan.base.collectOn
import com.huiwan.base.ktx.dp
import com.huiwan.base.ktx.isWideScreen
import com.huiwan.base.launchForResume
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.BaseFullScreenDialog
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.GamesConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.ChatApi
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.user.LoginHelper
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.three.http.core.KtResult
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.view.updateVisibility
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.share.ShareUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.weplay.competition.CompetitionInfo
import com.weplay.competition.CompetitionInfo.Companion.PLAYER_STATE_ALREADY_SIGNUP
import com.weplay.competition.CompetitionInfo.Companion.PLAYER_STATE_CAN_NOT_SIGNUP
import com.weplay.competition.CompetitionInfo.Companion.PLAYER_STATE_CAN_SIGNUP
import com.weplay.competition.CompetitionInfo.Companion.PLAYER_STATE_GAMING
import com.weplay.competition.CompetitionInfo.Companion.STATE_DESTROY
import com.weplay.competition.CompetitionInfo.Companion.STATE_END
import com.weplay.competition.CompetitionInfo.Companion.STATE_GAMING
import com.weplay.competition.CompetitionInfo.Companion.STATE_INIT
import com.weplay.competition.CompetitionInfo.Companion.STATE_NEXT_ROUND_READY
import com.weplay.competition.CompetitionInfo.Companion.STATE_READY
import com.weplay.competition.CompetitionOnlineHelper
import com.weplay.competition.CompetitionState
import com.weplay.competition.CompetitionStateButton
import com.weplay.competition.R
import com.weplay.competition.dialog.CompetitionRoundSettingDialog
import com.weplay.competition.round.CompetitionRoundActivity
import com.weplay.competition.round.data.CompetitionSimpleInfo
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

class CompetitionDetailFragment : Fragment() {
    private lateinit var viewmodel: CompetitionDetailViewModel
    private var countDownJob: Job? = null
    private lateinit var onlineHelper: CompetitionOnlineHelper

    private var verticalOffset = 0

    private var dialog: BaseFullScreenDialog? = null

    private val adapter: CompetitionDetailInfoAdapter by lazy {
        CompetitionDetailInfoAdapter(viewmodel)
    }
    private lateinit var topBg: ImageView

    private lateinit var stateBt: CompetitionStateButton
    private lateinit var tableIv: ImageView
    private lateinit var tableRd: View

    private lateinit var groupChatIv: ImageView
    private lateinit var groupChatRd: TextView

    private lateinit var resultLauncher: ActivityResultLauncher<Void?>

    private val joinAction: (KtResult<Unit>) -> Unit = {
        if (it is KtResultSuccess) viewmodel.onClickJoinSuccess()
    }

    private val exitAction: (KtResult<Unit>) -> Unit = {
        if (it is KtResultSuccess) viewmodel.onClickExitSuccess()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_competition_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewmodel = ViewModelProvider(requireActivity())[CompetitionDetailViewModel::class.java]
        onlineHelper = CompetitionOnlineHelper.start(
            this, viewmodel.gameType, viewmodel.competitionId, viewmodel
        )
        initToolBar(view)
        initView(view)
        initBottom(view)
        initData()
        resultLauncher = registerForActivityResult(
            HwApi::class.impl().genPwdActivityResultContract()
        ) { result ->
            viewmodel.modifySettings(pswd = result ?: "")
        }
    }

    private fun initView(view: View) {
        topBg = view.findViewById(R.id.competition_detail_top_bg)
        if (isWideScreen) {
            ViewUtil.setViewHeight(topBg, 292.dp)
        }
        val rv = view.findViewById<RecyclerView>(R.id.competition_info_rv)
        rv.layoutManager = LinearLayoutManager(requireContext())
        val dp20 = 20.dp
        val horizontalMargin =
            if (isWideScreen) (ScreenUtil.getScreenWidth() - 500.dp) / 2 else dp20
        rv.addItemDecoration(
            SpaceItemDecoration(
                Rect(horizontalMargin, dp20, horizontalMargin, 0),
                Rect(0, 0, 0, dp20)
            )
        )
        rv.adapter = adapter
    }

    private fun initToolBar(view: View) {
        val drawable = ColorDrawable(ResUtil.getColor(R.color.color_competition_main))
        drawable.alpha = 0
        val actionBar = view.findViewById<View>(R.id.action_bar)
        actionBar.background = drawable

        view.findViewById<View>(R.id.competition_detail_back_iv).setOnDoubleClick {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
        view.findViewById<View>(R.id.competition_detail_share_iv).setOnDoubleClick {
            showShareDialog()
        }

        var limitHeight = ScreenUtil.dip2px(70f)
        view.findViewById<AppBarLayout>(R.id.appbar).apply {
            addOnOffsetChangedListener { appBarLayout, verticalOffset ->
                if (<EMAIL> == verticalOffset) {
                    return@addOnOffsetChangedListener
                }
                <EMAIL> = verticalOffset
                if (abs(verticalOffset.toDouble()) >= limitHeight) {
                    drawable.alpha = 255
                } else {
                    val alpha = (abs(verticalOffset.toDouble()) * 1f / limitHeight * 255).toInt()
                    drawable.alpha = alpha
                }
            }
        }
    }

    private fun initBottom(view: View) {
        StatusBarUtil.fitNavigationBar(view.findViewById(R.id.competition_detail_bottom_lay))
        stateBt = view.findViewById(R.id.competition_detail_state_bt)
        tableIv = view.findViewById(R.id.competition_table_icon_tv)
        tableIv.setOnDoubleClick {
            tableRd.isVisible = false
            CompetitionRoundActivity.start(
                requireActivity(), viewmodel.gameType, viewmodel.competitionId
            )
        }
        tableRd = view.findViewById(R.id.competition_table_rd)
        groupChatIv = view.findViewById(R.id.competition_group_chat_icon_tv)
        groupChatRd = view.findViewById(R.id.competition_group_chat_rd)
        groupChatIv.setOnDoubleClick {
            val gid = viewmodel.info.groupId
            if (gid <= 0) {
                return@setOnDoubleClick
            }
            if (viewmodel.hasJoinGroupChat) {
                viewmodel.notify(ICompetitionDetailEvent.GotoGroupChat(gid))
            } else {
                viewmodel.joinGroupChat(gid)
            }
        }
    }

    private fun initData() {
        viewmodel.infoLiveData.observe(viewLifecycleOwner) { info ->
            WpImageLoader.load(info.bgImg, topBg, ImageLoadInfo.newInfo().screenWidth())
            adapter.update(info)
            updateChatRedDot(info)
            updateCompetitionState(info)
        }
        viewmodel.eventFlow.collectOn(lifecycleScope) { event ->
            when (event) {
                is ICompetitionDetailEvent.ChangeLock -> changeLockState()
                is ICompetitionDetailEvent.EditAnnouncement -> showEditAnnouncementDialog()
                is ICompetitionDetailEvent.ShowRegisterUsers -> showRegisterUsersDialog()
                is ICompetitionDetailEvent.StartGameEarly -> maybeStartGame()
                is ICompetitionDetailEvent.GotoGroupChat -> ChatApi::class.impl()
                    .gotoGroupChat(requireActivity(), event.gid)

                is ICompetitionDetailEvent.IdChanged -> {
                    onlineHelper.reset(event.gameType, event.cid)
                    dialog?.dismiss()
                }

                is ICompetitionDetailEvent.StateChanged -> {
                    updateTableRedDot(event.state)
                    dialog?.dismiss()
                    startCountDown()
                }

                else -> {}
            }
        }
        if (viewmodel.info.cid > 0) {
            updateTableRedDot(viewmodel.state)
        }
        startCountDown()
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownJob?.cancel()
    }

    private fun updateCompetitionState(info: CompetitionInfo) {
        val state = if (info.state == STATE_INIT) {
            when (info.playerState) {
                PLAYER_STATE_CAN_SIGNUP -> CompetitionState.Register(info, joinAction)
                PLAYER_STATE_ALREADY_SIGNUP,
                PLAYER_STATE_GAMING -> CompetitionState.Exit(info, exitAction)

                PLAYER_STATE_CAN_NOT_SIGNUP -> CompetitionState.NotRegister(info.needPwd)
                else -> CompetitionState.NotRegister(info.needPwd)
            }
        } else if (info.state == STATE_READY) {
            if (info.playerState == PLAYER_STATE_ALREADY_SIGNUP) {
                CompetitionState.Exit(info, exitAction)
            } else {
                CompetitionState.ReadyStart
            }
        } else if (info.state == STATE_GAMING || info.state == STATE_NEXT_ROUND_READY) {
            if (info.playerState == PLAYER_STATE_GAMING) {
                CompetitionState.Exit(info, exitAction)
            } else {
                CompetitionState.Gaming(
                    info.round, info.userNum, info.mode,
                    info.playerState != PLAYER_STATE_GAMING
                )
            }
        } else {
            CompetitionState.GameEnd
        }
        stateBt.updateState(state)
    }

    private fun startCountDown() {
        countDownJob?.cancel()
        val info = viewmodel.info
        if (info.getCountDownTime() <= 0) {
            return
        }
        countDownJob = this.launchForResume {
            while (true) {
                delay(1000)
                val seconds = TimeUtil.getLeftServerSecond(info.getCountDownTime())
                if (seconds > 0) {
                    adapter.notifyTimeUpdate()
                }
                if (seconds <= 0) {
                    viewmodel.requestDetailInfo()
                    break
                }
            }
        }
    }

    private fun updateTableRedDot(state: Int) {
        tableRd.isVisible = state == STATE_GAMING
                || state == STATE_NEXT_ROUND_READY
                || state == STATE_END
    }

    private fun updateChatRedDot(info: CompetitionInfo) {
        var num = 0
        if (info.isSelfOwner() || (info.playerState != PLAYER_STATE_CAN_SIGNUP && info.playerState != PLAYER_STATE_CAN_NOT_SIGNUP)) {
            num = ChatApi::class.impl().getGroupChatUnReadNum(info.groupId, true)
        }
        if (num > 0) {
            groupChatRd.updateVisibility(true)
            groupChatRd.text = if (num < 100) num.toString() else "99+"
        } else {
            groupChatRd.updateVisibility(false)
        }
    }

    private fun changeLockState() {
        if (viewmodel.info.needPwd) {
            dialog = HWUIDialogBuilder.newBuilder(requireContext()).apply {
                title(R.string.competition_detail_cancel_password_title)
                content(
                    ResUtil.getStr(
                        R.string.competition_detail_current_password_tips,
                        viewmodel.info.pwd
                    )
                )
                positiveButton(onClick = {
                    viewmodel.modifySettings(pswd = "")
                })
                negativeButton()
                setCanCancel(false)
                setDismissListener {
                    dialog = null
                }
            }.show()
        } else {
            resultLauncher.launch(null)
        }
    }

    private fun showEditAnnouncementDialog() {
        lifecycleScope.launch {
            if (viewmodel.state == STATE_END || viewmodel.state == STATE_DESTROY) {
                ToastUtil.show(R.string.competition_detail_can_not_modify_announce_tips)
                return@launch
            }
            val ret = CompetitionAnnounceEditDialogFragment.show(
                requireActivity(), viewmodel.info.announce
            ) ?: return@launch
            viewmodel.modifySettings(announcement = ret)
        }
    }

    private fun showRegisterUsersDialog() {
        CompetitionRoundSettingDialog.showSearchTeammateDialog(
            requireActivity(),
            CompetitionSimpleInfo(viewmodel.gameType, viewmodel.competitionId)
        )
    }

    private fun showShareDialog() {
        val data = ConstV3Info::class.instance().myShareWithSource.shareCompetition
        val url = "${data.myShareUrl}?code=${ShareUtil.id2code(LoginHelper.getLoginUid())}" +
                "&game_type=${viewmodel.gameType}&cid=${viewmodel.competitionId}"

        val shareInfo = ShareInfo()
        val competitionInfo = viewmodel.info
        var totalReward = 0
        competitionInfo.winRewardMap.forEach { (i, j) -> totalReward += j }
        shareInfo.setTitle(ResUtil.getStr(R.string.competition_detail_share_title, totalReward))
        shareInfo.desc = ResUtil.getStr(
            R.string.competition_detail_share_desc,
            GamesConfig::class.instance().getGameConfig(viewmodel.gameType).shortName
        )
        shareInfo.imgUrl = data.shareIconUrl
        shareInfo.setLink(shareInfo.getLinkIntercept(url))
        shareInfo.wespyDeeplink = "wespydeeplink://competition_detail?" +
                "game_type=${viewmodel.gameType}&cid=${viewmodel.competitionId}"
        shareInfo.shareContentType = ShareInfo.SHARE_CONTENT_TYPE_H5

        shareInfo.screenName = TrackScreenName.SHARE_PAGE
        shareInfo.scene = ResUtil.getStr(R.string.track_competition_detal)
        shareInfo.gameType = viewmodel.gameType

        shareInfo.addShareTypes(ShareType.weCircle)
        shareInfo.addTripartiteShareType()
        shareInfo.addShareTypes(ShareType.android, ShareType.copyLink, ShareType.friend)

        IShareApi::class.impl().showShareDialog(
            requireActivity(), shareInfo, object : ShareCallback {})
    }

    private fun maybeStartGame() {
        if (viewmodel.info.userNum > viewmodel.info.signUpNum) {
            ToastUtil.show(R.string.competition_detail_start_early_count_limit_tips)
            return
        }
        showStartGameEarlyDialog()
    }

    private fun showStartGameEarlyDialog() {
        dialog = HWUIDialogBuilder.newBuilder(requireContext()).apply {
            title(R.string.competition_detail_start_early_title)
            setCanCancel(false)
            negativeButton()
            positiveButton(onClick = {
                viewmodel.startGameEarly()
            })
            setDismissListener {
                dialog = null
            }
        }.show()
    }
}