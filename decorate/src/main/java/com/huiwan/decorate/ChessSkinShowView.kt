package com.huiwan.decorate

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.huiwan.anim.LifecycleAnimView
import com.huiwan.base.ktx.hide
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.dp
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.util.URIUtil
import com.huiwan.base.util.UrlUtil
import com.huiwan.configservice.constentity.propextra.JackarooSkinExtra
import com.huiwan.configservice.model.PropItem
import com.huiwan.decorate.databinding.JackarooChessPicShowViewBinding
import com.huiwan.store.file.FileCacheName
import com.huiwan.store.file.FileConfig.getSourceFileFullPath
import com.wepie.download.DownloadUtil
import com.wepie.download.LifeDownloadCallback
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import java.io.File

class ChessSkinShowView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding =
        JackarooChessPicShowViewBinding.inflate(LayoutInflater.from(context), this)

    init {
        clipChildren = false
    }

    /**
     * @param forceShowStaticPic 是否强制显示静态图
     */
    fun bind(propItem: PropItem?, forceShowStaticPic: Boolean = false) {
        binding.bind(propItem, forceShowStaticPic)
    }

    private fun JackarooChessPicShowViewBinding.bind(
        propItem: PropItem?,
        forceShowStaticPic: Boolean = false
    ) {
        val imageLoadInfo =
            ImageLoadInfo().width(100.dp).height(100.dp).scaleType(ImageView.ScaleType.FIT_XY)
                .setNoneCache(true)
        propItem?.let { config ->
            jackarooChessIv.hide()
            jackarooChessAnimIv.gone()
            jackarooChessAnimIv.clearAll()
            config.getExtraByType(JackarooSkinExtra::class.java)?.let {
                if (it.isVap() && !forceShowStaticPic) {
                    playVap(jackarooChessAnimIv, it.previewResUrl, config.itemId)
                } else {
                    jackarooChessIv.visible()
                    WpImageLoader.load(
                        config.mediaUrl, jackarooChessIv, imageLoadInfo
                    )
                }
            }
        } ?: run {
            jackarooChessIv.setImageDrawable(ResUtil.getDrawable(R.drawable.default_chess_skin))
            jackarooChessIv.visible()
        }
    }

    private fun playVap(animView: LifecycleAnimView, url: String, propId: Int) {
        val localPath = getLocalAnimUrlPath(url, propId, "vap_")
        playVapFromUrlWithLocalPath(animView, url, localPath)
    }

    private fun getLocalAnimUrlPath(animUrl: String, propId: Int, suffix: String): String {
        val defaultMd5 = propId.toString()
        val urlMd5 = UrlUtil.urlToMd5(animUrl, defaultMd5)
        return URIUtil.urlLocalPathWithMd5(
            urlMd5,
            getSourceFileFullPath(FileCacheName.PROP_PATH) + suffix
        )
    }

    private fun playVapFromUrlWithLocalPath(
        animView: LifecycleAnimView,
        url: String,
        localPath: String
    ) {
        val file = File(localPath)
        if (file.exists() && file.length() > 0) {
            animView.visible()
            animView.setLoop(Int.MAX_VALUE)
            animView.startPlay(file)
        } else {
            DownloadUtil.downloadFile(
                true,
                url,
                localPath,
                object : LifeDownloadCallback(animView) {
                    override fun onSuccess(url: String, path: String) {
                        super.onSuccess(url, path)
                        animView.visible()
                        animView.setLoop(Int.MAX_VALUE)
                        animView.startPlay(File(path))
                    }

                    override fun onFail(msg: String) {
                        super.onFail(msg)
                    }
                })
        }
    }
}