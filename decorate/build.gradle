apply from: "../base_component.gradle"
apply from: rootProject.file("gradle/base-composed.gradle")
dependencies {
    implementation libs.constraintlayout
    implementation libs.eventBus
    implementation project(":lib:store")
    implementation project(path: ':lib:anim')
    implementation project(path: ':lib:libwidget')
    implementation project(path: ':lib:libdownload')
    implementation project(path: ':service:ConfigService')
    implementation project(path: ':lib:libimageloader')
    implementation project(path: ':lib:libhttp')
    implementation project(path: ':service:UserService')
    implementation project(path: ':lib:baseutil')
    implementation project(path: ':lib:liblog')
    implementation project(path: ':lib:api')
    implementation libs.glide.compose
    implementation libs.glide
    implementation project(path: ':lib:libcomposewidget')
    implementation project(path: ':component:prop')
}
android {
    buildFeatures {
        viewBinding true
    }
}
