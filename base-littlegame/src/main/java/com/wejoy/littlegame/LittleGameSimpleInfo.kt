package com.wejoy.littlegame

import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.GameConfig.CurrencyType

open class LittleGameSimpleInfo(
    var gameType: Int = 0,

    /**
     * 匹配模式 tcp 模式，
     * 匹配时用的 matchMode,
     * 创建房间时用 roomMode
     */
    var mode: Int = 0,

    /**
     * 游戏模式
     * unity 小游戏使用
     */
    var gameMode: Int = 0,

    /**
     * 游戏级别，匹配房间的用户有效
     * 跟房及游戏恢复情况，无效
     */
    var betLevel: Int = 0,

    @CurrencyType
    var currencyType: Int = GameConfig.CURRENCY_COIN,

    var isCreate: Boolean = false
) {
    constructor(gameType: Int, mode: Int, gameMode: Int, betLevel: Int, currencyType: Int) : this(
        gameType,
        mode,
        gameMode,
        betLevel,
        currencyType,
        false
    )

    /**
     * 加入房间时房间类型是 vip 而自己不是 vip 时的跳转埋点信息
     */
    var joinFailedVipReferScreen = ""


    // 以下两个字段仅供cocos js拉起支付弹窗时使用，全屏支付页面走的是deeplink，用不着
    var gameid: Int = -1
    var referScreenName: String = ""

    fun toMap(): Map<String, Any> {
        val map = mutableMapOf<String, Any>()
        map["game_type"] = gameType
        if (betLevel != -1) {
            map["bet_level"] = betLevel
        }

        if (mode != -1) {
            map["mode"] = mode
        }
        if (gameMode != -1) {
            map["game_mode"] = gameMode
        }
        if (currencyType != -1) {
            map["game_currency_type"] = currencyType
        }
        if (gameid != -1) {
            map["gameid"] = gameid
        }
        if (referScreenName.isNotEmpty()) {
            map["refer_screen_name"] = referScreenName
        }
        return map
    }
}