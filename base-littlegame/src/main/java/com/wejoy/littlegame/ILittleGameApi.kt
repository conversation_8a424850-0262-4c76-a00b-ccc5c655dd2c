package com.wejoy.littlegame

import android.app.Dialog
import android.app.Service
import android.content.Context
import android.widget.ImageView
import com.huiwan.component.gift.send.GiftSendInfo
import com.huiwan.component.gift.send.GiftShowConfig
import com.huiwan.lib.api.Api
import com.wejoy.weplay.ex.context.toActivity
import org.jetbrains.annotations.TestOnly
import java.io.InputStream

interface ILittleGameApi : Api {

    /**
     * 显示游戏教程
     * 部分游戏需要根据模式显示不同的帮助弹窗
     * @param startTask 如果有，则显示开始按钮，否则不显示
     */
    fun showGameRuleDialog(
        context: Context, info: LittleGameSimpleInfo, startTask: Runnable?
    )

    fun showGameRuleDialog(context: Context, info: LittleGameSimpleInfo)


    fun giftShowConfig(rid: Int, toUid: Int, giftId: Int): GiftShowConfig

    fun giftShowConfig(
        rid: Int, uidList: List<Int>, isMultiSend: <PERSON><PERSON>an, giftId: Int
    ): GiftShowConfig

    /**
     * 小游戏游戏版本号，仅 cocos 游戏有效
     */
    fun getGameVersion(gameType: Int): Int

    /**
     * 进入小游戏匹配界面，一般为匹配时挂起回去
     */
    fun gotoIceBallMatchActivity(context: Context, gameType: Int, fromMainPage: Boolean)

    /**
     * 进入小游戏创建房间界面
     */
    fun gotoIceBallCreateRoomActivity(
        context: Context,
        gameType: Int,
        jumpCreateInfo: JumpCreateInfo,
    )

    /**
     * 进入小游戏匹配准备页
     * 一般是二人组队的准备界面
     */
    fun gotoIceBallMatchPrepareActivity(
        context: Context, gameType: Int, fromGameMain: Boolean,
        source: String,
    )

    /**
     * 进入小游戏主页
     */
    fun gotoCocosMainActivity(context: Context, gameType: Int)

    /**
     * 挂起时取消匹配。
     */
    fun onSuspendReqCancelMatch(tid: Int, gameType: Int, duration: Long, needCancelMatch: Boolean)

    /**
     * 小游戏 游戏资源解压目录
     */
    fun getLittleGameResPath(gameType: Int): String

    /**
     * 小游戏覆盖全屏的弹窗
     * context 需要基于 Activity
     */
    fun updateLittleGameLoadingAnim(context: Context, gameType: Int, show: Boolean)

    /**
     * Unity小游戏加载失败页面
     */
    fun showTimeOutPage(context: Context, backRunnable: Runnable?)

    fun handleEnterError(
        context: Context, errorCode: Int, scene: Int, info: LittleGameSimpleInfo, next: () -> Unit
    ): Boolean

    fun showGameStateNotification(title: String, desc: String, service: Service)

    fun isCocosUseWeb(): Boolean

    fun showUserInfoDialogInWeb(
        context: Context,
        uid: Int,
        rid: Int,
        gameType: Int,
        subSource: String,
        giftShowConfigFetcher: (callback: (LittleGameSendGiftConfig) -> Unit) -> Unit
    ): Dialog

    fun preload(context: Context)

    fun notifyUpdateCocosGameStatus(inGame: Boolean)

    fun reportCocosSendGift(info: GiftSendInfo, source: String)

    fun getAgoraStatus(): BooleanArray?

    fun isCocosWebGame(): Boolean

    fun isInCocosWeb(): Boolean

    fun unpackCocosInGameRes(
        srcInputStream: InputStream,
        targetDir: String
    ): Result<Unit>

    fun unpackCocosMatchRes(
        srcInputStream: InputStream,
        gameType: Int
    ): Result<Unit>

    fun updateCocosGameVersionInfo(gameType: Int, isWebGame: Boolean, targetVersion: Int)

    fun updateCocosMatchVersionInfo(gameType: Int, targetVersion: String)

    fun getCocosMatchResVersion(gameType: Int): String

    fun getMatchResDir(gameType: Int): String

    @Throws(IllegalStateException::class)
    suspend fun fetchGameUnit(gameType: Int): CocosGameVersionUnit

    fun checkLoadResWithDialog(
        context: Context,
        gameType: Int,
        trackScene: String,
        next: (isCocos: Boolean) -> Unit
    )

    fun checkLoadResSilent(gameType: Int)

    fun gotoLittleGame(context: Context, info: LittleGameInfo)

    fun gotoLittleGameAndEnterRoom(context: Context, info: LittleGameInfo, enterRoomInfo: LittleGameEnterRoomSimpleInfo)

    @TestOnly
    fun updateBuildInAppCocosDebugInfo(gameType: Int, zipUrl: String)

    val isForceIncUpdate: Boolean

    val incUpdateItemRatioLimit: Int

    val incUpdateSizeRatioLimit: Int

    /**
     * 展示队伍邀请弹窗
     */
    fun showTeamInviteDialog(context: Context, gameType: Int, tid: Int, subScene: String): Dialog

    /**
     * 小游戏加载时的加载 Drawable
     * 资源不在小游戏模块
     */
    fun setLoadingDrawable(view: ImageView)

    /**
     * 重启 cocos
     * 根据 deeplink 跳转到 cocos 内
     */
    fun relaunchCocos(context: Context, deeplink: String)

    /**
     * 如果进程内存在队伍信息，则退出该队伍
     */
    fun leaveCurTeamIfIn()

    /**
     * 是否启用cocosWebView的cache，需要读服务器
     */
    fun setCocosWebViewCacheEnabled(value: Boolean)

    /**
     * 是否启用cocosWebView的cache
     */
    fun getCocosWebViewCacheEnabled(): Boolean

    /**
     * 是否启用cocosWebView预加载，需要读服务器
     */
    fun setPreloadCocosAssetEnabled(value: Boolean)

    /**
     * 是否启用cocosWebView预加载
     */
    fun getPreloadCocosAssetEnabled(): Boolean

    /**
     * 预加载资源
     */
    fun preloadCocosAsset(context: Context, gameType: Int)

    class LittleGameSendGiftConfig(
        val giftShowConfig: GiftShowConfig,
        val notifyUidList: List<Int>,
        val trackInfo: Map<String, Any>,
        /**
         * 是否使用通用的 http 送礼接口
         */
        val commonSend: Boolean
    )

    /**
     * 弹窗整体管理器
     */
    interface IDialogManagePage {
        /**
         * 把后续可能需要统一清理掉的弹窗记录下来
         */
        fun recordShowingDialog(dialog: Dialog)

        /**
         * 清理所有的记录的弹窗
         */
        fun clearRecordDialogs()
    }

    class JumpCreateInfo(
        val fromGameMain: Boolean = false,
        val needShowInviteDialog: Boolean = false,
        val canSupportCocosMatch: Boolean = true,
        val source: String
    )

    companion object {
        /**
         * 如果 context 的类型实际上是支持弹窗整体管理器
         * 将 dialog 记录下来
         */
        fun recordShowingDialog(context: Context, dialog: Dialog) {
            val activity = context.toActivity() ?: return
            if (activity is IDialogManagePage) {
                activity.recordShowingDialog(dialog)
            }
        }

        /**
         * 如果 context 的类型实际上是支持弹窗整体管理器
         * 将记录的 dialog 消除掉
         */
        fun clearRecordDialogs(context: Context) {
            val activity = context.toActivity() ?: return
            if (activity is IDialogManagePage) {
                activity.clearRecordDialogs()
            }
        }
    }
}