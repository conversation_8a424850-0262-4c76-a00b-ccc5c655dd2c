<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


        <include
            android:id="@+id/rank_head_rank_2"
            layout="@layout/rank_list_jk_game_head_item"
            android:layout_width="0dp"
            android:layout_height="206dp"
            app:layout_constraintEnd_toStartOf="@id/rank_head_rank_1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/rank_head_rank_1" />

        <include
            android:id="@+id/rank_head_rank_1"
            layout="@layout/rank_list_jk_game_head_item"
            android:layout_width="0dp"
            android:layout_height="216dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="15dp"
            app:layout_constraintEnd_toStartOf="@id/rank_head_rank_3"
            app:layout_constraintStart_toEndOf="@id/rank_head_rank_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/rank_head_rank_3"
            layout="@layout/rank_list_jk_game_head_item"
            android:layout_width="0dp"
            android:layout_height="206dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/rank_head_rank_1"
            app:layout_constraintBottom_toBottomOf="@+id/rank_head_rank_1"  />


</androidx.constraintlayout.widget.ConstraintLayout>