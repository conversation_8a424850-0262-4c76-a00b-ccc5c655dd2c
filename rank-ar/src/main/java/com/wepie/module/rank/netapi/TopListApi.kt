package com.wepie.module.rank.netapi

import com.huiwan.user.entity.CocosUserWeeklyInfo
import com.huiwan.user.entity.UserGameInfo
import com.huiwan.user.http.UserUrlConfig.USER_API_USER_GAME_INFO
import com.huiwan.user.http.UserUrlConfig.USER_API_USER_GAME_WEEKLY
import com.three.http.core.HttpUtil
import com.three.http.core.KtResult
import com.three.http.core.postSuspend
import com.wepie.module.rank.bean.RankJkCollectionInfo
import com.wepie.module.rank.bean.RankJkItemInfo

private const val TOP_LIST_API_GAME_RANK_EARNINGS_WEEKLY = "/top_list_api/game_rank_weekly"
private const val TOP_LIST_API_GAME_RANK_EARNINGS_TOTAL = "/top_list_api/game_rank_total"
private const val TOP_LIST_API_GAME_RANK_COLLECTIONS_RANK = "/top_list_api/get_collection_rank"

suspend fun reqGameRank(
    weekly: Boolean,
    gameType: Int,
    rankType: Int = 0
): KtResult<List<RankJkItemInfo>> {
    val uri = if (weekly) TOP_LIST_API_GAME_RANK_EARNINGS_WEEKLY else TOP_LIST_API_GAME_RANK_EARNINGS_TOTAL
    return HttpUtil.newBuilder()
        .uri(uri)
        .addParam("game_type", gameType.toString())
        .addParam("rank_type", rankType.toString())
        .build()
        .postSuspend<List<RankJkItemInfo>>()
}

suspend fun reqGameCollectionRank(
    gameType: Int,
    rankType: Int = 0
): KtResult<RankJkCollectionInfo> {
    return HttpUtil.newBuilder()
        .uri(TOP_LIST_API_GAME_RANK_COLLECTIONS_RANK)
        .addParam("game_type", gameType.toString())
        .addParam("rank_type", rankType.toString())
        .build()
        .postSuspend<RankJkCollectionInfo>()
}


suspend fun reqRankSelfWeekly(gameType: Int, rankType: Int) =
    HttpUtil.newBuilder().uri(USER_API_USER_GAME_WEEKLY)
        .addParam("game_type", gameType.toString())
        .addParam("rank_type", rankType.toString())
        .build().postSuspend<CocosUserWeeklyInfo>()

suspend fun reqRankSelfTotal(gameType: Int, rankType: Int) =
    HttpUtil.newBuilder().uri(USER_API_USER_GAME_INFO)
        .addParam("game_type", gameType.toString())
        .addParam("rank_type", rankType.toString())
        .build().postSuspend<UserGameInfo>()