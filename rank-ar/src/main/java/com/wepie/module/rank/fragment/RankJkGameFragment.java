package com.wepie.module.rank.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.base.context.holder.ContextHolder;
import com.huiwan.base.ktx.ViewExtKt;
import com.huiwan.base.str.ResUtil;
import com.wepie.module.rank.R;
import com.wepie.module.rank.activity.IRankFullBg;
import com.wepie.module.rank.activity.NewRankActivity;
import com.wepie.module.rank.activity.NewRankConst;
import com.wepie.module.rank.view.base.BaseRankDetailFragment;
import com.wepie.module.rank.view.jackaroo.RankJkGameDetailFragment;
import com.wepie.module.rank.viewmodel.BaseRankViewModel;
import com.wepie.module.rank.viewmodel.RankJackarooViewModel;

public class RankJkGameFragment extends BaseRankFragment {
    private TextView weeklyTv;
    private TextView totallyTv;

    private RankJackarooViewModel rankViewModel;

    private int selectedSubIndex = 0;

    @Override
    protected void initView(View rootView) {
        super.initView(rootView);
        tab = NewRankActivity.RANK_TAB_INDEX_JK;
        ViewGroup subLay = rootView.findViewById(R.id.rank_sub_period_lay);
        ViewExtKt.updateVisible(subLay, true);
        weeklyTv = subLay.findViewById(R.id.week_tv);
        totallyTv = subLay.findViewById(R.id.total_tv);
        weeklyTv.setOnClickListener(v -> {
            int type = selectedTypeByPagerPosition(rankListVp.getCurrentItem());
            rankViewModel.changeShowPeriod(type, true);
        });
        totallyTv.setOnClickListener(v -> {
            int type = selectedTypeByPagerPosition(rankListVp.getCurrentItem());
            rankViewModel.changeShowPeriod(type, false);
        });

        initSubLabelViewTheme(0xff200b36, R.drawable.rank_sub_label_vip);
        observeWeekly();
    }


    @Override
    protected void initSubLabelList() {
        subLabelList.clear();
        subLabelList.add(ResUtil.getString(R.string.jackaroo_rank_earns));
        subLabelList.add(ResUtil.getString(R.string.jackaroo_game_career_collection));
    }

    @Override
    protected BaseRankViewModel subscribeViewModel() {
        rankViewModel = new ViewModelProvider(requireActivity()).get(RankJackarooViewModel.class);
        return rankViewModel;
    }

    @Override
    protected Fragment createRankDetailFragment(int position) {
        int type = selectedTypeByPagerPosition(position);
        BaseRankDetailFragment<?> fragment = new RankJkGameDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BaseRankDetailFragment.EXTRA_TYPE, type);
        bundle.putSerializable(BaseRankDetailFragment.EXTRA_MODEL_CLASS, rankViewModel.getClass());
        fragment.setArguments(bundle);
        return fragment;
    }

    private void observeWeekly() {
        rankViewModel.getMainWeeklyLiveData().observe(this, weekly -> {
            if (weekly) {
                weeklyTv.setTextAppearance(R.style.RankJkPeriodTextAppearance_Enable);
                totallyTv.setTextAppearance(R.style.RankJkPeriodTextAppearance_Disable);
            } else {
                weeklyTv.setTextAppearance(R.style.RankJkPeriodTextAppearance_Disable);
                totallyTv.setTextAppearance(R.style.RankJkPeriodTextAppearance_Enable);
            }
        });
    }

    @Override
    protected void onShowFullBg(IRankFullBg fullBg) {
        changeRankBg(fullBg);
    }

    @Override
    protected void onSubPageSelected(int subIndex) {
        selectedSubIndex = subIndex;
        rankViewModel.onSelectRankType(selectedTypeByPagerPosition(subIndex));
        Context context = getContext();
        if (context != null) {
            changeRankBg(ContextHolder.of(context, IRankFullBg.class));
        }
        if (subIndex == 0) {
            initSubLabelViewTheme(0xff200b36, R.drawable.rank_sub_label_vip);
            weeklyTv.setText(R.string.jackaroo_rank_weekly);
        } else {
            initSubLabelViewTheme(0xff200b36, R.drawable.rank_sub_label_family);
            weeklyTv.setText(R.string.rank_time_monthly);
        }
    }

    private void changeRankBg(IRankFullBg rankBg) {
        int res = selectedSubIndex == 0 ? R.drawable.rank_bg_jk_earns : R.drawable.rank_bg_jk_collections;
        rankBg.showRankBg(res);
    }

    private int selectedTypeByPagerPosition(int position) {
        return position == 0 ? NewRankConst.TYPE_JK_GAME_EARN : NewRankConst.TYPE_JK_GAME_COLLECTION;
    }
}
