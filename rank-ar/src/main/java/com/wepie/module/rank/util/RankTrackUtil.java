package com.wepie.module.rank.util;

import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.liblog.main.HLog;

import java.util.HashMap;
import java.util.Map;

public class RankTrackUtil {
    private static final String TAG = "RankTrackUtil";

    private static final String SCREEN_SUB_RANK_TODAY = "今日榜";
    private static final String SCREEN_SUB_RANK_YESTERDAY = "昨日榜";
    private static final String SCREEN_SUB_RANK_P = "名人堂";
    private static final String SCREEN_SUB_RANK_ANNUAL = "年榜";

    private static final String SCREEN_SUB_RANK_DAY = "日榜";
    private static final String SCREEN_SUB_RANK_WEEK = "周榜";
    private static final String SCREEN_SUB_RANK_TOTAL = "总榜";
    private static final String SCREEN_SUB_RANK_BLESS = "祝福榜";

    private static final String SCREEN_SUB_RANK_MALE = "男生";
    private static final String SCREEN_SUB_RANK_FEMALE = "女生";

    private static final String SCENE_RANK = "排行榜";
    private static final String SCENE_AVATAR = "会玩秀";

    static class RankItem {
        String screenName;
        String[] arr;

        public RankItem(String screenName, String[] arr) {
            this.screenName = screenName;
            this.arr = arr;
        }
    }

    private static final RankItem[] RANK_ITEM = new RankItem[]{
            new RankItem(TrackScreenName.RANK_JK_RANK, new String[]{"Earning", "Collection"}), // 数据暂时不关注，这里先放一个待用的。
            new RankItem(TrackScreenName.POPULARITY_RANK, new String[]{SCREEN_SUB_RANK_TODAY, SCREEN_SUB_RANK_YESTERDAY, SCREEN_SUB_RANK_P, SCREEN_SUB_RANK_ANNUAL}),
            new RankItem(TrackScreenName.VIP_RANK, new String[]{SCREEN_SUB_RANK_TODAY, SCREEN_SUB_RANK_YESTERDAY, SCREEN_SUB_RANK_P}),
            new RankItem(TrackScreenName.COUPLE_RANK, new String[]{SCREEN_SUB_RANK_WEEK, SCREEN_SUB_RANK_TOTAL, SCREEN_SUB_RANK_BLESS}),
            new RankItem(TrackScreenName.VOICE_ROOM_RANK, new String[]{SCREEN_SUB_RANK_DAY, SCREEN_SUB_RANK_WEEK, SCREEN_SUB_RANK_TOTAL}),
            new RankItem(TrackScreenName.FAMILY_RANK, new String[]{SCREEN_SUB_RANK_WEEK, SCREEN_SUB_RANK_TOTAL}),
            new RankItem(TrackScreenName.AVATAR_SHOW_RANK, new String[]{SCREEN_SUB_RANK_MALE, SCREEN_SUB_RANK_FEMALE}),
    };


    public static void addTrack(boolean rankPage, int index, int subIndex) {
        HLog.d(TAG, "track rank({}, {})", index, subIndex);
        if (index >= 0 && index < RANK_ITEM.length) {
            RankItem item = RANK_ITEM[index];
            if (subIndex >= 0 && subIndex < item.arr.length) {
                String sub = item.arr[subIndex];
                viewScreen(item.screenName, sub, rankPage);
            }
        }
    }

    private static void viewScreen(String screenName, String screenSubName, boolean rankPage) {
        Map<String, Object> map = new HashMap<>();
        map.put(TrackUtil.trackValue().getScreenName(), screenName);
        map.put("screen_sub_name", screenSubName);
        map.put("scene", rankPage ? SCENE_RANK : SCENE_AVATAR);
        TrackUtil.trackEvent(TrackUtil.trackValue().getAppViewScreen(), map);
    }
}
