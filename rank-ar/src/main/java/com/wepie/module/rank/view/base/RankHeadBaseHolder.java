package com.wepie.module.rank.view.base;

import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.huiwan.widget.image.RoundedDrawable;
import com.wepie.module.rank.R;

import java.util.ArrayList;
import java.util.List;

@Keep
public abstract class RankHeadBaseHolder<T> {
    protected final View rootView;
    private final ArrayList<HeadItemHolder<T>> rankItems = new ArrayList<>(3);

    public ArrayList<View> getViewArrayList() {
        return mViewArrayList;
    }

    private final ArrayList<View> mViewArrayList = new ArrayList<>(3);

    public RankHeadBaseHolder(View rootView) {
        this.rootView = rootView;
        View view1 = rootView.findViewById(R.id.rank_head_rank_1);
        View view2 = rootView.findViewById(R.id.rank_head_rank_2);
        View view3 = rootView.findViewById(R.id.rank_head_rank_3);
        mViewArrayList.add(view1);
        mViewArrayList.add(view2);
        mViewArrayList.add(view3);
        HeadItemHolder<T> rankItem1 = createHolder(view1);
        HeadItemHolder<T> rankItem2 = createHolder(view2);
        HeadItemHolder<T> rankItem3 = createHolder(view3);
        rankItem1.updateRankNum(1);
        rankItem2.updateRankNum(2);
        rankItem3.updateRankNum(3);
        rankItems.add(rankItem1);
        rankItems.add(rankItem2);
        rankItems.add(rankItem3);
    }

    public void update(@NonNull List<T> dataList) {
        int size = dataList.size();
        int holderSize = rankItems.size();
        for (int i = 0; i < holderSize; i++) {
            HeadItemHolder<T> holder = rankItems.get(i);
            if (i < size) {
                holder.update(dataList.get(i));
            } else {
                holder.showEmpty();
            }
        }
    }

    protected abstract HeadItemHolder<T> createHolder(View view);

    public abstract static class HeadItemHolder<T> {
        public abstract void updateRankNum(int num);

        public abstract void update(T data);

        public void showEmpty() {
        }

        public void showEmptyHead(ImageView imageView) {
            Drawable head = ContextCompat.getDrawable(imageView.getContext(), R.drawable.default_head_icon);
            head = RoundedDrawable.fromDrawable(head);
            if (head instanceof RoundedDrawable) {
                ((RoundedDrawable) head).setOval(true);
            }
            imageView.setImageDrawable(head);
        }
    }
}
