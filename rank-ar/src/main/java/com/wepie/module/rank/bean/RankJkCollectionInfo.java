package com.wepie.module.rank.bean;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;


public class RankJkCollectionInfo {

    @SerializedName("rank")
    public List<RankJkItemInfo> rank = new ArrayList<>();

    @SerializedName("mine")
    public RankJkItemInfo mine = new RankJkItemInfo();

    public List<RankJkItemInfo> getRank() {
        return rank;
    }

    public RankJkItemInfo getMine() {
        return mine;
    }


}