package com.wepie.module.rank.viewmodel

import android.util.SparseBooleanArray
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.editionentity.GameConfig.RANK_TYPE_COLLECTION_MONTHLY
import com.huiwan.configservice.editionentity.GameConfig.RANK_TYPE_COLLECTION_TOTAL
import com.huiwan.configservice.editionentity.GameConfig.RANK_TYPE_EARNING
import com.huiwan.constants.GameType
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wepie.module.rank.activity.NewRankConst
import com.wepie.module.rank.bean.RankJkCollectionInfo
import com.wepie.module.rank.netapi.reqGameCollectionRank
import com.wepie.module.rank.netapi.reqGameRank
import com.wepie.module.rank.netapi.reqRankSelfTotal
import com.wepie.module.rank.netapi.reqRankSelfWeekly
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class RankJackarooViewModel : BaseRankViewModel() {
    companion object {
        const val GAME_TYPE = GameType.GAME_TYPE_JACKAROO
    }

    private val _state = MutableStateFlow(jackarooDefaultStat())
    val stat: StateFlow<JackarooRankStat> = _state

    /**
     * earn -> weekly_boolean
     * win -> weekly_boolean
     * 默认展示周维度，根据榜单类型单独记忆当前是哪个维度。
     */
    private val typePeriodMap = SparseBooleanArray()
    private val _mainWeeklyLiveData = MutableLiveData(true)
    val mainWeeklyLiveData: LiveData<Boolean> = _mainWeeklyLiveData

    private val _collectionStat = MutableStateFlow(JackarooRankType())
    private val _earnStat = MutableStateFlow(JackarooRankType())
    private var seq: Int = 0
    val collectionStat: StateFlow<JackarooRankType> = _collectionStat
    val earnStat: StateFlow<JackarooRankType> = _earnStat

    init {
        typePeriodMap.put(NewRankConst.TYPE_JK_GAME_EARN, true)
        typePeriodMap.put(NewRankConst.TYPE_JK_GAME_COLLECTION, true)
    }

    /**
     * type 对应的 weekly 做出调整
     */
    fun changeShowPeriod(rankType: Int, weekly: Boolean) {
        val preWeekly = typePeriodMap.get(rankType, true)
        typePeriodMap.put(rankType, weekly)
        if (weekly != preWeekly) {
            refreshStat(rankType)
            refreshRankTypeWeekly(rankType)
        }
    }

    fun onSelectRankType(rankType: Int) {
        refreshRankTypeWeekly(rankType)
    }

    private fun refreshRankTypeWeekly(rankType: Int) {
        val weekly = typePeriodMap.get(rankType, true)
        _mainWeeklyLiveData.value = weekly
    }

    fun refreshIfEmpty(rankType: Int) {
        if (rankType == NewRankConst.TYPE_JK_GAME_COLLECTION) {
            if (stat.value.monthlyCollections.mine.uid != LoginHelper.getLoginUid()) {
                onRefresh(rankType)
            }
        } else {
            if (stat.value.weeklyEarns.mine.uid != LoginHelper.getLoginUid()) {
                onRefresh(rankType)
            }
        }
    }

    override fun onRefresh(rankType: Int) {
        if (rankType == NewRankConst.TYPE_JK_GAME_COLLECTION) {
            reqCollection(monthly = true, uiRankType = rankType)
            reqCollection(monthly = false, uiRankType = rankType)
        } else if (rankType == NewRankConst.TYPE_JK_GAME_EARN) {
            reqEarn(weekly = true, uiRankType = rankType)
            reqEarn(weekly = false, uiRankType = rankType)
        }
    }

    private fun refreshStat(uiRankType: Int) {
        val weekly = typePeriodMap.get(uiRankType, true)
        if (uiRankType == NewRankConst.TYPE_JK_GAME_COLLECTION) {
            _collectionStat.value = if (weekly) {
                stat.value.monthlyCollections
            } else {
                stat.value.totalCollections
            }
        } else {
            _earnStat.value = if (weekly) {
                stat.value.weeklyEarns
            } else {
                stat.value.totalEarns
            }
        }
    }


    private fun reqCollection(monthly: Boolean, uiRankType: Int = 0) {
        viewModelScope.launch {
            val rankType = if (monthly) RANK_TYPE_COLLECTION_MONTHLY else RANK_TYPE_COLLECTION_TOTAL
            val dataRes = reqGameCollectionRank(GAME_TYPE, rankType)
            val rankList = when (dataRes) {
                is KtResultSuccess -> dataRes.data.rank
                is KtResultFailed -> {
                    ToastUtil.show(dataRes.msg)
                    emptyList()
                }

                else -> {
                    emptyList()
                }
            }.mapIndexed { index, item ->
                JackarooRankItem(
                    uid = item.uid,
                    rank = index + 1,
                    value = item.score.format(),
                    collection = true,
                    activeLevel = item.activeLevel,
                    qualifyStar = item.qualifyInfo?.star ?: 0,
                    qualifyGrade = item.qualifyInfo?.grade ?: 0
                )
            }
            val selfInfo = getSelfInfo(dataRes, rankList)

            val pre = _state.value
            val rank = JackarooRankType(seq++, false, rankList, selfInfo)
            when {
                monthly -> {
                    _state.value = pre.copy(monthlyCollections = rank)
                }

                !monthly -> {
                    _state.value = pre.copy(totalCollections = rank)
                }
            }
            refreshStat(uiRankType)
        }


    }

    private fun getSelfInfo(
        dataRes: KtResult<RankJkCollectionInfo>,
        rankList: List<JackarooRankItem>
    ): JackarooRankItem {
        return when (dataRes) {
            is KtResultSuccess -> {
                val selfUser = dataRes.data.mine
                JackarooRankItem(
                    LoginHelper.getLoginUid(),
                    isSelfRank(LoginHelper.getLoginUid(), rankList),
                    selfUser.score.format(),
                    collection = true,
                    activeLevel = selfUser.activeLevel,
                    qualifyGrade = selfUser.qualifyInfo?.grade ?: 0,
                    qualifyStar = selfUser.qualifyInfo?.star ?: 0
                )
            }

            is KtResultFailed -> {
                ToastUtil.show(dataRes.msg)
                JackarooRankItem(LoginHelper.getLoginUid(), 0, "", true)
            }

            else -> {
                JackarooRankItem(LoginHelper.getLoginUid(), 0, "", true)
            }
        }
    }

    private fun isSelfRank(uid: Int, rank: List<JackarooRankItem>): Int {
        return rank.find { uid == it.uid }?.rank ?: 0
    }

    private fun Long.format(): String =
        StringUtil.formatInteger(this, "0.0")

    private fun reqEarn(weekly: Boolean, uiRankType: Int = 0) {
        viewModelScope.launch {
            val rankType = RANK_TYPE_EARNING
            val rankList = when (val rankListRes = reqGameRank(weekly, GAME_TYPE, rankType)) {
                is KtResultSuccess -> rankListRes.data
                is KtResultFailed -> {
                    ToastUtil.show(rankListRes.msg)
                    emptyList()
                }

                else -> {
                    emptyList()
                }
            }.mapIndexed { index, item ->
                JackarooRankItem(
                    uid = item.uid,
                    rank = index + 1,
                    value = item.score.format(),
                    collection = false,
                    activeLevel = item.activeLevel,
                    qualifyStar = item.qualifyInfo?.star ?: 0,
                    qualifyGrade = item.qualifyInfo?.grade ?: 0
                )
            }
            val selfInfo = reqSelf(weekly, rankType, false)

            val pre = _state.value
            val rank = JackarooRankType(seq++, false, rankList, selfInfo)
            when {
                weekly -> {
                    _state.value = pre.copy(weeklyEarns = rank)
                }

                !weekly -> {
                    _state.value = pre.copy(totalEarns = rank)
                }
            }
            refreshStat(uiRankType)
        }
    }

    private suspend fun reqSelf(
        weekly: Boolean,
        rankType: Int,
        collection: Boolean
    ): JackarooRankItem {
        val selfUser = UserService.get().loginUser
        return if (weekly) {
            when (val self = reqRankSelfWeekly(GAME_TYPE, rankType)) {
                is KtResultSuccess -> JackarooRankItem(
                    LoginHelper.getLoginUid(),
                    self.data.rank,
                    self.data.score.format(collection),
                    collection,
                    activeLevel = selfUser.level,
                    qualifyGrade = selfUser.qfGrade,
                    qualifyStar = selfUser.qfStar
                )

                is KtResultFailed -> {
                    ToastUtil.show(self.msg)
                    JackarooRankItem(LoginHelper.getLoginUid(), 0, "", collection)
                }

                else -> {
                    JackarooRankItem(LoginHelper.getLoginUid(), 0, "", collection)
                }
            }
        } else {
            when (val self = reqRankSelfTotal(GAME_TYPE, rankType)) {
                is KtResultSuccess -> JackarooRankItem(
                    LoginHelper.getLoginUid(),
                    self.data.rank,
                    self.data.score.format(collection),
                    collection,
                    activeLevel = selfUser.level,
                    qualifyGrade = selfUser.qfGrade,
                    qualifyStar = selfUser.qfStar
                )

                is KtResultFailed -> {
                    ToastUtil.show(self.msg)
                    JackarooRankItem(LoginHelper.getLoginUid(), 0, "", collection)
                }

                else -> {
                    JackarooRankItem(LoginHelper.getLoginUid(), 0, "", collection)
                }
            }
        }
    }

    fun Long.format(win: Boolean): String {
        return if (win) {
            this.toString()
        } else {
            StringUtil.formatInteger(this, "0.0")
        }
    }
}


internal fun jackarooDefaultStat(): JackarooRankStat {
    return JackarooRankStat()
}

data class JackarooRankItem(
    val uid: Int = 0,
    val rank: Int = 0,
    val value: String = "",
    val collection: Boolean = false,
    val activeLevel: Int = 0,
    val qualifyGrade: Int = 0,
    val qualifyStar: Int = 0,
)

data class JackarooRankType(
    // 提醒 对比刷新数据
    val seq: Int = 0,
    val isLoading: Boolean = true,
    val rank: List<JackarooRankItem> = emptyList(),
    val mine: JackarooRankItem = JackarooRankItem()
)

data class JackarooRankStat(
    val weeklyEarns: JackarooRankType = JackarooRankType(),
    val totalEarns: JackarooRankType = JackarooRankType(),
    val monthlyCollections: JackarooRankType = JackarooRankType(),
    val totalCollections: JackarooRankType = JackarooRankType(),
)