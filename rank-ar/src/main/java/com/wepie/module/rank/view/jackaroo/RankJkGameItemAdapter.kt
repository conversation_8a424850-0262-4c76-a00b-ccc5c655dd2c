package com.wepie.module.rank.view.jackaroo

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.ChessSkinShowView
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.JackarooLevelView
import com.huiwan.decorate.NameTextView
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.UserSimpleInfoCallback
import com.huiwan.user.entity.UserSimpleInfo
import com.huiwan.widget.rv.RVHolder
import com.wejoy.weplay.ex.view.updateVisibility
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.WpImageLoader
import com.wepie.module.rank.R
import com.wepie.module.rank.util.RankJumpUtil
import com.wepie.module.rank.view.base.RankRecycleViewItemAdapter
import com.wepie.module.rank.viewmodel.JackarooRankItem
import java.lang.ref.WeakReference

class RankJkGameItemAdapter(context: Context) :
    RankRecycleViewItemAdapter<JackarooRankItem, RankJkGameItemAdapter.ViewHolder>() {
    private val layoutInflater: LayoutInflater = LayoutInflater.from(context)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(layoutInflater.inflate(R.layout.rank_jk_game_rank_item, parent, false))
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val info = dataList[position]!!
        holder.refresh(info)
    }

    /**
     * [R.layout.rank_jk_game_rank_item]
     */
    class ViewHolder(view: View) : RVHolder(view) {
        private val rankTv: TextView = view.findViewById(R.id.rank_tv)
        private val valueNumTv: TextView = view.findViewById(R.id.value_num_tv)
        private val chipView: View = view.findViewById(R.id.chip_icon_view)
        private val collectionView: View = view.findViewById(R.id.collection_icon_view)
        private val bottomLine: View = view.findViewById(R.id.bottom_line)
        val chessIv: ChessSkinShowView = view.findViewById(R.id.chess_iv)
        val gradeIv: ImageView = view.findViewById(R.id.grade_diamond_iv)
        val gradeKingLay: View = view.findViewById(R.id.grade_king_lay)
        val gradeKingTv: TextView = view.findViewById(R.id.grade_king_tv)
        val headIv: DecorHeadImgView = view.findViewById(R.id.head_iv)
        val levelView: JackarooLevelView = view.findViewById(R.id.jk_level_view)
        val nameTv: NameTextView = view.findViewById(R.id.name_tv)

        var uid = 0

        private fun refreshLine() {
            val adapter = bindingAdapter
            if (adapter == null) {
                bottomLine.updateVisibility(false)
            } else if (bindingAdapterPosition < adapter.itemCount - 1) {
                bottomLine.updateVisibility(true)
            } else {
                bottomLine.updateVisibility(false)
            }
        }

        private fun refreshRank(rank: Int) {
            if (rank <= 0) {
                rankTv.visibility = View.VISIBLE
                rankTv.text = "-"
            } else if (rank <= 999) {
                rankTv.text = rank.toString()
            } else {
                rankTv.text = "999+"
            }
        }

        @SuppressLint("SetTextI18n")
        fun refresh(item: JackarooRankItem) {
            if (item.uid <= 0) {
                return
            }
            this.uid = item.uid
            chipView.updateVisibility(!item.collection)
            collectionView.updateVisibility(item.collection)
            valueNumTv.text = item.value
            headIv.showUserHeadWithDecoration(item.uid)
            refreshRank(item.rank)
            refreshLine()
            UserService.get().getCacheSimpleUser(uid, UserCb(WeakReference(this)))
            //        dateTv.setText(TimeUtil.getRankDate(itemInfo.getTimeInSecond()));
            itemView.setOnClickListener {
                if (!LoginHelper.isLimit()) {
                    RankJumpUtil.jumpUser(it.context, uid, TrackScreenName.RANK_JK_RANK)
                }
            }
        }
    }


    class UserCb(val ref: WeakReference<ViewHolder>) : UserSimpleInfoCallback {
        override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo?) {
            val holder = ref.get()
            if (holder != null && simpleInfo != null && simpleInfo.uid == holder.uid) {
                holder.nameTv.setUserName(simpleInfo)
                holder.levelView.setLevel(simpleInfo.level)
                bindGradeIC(holder, simpleInfo)
                bindGradeKing(holder, simpleInfo)
                bindChessIC(holder,simpleInfo)
            }
        }

        override fun onUserInfoFailed(description: String?) {
        }

        private fun bindGradeIC(holder: ViewHolder, simpleInfo: UserSimpleInfo) {
            val qfGrade = ConfigHelper.getInstance().constV3Info.findQFGrade(simpleInfo.getQfGrade())
            if (qfGrade == null || qfGrade.icon.isNullOrEmpty()) {
                holder.gradeIv.visibility = View.GONE
            } else {
                holder.gradeIv.visibility = View.VISIBLE
                WpImageLoader.load(qfGrade.icon, holder.gradeIv)
            }
        }

        private fun bindChessIC(holder: ViewHolder, simpleInfo: UserSimpleInfo) {
            val propItem =
                ConfigHelper.getInstance().propConfig.getPropItem(simpleInfo.collectionInfo.inUseChessPropId)
            holder.chessIv.bind(propItem)
        }

        private fun bindGradeKing(holder: ViewHolder, simpleInfo: UserSimpleInfo) {
            if (simpleInfo.qfStar > 0) {
                holder.gradeKingLay.visibility = View.VISIBLE
                holder.gradeKingTv.text = simpleInfo.qfStar.toString()
            } else {
                holder.gradeKingLay.visibility = View.GONE
            }
        }
    }
}
