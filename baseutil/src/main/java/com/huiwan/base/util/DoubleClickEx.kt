package com.huiwan.base.util

import android.view.View
import com.huiwan.base.LibBaseUtil

/**
 * 默认屏蔽间隔
 */
const val SKIP_DURATION = 500L //milliseconds
private const val TAG = "SingleClickListener"

/**
 * 屏蔽快速点击
 * @param skipDurationMs 不屏蔽的 最短时间间隔， 默认 500 毫秒
 */
abstract class SingleClickListener(private val skipDurationMs: Long = SKIP_DURATION) :
    View.OnClickListener {
    /**
     * 上次点击时间
     */
    private var mLastClickTime: Long = 0
    final override fun onClick(v: View) {
        if (System.currentTimeMillis() - mLastClickTime > skipDurationMs) {
            onClickInternal(v)
            mLastClickTime = System.currentTimeMillis()
        } else {
            if (LibBaseUtil.buildDebug()) {
                LibBaseUtil.logInfo(TAG, false, "repeat click")
            }
        }
    }

    /**
     * onClick 处理方法
     */
    abstract fun onClickInternal(v: View)
}

/**
 * 屏蔽快速点击出两次效果。
 * 拓展方法， 方便 kotlin 使用.
 * 默认屏蔽间隔 500 毫秒
 */
fun View.setOnDoubleClick(skipDurationMs: Long = SKIP_DURATION, listener: View.OnClickListener) {
    this.setOnClickListener(object : SingleClickListener(skipDurationMs) {
        override fun onClickInternal(v: View) {
            listener.onClick(v)
        }
    })
}
