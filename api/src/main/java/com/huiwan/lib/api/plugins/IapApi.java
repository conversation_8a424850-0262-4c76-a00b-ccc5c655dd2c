package com.huiwan.lib.api.plugins;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.huiwan.lib.api.Api;
import com.huiwan.lib.api.DataCallback;

import java.util.HashMap;
import java.util.Map;

public interface IapApi extends Api {

    // 支付场景参数，用于创建订单后的埋点上报
    // 因为支付模块内部获取不到这些场景信息，需要外部调用支付接口前手动传入
    Map<String, Object> trackEventParamMap = new HashMap<>();

    /**
     *
     * @param activity the activity
     * @param goodsInfo the goods info, WespyGoods preferred
     * @param extParam extra Parameters for payment
     * @param callback the callback result of the payment
     */
    void doPay(Activity activity, Object goodsInfo, Map<String, String> extParam, DataCallback<PayResult> callback);

    void tryRestore();

    String getCurrency();

    void checkUnHandledPurchase(boolean lessLog);

    void init();

    String payChannel();

    boolean valid();

    default void updateTrackEventParam(Map<String, Object> map) {
        trackEventParamMap.clear();
        if (map != null) {
            trackEventParamMap.putAll(map);
        }
    }

    // 向外提供拷贝，内外直接操作map时互不影响
    @NonNull
    default Map<String, Object> getTrackEventParamCopy() {
        return new HashMap<>(trackEventParamMap);
    }

    default void clearTrackEventParam() {
        trackEventParamMap.clear();
    }


    class OrderInfo {
        @SerializedName("order_id")
        public String orderId;
        @SerializedName("order_money")
        public String price;
    }

    class PayResult {
        @SerializedName("wpOrderId")
        public String wpOrderId;
        @SerializedName("skuId")
        public String skuId;
        @SerializedName("msg")
        public String msg = "";

        public PayResult(String wpOrderId, String skuId) {
            this.wpOrderId = wpOrderId;
            this.skuId = skuId;
        }
    }
}
