package com.huiwan.lib.api.plugins;

import android.app.Activity;
import android.content.Context;

import androidx.activity.result.contract.ActivityResultContract;
import androidx.annotation.Nullable;

import com.huiwan.base.interfaces.SingleCallback;
import com.huiwan.constants.LoginSource;
import com.huiwan.lib.api.Api;
import com.huiwan.lib.api.DataCallback;

import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlinx.coroutines.flow.Flow;

public interface HwApi extends Api {

    void gotoVipMainActivity(Context context, String screenName);

    void gotoGiftVipToFriendActivity(Context context, int uid);

    void gotoGoodsListActivity(Context context);

    void gotoGoodsListActivity(Context context, boolean subCoinFirst, Map<String, Object> trackParam);

    void gotoHorizontalPay(Context context, String scene, int gameType);

    void giftShowBottomCoinNotEnough(Context context, int rid);

    void showCoinNotEnoughDialog(boolean bottomFirst);

    void showCoinNotEnoughDialog(boolean bottomFirst, Map<String, Object> map);

    Flow<Integer> showGameChipExchangeDialog(int gameType, int betLevel, Map<String, Object> trackExt);

    /**
     * @return 1:兑换成功 0: 取消兑换
     */
    @Nullable
    Flow<Integer> showGameChipExchangeDialog(int gameType, int gameMode, int mode, int betLevel, Map<String, Object> trackExt);

    boolean giftShowVoiceBroad(int rid);

    void showBuyDialog(String json, DataCallback<Integer> callback);

    void showFriendChooseDialog(Context context, boolean showSelf, int selectionLimit, SingleCallback<Object> callback);

    void handleCustomCommand(String payload);

    void gotoUserInfoDetailActivityFromGame(Context context, int uid, int gameType, String trackJsonExt);

    void gotoUserInfoDetailActivity(Context context, int uid, String source);

    void gotoUserInfoDetailActivity(Context context, int uid, String source, String trackJsonExt);

    void gotoReportGameUserActivity(Context context, int uid, int gameType, int rid);

    void gotoReportVoiceRoomUserActivity(Context context, int uid, int gameType, int rid);

    boolean interceptHttpCode(int code, boolean coinUseBottom);

    void gotoMainActivity(Context context);

    void gotoLoginActivity(Activity activity, boolean fromStartActivity, int from);

    void loginFacebook(Activity activity, String token, String userId);

    void loginHuawei(Activity activity, String token);

    void loginGoogle(Activity activity, String accessToken, String audience);

    void loginTwitter(Activity activity, String accessToken, String accessSecret, int authType);

    void sendLoginSms(String phone, int phoneCodeType, DataCallback<Integer> callback);

    void sendSms(Activity activity, String phone, int phoneCodeType, Map<String, String> map, Function1<Integer, Unit> onSucc);

    void smsLogin(Activity activity, String phone, LoginSource loginSource, String vCode, DataCallback<Object> callback);

    void selectLocal(Activity activity, int requestCode);

    void gotoCustomServiceActivity(Context context);

    Class<? extends Activity> getLocationSelectActivityClass();

    void gotoPropose2Activity(Context context, int selectUid);

    void gotoLoverHomeActivity(Context context, int targetId);

    void gotoSuperiorRoomDetailActivity(Context context, String screenName);

    void gotoVoiceRoomActivity(Context context, int roomId, int gameType, String source);

    void gotoFamilyMainActivity(Context context, String screenName);

    void gotoFamilyDetailActivity(Context mContext, int familyId, String source);

    void followRoomWithRefreshFollowRoom(final int rid, final boolean isFollow, DataCallback<Integer> callback);

    void gotoOtherPager(final Context mContext, String schemeText, String source);

    void gotoSetting(Context mContext);

    String getSimCountry();

    String getSimOperator();

    void getLoginConfig(DataCallback<Object> callback);

    void loginTourist(Activity activity);

    /**
     * 在没有base activity的地方可以使用api展示loading对话框
     *
     * @param activity
     * @param content
     * @param cancel
     */
    void showProgressDialog(Activity activity, String content, boolean cancel);

    /**
     * 在没有base activity的地方可以使用api隐藏loading对话框
     *
     * @param activity
     */
    void hideProgressDialog(Activity activity);

    /**
     * 退出登录后续操作
     */
    void logoutHelperOnLogout();

    /**
     * 退出登录后重启应用
     */
    void logoutRestart(Activity activity);

    /**
     * 获取华为的登录url
     *
     * @return
     */
    String getHuaweiLoginUrl();

    boolean checkGameStatus(Context context, int type, int gameType, int targetRid);

    /**
     * 判断是否在debug状态
     * 与BuildConfig.debug不同的是
     * 这个debug可以判断是否是超级用户状态下的release
     *
     * @return
     */
    boolean isDebug();

    /**
     * 举报录音段数
     */
    int getRecordVocCount();

    /**
     * 获取录音有效期
     *
     * @return
     */
    int getRecordVocValidSec();

    int getBackupAddrCount();

    int getBackupAddrSecond();

    void showGoods(Context context);

    void getRecommendGroupByGameTypeReq(int gameType, DataCallback<List<?>> callback);

    boolean isInVoiceRoom();

    void exitLocalVoiceRoom();

    String getSimAreaCode();

    ActivityResultContract<Void, String> genPwdActivityResultContract();
}
