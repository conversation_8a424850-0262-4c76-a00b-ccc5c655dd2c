package com.wepie.lib.api.plugins.track.config.os;


/**
 * Created by <PERSON><PERSON> on 2019-08-21.
 *
 * screen name 和view相关，一个activity可能多个 screen name
 */
public class TrackScreenName {

    public static final String HOME_PAGE = "首页";
    public static final String HOME_PAGE_GAME = "首页游戏入口";
    public static final String HOME_RECENT_GAME_PLAY = "最近常玩";
    public static final String COMPLETE_USER_PAGE = "完善资料页";
    public static final String COMPLETE_USER_PAGE2 = "完善资料";//理论上这里是同一个名字，暂时保持原样
    public static final String EDIT_USER_PAGE = "编辑资料页";
    public static final String LOGIN_PAGE = "登录页";
    public static final String PAY_PAGE = "充值页面";
    public static final String PAY_DIALOG = "充值弹窗";
    public static final String ANNOUNCEMENT_CAROUSEL_PAGE = "公告轮播页";
    public static final String ACTIVITY_TAB = "活动底tab";
    public static final String VOICE_ROOM_LIST_TAB = "语音房tab";
    public static final String DEEP_LINK = "deeplink";
    public static final String GAME_PAGE = "游戏页";
    public static final String OPEN_NOTIFY_DIALOG = "开启推送提醒弹窗";
    public static final String SEND_RED_PACKET = "发红包";
    public static final String RED_PACKET_LIST = "红包列表页面";
    public static final String SIGN_IN_DIALOG = "签到弹窗";
    public static final String SIGN_IN_SUCCESS_DIALOG = "签到成功弹窗";
    public static final String WEREWOLF_GAME_OVER_DIALOG = "狼人杀结算弹窗";
    public static final String WEREWOLF_12_GAME_OVER_DIALOG = "狼人杀12人结算弹窗";
    public static final String DRAW_GUESS_GAME_OVER_DIALOG = "你画我猜结算弹窗";
    public static final String SEND_BROADCAST_PAGE = "发广播页";
    public static final String BUY_VIP_DIALOG = "购买会员弹窗";
    public static final String DONATE_VIP_PAGE = "赠送会员页";
    public static final String VIP_MAIN_PAGE = "会员主页面";
    public static final String SEND_GIFT_DIALOG = "送礼物弹窗";
    public static final String VOICE_SETTING_PAGE = "语音房房间设定页";
    public static final String VOICE_THEME_SETTING_PAGE = "语音房房间背景选择";
    public static final String GIFT_DESC_DIALOG = "礼物介绍弹窗";
    public static final String FAMILY_RECOMMEND_LIST = "家族推荐列表页";

    public static final String ACTIVITY_MESSAGE_PAGE = "活动消息页";
    public static final String USER_INFO_SET = "资料设置";
    public static final String RECOMMEND_BUY_BUBBLE = "道具推荐购买气泡弹窗";
    public static final String RECOMMEND_BUY_MIC_ANIM = "道具推荐购买麦克风弹窗";
    public static final String HOME_USER_RECOMMEND = "首页推荐列表";
    public static final String CHAT_CONVERSATION = "消息列表";
    public static final String SELF_HOMEPAGE = "个人主页";
    public static final String SELF_HOMEPAGE_2 = "个人资料页";

    public static final String SPY_GAME_END_MAIN_DIALOG = "卧底结算弹窗";
    public static final String SPY_GAME_END_ACCURACY_DIALOG = "卧底准确率弹窗";
    public static final String SPY_GAME_END_MATCH_CLICK_DIALOG = "卧底默契点击弹窗";
    public static final String SPY_GAME_END_MATCH_STAY_DIALOG = "卧底默契挽留弹窗";

    public static final String MUSIC_HUM_GAME_IN = "抢唱游戏中";
    public static final String MUSIC_HUM_MATCH_PAGER = "抢唱匹配页";
    public static final String MUSIC_HUM_GAME_RESULT = "抢唱结算页";
    public static final String MUSIC_HUM_SONG_CARD = "抢唱题卡";
    public static final String MUSIC_HUM_QUICK_MSG = "抢唱快捷消息";
    public static final String MUSIC_HUM_FACE_MSG = "抢唱表情";

    public static final String FIRST_CHARGE_FIRST_WIN = "游戏首胜礼包弹窗";
    public static final String FIRST_CHARGE_SIX_WIN = "游戏6胜礼包弹窗";
    public static final String FIRST_CHARGE_TEN_WIN = "游戏10胜礼包弹窗";
    public static final String FIRST_CHARGE_CARE_ONE = "2000守护礼包弹窗";
    public static final String FIRST_CHARGE_CARE_TWO = "3000守护礼包弹窗";
    public static final String FIRST_CHARGE_QIXI_ONE = "七夕特权礼包";
    public static final String FIRST_CHARGE_QIXI_TWO = "七夕恩爱超值礼包";

    public static final String AB_TEST_LOGIN_PAGE = "ABtest登录页";

    public static final String AUDIO_MATCH_GAMING = "语音匹配完成页面";
    public static final String AUDIO_MATCH_COMPLETE = "语音匹配结束页";

    public static final String CHAT_SETTING = "聊天设置页";
    public static final String CHAT_ROAMING_PAGE = "聊天记录漫游";

    public static final String VOICE_ROOM_LIST_BANNER = "语音房banner";
    public static final String GOODS_LIST_BANNER = "储值页banner";

    public static final String SAY_GUESS_GAME_RESULT = "演猜结算页";
    public static final String SAY_GUESS_GAME_RESULT_DIALOG = "演猜结算页推荐用户弹窗";
    public static final String SAY_GUESS_GAME_RESULT_USER = "演猜结算页推荐用户";
    public static final String SAY_GUESS_GUESS_WORK = "猜词";
    public static final String SAY_GUESS_TEAM = "演猜组队";
    public static final String GAME_INVITE_PAGER = "邀请好友页";
    public static final String SAY_GUESS_GAME_END_PAGER = "演猜结算页";
    public static final String SAY_GUESS_DIALOG = "演猜弹窗";

    public static final String SEND_REDPACKET = "发红包";
    public static final String ADVERT_SHOW = "开屏展示";
    public static final String ADVERT_CLICK = "开屏点击";

    public static final String XROOM_TEAM = "组队";
    public static final String XROOM_CREDIT_GIFT_DIALOG = "信誉礼包弹窗";

    public static final String MENTOR_USER_SETTING = "用户设置页";
    public static final String MENTOR_USER_CHATTING = "聊天设置页";
    public static final String MENTOR_TEACHER = "我的师父页";
    public static final String MENTOR_STUDENT = "我的徒弟页";
    public static final String MENTOR_FIND_STUDENT = "找徒弟页";

    public static final String GAME_ROOM = "桌游房间";
    public static final String SQUID_GAME_HOMEPAGE = "鱿鱼游戏首页";
    public static final String SUGAR_GAME_HOMEPAGE = "抠糖饼游首页";
    public static final String WEREWOLF_DIALOG = "狼人杀弹窗";
    public static final String SPY_DIALOG = "谁是卧底首页";
    public static final String MUSIC_HUM_PAGE = "抢唱页面";

    public static final String DISCOVER_PAGE = "发现";

    public static final String GAME_ROOM_PAGE = "桌游房间页";

    public static final String SPACEWOLF_INVITE = "太空拉新邀请页";
    public static final String SPACEWOLF_ASSIST = "太空拉新助力页";
    public static final String SPACEWOLF_HOME = "太空狼人首页";

    public static final String AVATAR_MUSIC_HUM_MATCH = "抢唱匹配页";
    public static final String AVATAR_SHOP_BANNER = "商城banner";
    public static final String AVATAR_DIALOG_BEFORE_MUSIC_HUM = "入口引导弹窗";
    public static final String AVATAR_DIALOG_AFTER_MUSIC_HUM = "游戏后引导弹窗";
    public static final String AVATAR_SCREEN_SHOT = "会玩秀截图";
    public static final String VOICE_ROOM_CHAT = "语音房聊天页";

    public static final String CREATE_ADVANCED_ROOM = "开通高级语音房";
    public static final String CREATE_VOICE_ROOM = "创建语音房间";
    public static final String SELECT_ROOM_RID = "选择房间号";
    public static final String VOICE_ROOM_LIST = "语音房列表页";
    public static final String VIDEO_ROOM_SELECT_VIDEO = "选择视频";
    public static final String VIDEO_ROOM_PLAY_LIST = "播放列表";

    public static final String VOICE_ROOM_PAGE = "语音房页";
    public static final String VOICE_MODE_MSG = "语音房公屏";
    public static final String VOICE_MUSIC_CLI = "语音房音乐控件";
    public static final String VOICE_FOCUS_LIST = "语音房关注列表页";
    public static final String VOICE_ROOM_MORE = "语音房更多";
    public static final String VIDEO_ROOM_WATCH_TV = "语音房观影页";
    public static final String VOICE_ROOM_NAVIGATION = "语音房导航栏";

    public static final String VOICE_MIC_CLI = "语音房麦序控件";
    public static final String VOICE_ACTIVITY_RESOURCE = "语音房公屏资源位";

    public static final String RECOVER_DIALOG = "断线重连弹窗";
    public static final String GROUP_CHAT_PAGE = "群聊页";

    public static final String LANG_PREF_DIALOG = "语言偏好弹窗";
    public static final String AGE_PREF_DIALOG = "年龄偏好弹窗";
    public static final String SETTING_PAGE = "设置页面";

    public static final String FAMILY_RANK_PAGE = "家族榜";

    public static final String LOVER_NEST = "情侣小窝";
    public static final String BG_SETTING_PAGE = "设置背景图片页";
    public static final String RANK_SELECT_AREA_PAGE = "排行榜选择地区页";
    public static final String RANK_JK_RANK = "Jackaroo";
    public static final String POPULARITY_RANK = "人气排行榜";
    public static final String VIP_RANK = "会员排行榜";
    public static final String COUPLE_RANK = "情侣排行榜";
    public static final String VOICE_ROOM_RANK = "语音房排行榜";
    public static final String FAMILY_RANK = "家族排行榜";
    public static final String AVATAR_SHOW_RANK = "会玩秀排行榜";
    public static final String BALOOT_RANK = "排行榜";

    public static final String MJ_INVITE = "麻将组队页";
    public static final String MJ_GAME_RESULT = "麻将结算页";
    public static final String AUDIO_MATCH_SETTING_PAGE = "语音匹配设置页";

    public static final String PRAISE_GUIDE_DIALOG = "好评引导弹窗";


    public static final String BATTLE_GAME_PAGE = "对战小游戏页";
    public static final String MY_TAB_PAGE = "我的页面";
    public static final String VOICE_ROOM_GALA_WEB = "语音房公屏";

    public static final String CHAT_FOR_JUDGE = "法官聊天页";

    public static final String SPY_PAGE = "谁是卧底首页";
    public static final String SPY_CREATE_PAGE = "谁是卧底创建房间页";

    public static final String VOICE_ROOM_RECOMMEND = "语音房推荐弹窗";
    public static final String VOICE_ROOM_RECOMMEND_CLICK = "房间推荐弹窗";
    public static final String VOICE_ROOM_ACTIVITY_STATE = "语音房活动预告资源位";
    public static final String VOICE_ROOM_ACTIVITY_STATE_CLICK = "语音房公屏";

    public static final String VOICE_ROOM_ACTIVITY_CENTER_BANNER = "Popular下的banner";

    public static final String VOICE_ROOM_POPULAR_PAGE = "Popular页面";

    public static final String VOICE_ROOM_ADMIN_MANAGE_ACTIVITY = "房管办活动页面";

    public static final String VOICE_ROOM_PAGE_TAB = "语音房中心首页";

    public static final String USER_REGISTER_DIALOG = "注册选择区服弹窗";

    public static final String UNITY_QUALITY_DIALOG = "画质调节弹窗";

    public static final String DRAGON_RESULT_PAGE = "小玩法结算弹窗";

    public static final String DRAGON_RANK_PAGE = "小玩法排行榜";

    public static final String SET_BG_CHAT = "聊天设定页";
    public static final String SET_BG_COMMON_CHAT = "App全局设置页";
    public static final String SUB_WANYOU_RECOMMEND = "玩友推荐";

    // 群聊相关
    public static final String GROUP_IN_MJ = "麻将首页";
    public static final String GROUP_IN_BOOM_CAT = "炸弹猫首页";
    public static final String GROUP_IN_INTEREST = "兴趣群聊页";
    public static final String GROUP_IN_DETAIL = "群资料页";
    public static final String GROUP_IN_VOICE_ROOM_DIALOG = "语音房群聊弹窗";
    public static final String GROUP_IN_FRIEND_CIRCLE = "玩友圈分享";
    public static final String GROUP_IN_CHAT_SHARE = "聊天分享";
    public static final String GROUP_IN_MAIN = "群聊主页";
    public static final String GROUP_VOICE_ROOM_IN_CHAT = "兴趣群聊天页";
    public static final String FRIEND_PLAY = "好友在玩";
    public static final String FAMILY_PLAY = "家族在玩";
    public static final String ACCEPT_FRIEND_GAME_POPUP = "添加好友下拉弹窗";
    public static final String VISIT_ME = "谁看过我";
    public static final String TASK_FAMILY_TASK = "任务-家族任务";
    public static final String FAMILY_WELCOME = "家族迎新";

    public static final String MSG_LONG_CLICK_WINDOW = "消息长按弹窗";
    public static final String CHOOSE_PHOTO_WINDOW = "选择相片弹窗";
    public static final String SINGLE_CHAT_PAGE = "个人聊天页";

    public static final String GIFT_PACK_DIALOG = "礼包弹窗";

    public static final String VOICE_ROOM_UTIL_DIALOG = "语音房控件弹窗";
    public static final String MEMBER_INVITE_DIALOG = "成员团邀请弹窗";
    public static final String VOICE_ROOM_PERSONAL_DIALOG = "语音房个人弹窗";
    public static final String GROUP_SEARCH = "群聊搜索";
    public static final String USER_TITLE_PAGE = "称号橱窗页";
    public static final String ENERGY_DIALOG = "体力值弹窗";
    public static final String GAME = "游戏";
    public static final String GAME_HOME_PAGE = "游戏首页";
    public static final String BIND_ACCOUNT_TIP_DIALOG = "绑定账号提示弹窗";
    public static final String GAME_SELECT_HOME_DIALOG = "匹配二级选场弹窗";

    public static final String SHARE_SELECT_TYPE = "选择H5分享方式";
    public static final String SHARE_PAGE = "分享页";
    public static final String SHARE_OUTSIDE_PAGE = "站外邀请页";

    public static final String NEW_USER_INTRODUCE = "新手引导";
    public static final String NEW_USER_TUTORIAL = "新手教程";

    public static final String AVATAR_COLLECTION_DIALOG = "会玩秀收藏值弹窗";
    public static final String AVATAR_SHOW_TIP_DIALOG = "会玩秀展示提示弹窗";

    public static final String SELECT_PAY_TYPE_DIALOG = "选择支付方式弹窗";
    public static final String COCOS_MATCH_PAGE = "匹配页";
    public static final String GOLD = "金币";
    public static final String DIAMOND = "钻石";
    public static final String PAGE_VOICE_SEND_GIFT = "语音房送礼页";

    public static final String MAIN_GAME_PAGE = "大厅主页";
    public static final String GAME_PAGE_ENTRY = "游戏进场页";
    public static final String GAME_IN_PLAYING = "游戏中页";
    public static final String GAME_RESULT = "游戏结束页";

    public static final String GAME_STORE_PAGE = "游戏商城页";
    public static final String NATIVE_STORE_PAGE = "原生商城页";

    public static final String CREATE_COMPETITION_PAGE = "创建比赛";
    public static final String VIP_GAME_ROOM_CREATE = "VIP游戏房创房";

    public static final String PLAYING_GAMING = "游戏中";
    public static final String BEFORE_GAME_START = "开始游戏前";
    public static final String RANKING_UP_ANIM_VIEW = "排名结算";

    public static final String QUALIFYING_HOME_PAGE = "排位页";
    public static final String QUALIFYING_SETTLE_PAGE = "排位赛结算页";

    public static final String WEJOY_SETTING_PAGE = "设置页";
    public static final String CLEAR_CACHE_PAGE = "清除缓存页";
    public static final String CLEAR_GAME_CACHE_PAGE = "管理游戏资源";
    public static final String MEMORY_NOT_ENOUGH = "手机储存空间不足弹窗";

    public static final String COMPETITION_LOBBY = "赛事大厅页";
    public static final String COMPETITION_PAGE = "赛程表页";

    public static final String FAMILY_WELCOME_DIALOG = "欢迎加入家族弹窗";
    public static final String FAMILY_BENEFIT_DETAIL_PAGE = "家族福利详情页";
    public static final String FAMILY_CONTRIBUTE_RANK = "贡献榜";
    public static final String FAMILY_GIFT_RANK = "送礼榜";
    public static final String FAMILY_RECEIVED_RANK = "收礼榜";
    public static final String FAMILY_GAME_RANK = "段位榜";
}
