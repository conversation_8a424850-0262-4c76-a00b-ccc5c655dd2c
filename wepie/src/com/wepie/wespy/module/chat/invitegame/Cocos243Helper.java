package com.wepie.wespy.module.chat.invitegame;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.configservice.international.service.GlobalConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.littlegame.cocos.CocosLaunchInfo;
import com.huiwan.littlegame.cocos.CocosWebActivity;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.littlegame.LittleGameInfo;
import com.wejoy.littlegame.LittleGameRoomInfo;
import com.wejoy.littlegame.LittleGameSeat;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.CocosLaunchTrack;
import com.wepie.wespy.cocosnew.CocosTrackKt;
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameStartConfig;
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil;
import com.wepie.wespy.cocosnew.update.CocosVersionManager;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.abtest.AbTestManager;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;

public class Cocos243Helper {
    private static final String TAG = "CocosHelper";

    /**
     * 直接进入 cocos 内游戏页
     */
    public static void gotoCocosGameActivity(Context context, LittleGameStartConfig startConfig) {
        VoiceManager.getInstance().leaveChannel();
        // 预加载资源入口，具体是否会执行视abTest配置而定
        ApiService.of(ILittleGameApi.class).preloadCocosAsset(context, startConfig.getGameType());
        LittleGameInfo gameInfo = LittleGame.fromTeamInfoNoSeat();
        int gameMode = gameInfo.getGameMode();
        if (gameMode <= 0 && startConfig.getGameMode() > 0) {
            HLog.d(TAG, HLog.USR, "jumpCocos user start config gameMode {}", gameMode);
            gameMode = startConfig.getGameMode();
        }
        List<LittleGameSeat> seatList = new ArrayList<>();
        for (VoiceRoomInfo.SeatInfo seatInfo : startConfig.getVoiceRoomInfo().seatInfos) {
            seatList.add(new LittleGameSeat(seatInfo.uid, seatInfo.seat_num));
        }
        LittleGameRoomInfo roomInfo = new LittleGameRoomInfo(startConfig.getVoiceRoomInfo().rid, seatList);
        gameInfo.setRoomInfo(roomInfo);
        gameInfo.setBeforeStartData(startConfig.getBeforeGameStart());
        gameInfo.setGameType(startConfig.getGameType());
        gameInfo.setRestore(startConfig.isRestore());
        gameInfo.setFollowUid(startConfig.getWatcherTargetUid());
        gameInfo.setJumpMatch(startConfig.getJumpMatch());
        gameInfo.setCompetitionForbidVoice(startConfig.getCompetitionForbidVoice());
        gameInfo.setCid(startConfig.getCompetitionId());
        Intent intent = CocosWebActivity.getIntent(context, CocosLaunchInfo.ENTER_MODE_JUMP_GAME);
        LittleGame.addGameInfoToIntent(intent, gameInfo,
                AbTestManager.getInstance().getConnectorSupport().contains(startConfig.getGameType()));
        GlobalConfigHelper.addGlobalConfig(intent);
        HLog.d(TAG, HLog.USR, "jumpCocos: {}, {}, {}", gameInfo.getRid(), gameInfo.getGameType(), gameMode);
        addCommonArg(intent, gameInfo.getGameType());
        intent.putExtra(CocosLaunchInfo.KEY_GAME_MODE, gameMode);
        context.startActivity(intent);
        applyEnterAnim(context);
    }

    /**
     * 进入 cocos 内主页
     */
    public static void gotoMainInCocos(Context context, int gameType, int gameMode) {
        // 预加载资源入口，具体是否会执行视abTest配置而定
        ApiService.of(ILittleGameApi.class).preloadCocosAsset(context, gameType);
        Intent intent = CocosWebActivity.getIntent(context, CocosLaunchInfo.ENTER_MODE_DIRECT_JUMP);
        intent.putExtra(CocosLaunchInfo.KEY_GAME_TYPE, gameType);
        intent.putExtra(CocosLaunchInfo.KEY_GAME_MODE, gameMode);
        addCommonArg(intent, gameType);
        context.startActivity(intent);
        applyEnterAnim(context);
        CocosTrackKt.cocosTrackLaunch(new CocosLaunchTrack.LaunchCocos(gameType));
    }


    /**
     * 使用 deeplink 跳转到 cocos 内
     * deeplink跳转不走预加载
     */
    public static void gotoCommonInCocosWithDeeplink(Context context, String deeplink, int gameType, String source) {
        LittleGameResUtil.INSTANCE.checkLoadRes(context, gameType, source, b -> {
            Intent intent = CocosWebActivity.getIntent(context, CocosLaunchInfo.ENTER_MODE_DEEP_LINK);
            intent.putExtra(CocosLaunchInfo.KEY_DEEP_LINK, deeplink);
            intent.putExtra(CocosLaunchInfo.KEY_GAME_TYPE, gameType);
            addCommonArg(intent, gameType);
            context.startActivity(intent);
            applyEnterAnim(context);
            return Unit.INSTANCE;
        });
    }

    /**
     * 加入队伍到 cocos 内匹配页面
     * cocos 会在游戏内 syncTeam
     */
    public static void followMatchInCocos(Context context, int gameType, int enterMode) {
        Intent intent = CocosWebActivity.getIntent(context, enterMode);
        intent.putExtra(CocosLaunchInfo.KEY_GAME_TYPE, gameType);
        addCommonArg(intent, gameType);
        context.startActivity(intent);
        applyEnterAnim(context);
    }

    private static void addCommonArg(Intent intent, int gameType) {
        int version = CocosVersionManager.getInstance().getGameVersion(gameType);
        intent.putExtra(CocosLaunchInfo.KEY_COCOS_RES_VERSION, version);
        intent.putExtra(CocosLaunchInfo.KEY_GAME_TYPE, gameType);
    }

    private static void applyEnterAnim(Context context) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity != null) {
            activity.overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        }
    }
}
