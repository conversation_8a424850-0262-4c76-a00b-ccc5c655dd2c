package com.wepie.wespy.module.chat.gamemodel;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.emoji.view.resource.MultipleEmojiHelper;
import com.wepie.wespy.R;

import java.util.List;

/**
 * 通用消息UI元素的ViewHolder，管理bubbleItem、emojiLayout和quoteItem
 *
 * <AUTHOR>
 */
public class CommonMessageViewHolder {
    
    // 通用UI元素
    private final ChatMsgBubbleItem bubbleItem;
    private final LinearLayout emojiLayout;
    private final MsgQuoteModel quoteItem;

    public CommonMessageViewHolder(ViewGroup rootView) {
        bubbleItem = rootView.findViewById(R.id.chat_msg_bubble_item);
        emojiLayout = rootView.findViewById(R.id.emoji_layout);
        quoteItem = rootView.findViewById(R.id.quote_item);
    }

    /**
     * 显示大表情
     */
    public void showBigEmojis(List<EmojiItem> emojiItems, View.OnLongClickListener listener) {
        if (emojiLayout != null && !emojiItems.isEmpty()) {
            emojiLayout.setVisibility(View.VISIBLE);
            MultipleEmojiHelper.showEmojis(emojiLayout, emojiItems, listener);
        }
    }

    /**
     * 隐藏大表情布局
     */
    public void hideEmojiLayout() {
        if (emojiLayout != null) {
            emojiLayout.setVisibility(View.GONE);
        }
    }

    /**
     * 显示引用消息
     */
    public void showQuoteItem() {
        if (quoteItem != null) {
            quoteItem.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏引用消息
     */
    public void hideQuoteItem() {
        if (quoteItem != null) {
            quoteItem.setVisibility(View.GONE);
        }
    }

    /**
     * 获取引用消息组件
     */
    public MsgQuoteModel getQuoteItem() {
        return quoteItem;
    }

    /**
     * 获取ChatMsgBubbleItem
     */
    public ChatMsgBubbleItem getBubbleItem() {
        return bubbleItem;
    }
}
