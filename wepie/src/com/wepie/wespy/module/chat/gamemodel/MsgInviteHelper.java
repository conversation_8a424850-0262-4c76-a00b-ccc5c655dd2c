package com.wepie.wespy.module.chat.gamemodel;

import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_BOXING_AGENT_INVITE;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_COCOS_TEAM_INVITE_GROUP;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_SAY_GUESS_INVITE_GROUP;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_SQUID_INVITE;
import static com.wepie.wespy.model.entity.ChatMsg.SUBTYPE_XROOM_INVITE;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.littlegame.cocos.CocosLaunchInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.HeadImageLoader;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.util.CommonUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.InviteCardInfo;
import com.wepie.wespy.model.entity.JumpCocosTeamInfo;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;

public class MsgInviteHelper {

    public static void updateNewInvite(View root, String content, InviteCardInfo cardInfo, ChatBubbleItem bubbleItem, View.OnClickListener onClickListener, View.OnLongClickListener longClickListener) {
        TextView inviteMsgContent = root.findViewById(R.id.invite_msg_content);
        TextView inviteTitle = root.findViewById(R.id.invite_msg_title);
        TextView inviteSubTitle = root.findViewById(R.id.invite_msg_sub_title);
        CustomCircleImageView inviteIcon = root.findViewById(R.id.invite_msg_icon);

        inviteMsgContent.setText(content);
        String title = "", subTitle = "", icon = "";
        if (cardInfo.inviteType == InviteCardInfo.TYPE_GAME_INVITE) {
            GameConfig gameConfig = ConfigHelper.getInstance().getGameConfig(cardInfo.gameType);
            title = gameConfig.getName();
            icon = gameConfig.getIconUrl();
            subTitle = cardInfo.getSubTitle();
        } else if (cardInfo.inviteType == InviteCardInfo.TYPE_VOICE_INVITE) {
            title = cardInfo.roomName;
            icon = cardInfo.headImg;
            subTitle = cardInfo.getSubTitle();
        } else if (cardInfo.inviteType == InviteCardInfo.TYPE_VOICE_ACT_INVITE) {
            title = cardInfo.actName;
            icon = cardInfo.actImg;
            subTitle = cardInfo.actIntroduction;
        }
        if (TextUtils.isEmpty(subTitle)) {
            ViewGroup.LayoutParams layoutParams = inviteTitle.getLayoutParams();
            if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                ((ConstraintLayout.LayoutParams) layoutParams).topMargin = ScreenUtil.dip2px(20);
            }
        }
        inviteTitle.setText(title);
        inviteSubTitle.setText(subTitle);
        if (!TextUtils.isEmpty(icon)) {
            WpImageLoader.load(icon, inviteIcon, HeadImageLoader.genHeadLoadInfo());
        }
        root.setOnClickListener(onClickListener);
        root.setOnLongClickListener(longClickListener);

        inviteTitle.setTextColor(bubbleItem.getGiftDescColor());
        inviteSubTitle.setTextColor(bubbleItem.getGiftDescColor());
        inviteMsgContent.setTextColor(bubbleItem.getTextColor());
    }

    public static void handleCardJump(Context context, InviteCardInfo cardInfo, int subType) {
        if (CommonUtil.isInGame()) {
            ToastUtil.show(R.string.common_in_gaming);
            return;
        }
        if (subType == SUBTYPE_COCOS_TEAM_INVITE_GROUP) {
            if (cardInfo.tid <= 0 || cardInfo.gameType <= 0) {
                ToastUtil.show(R.string.common_invite_error);
            } else {
                JumpCocosTeamUtil.jumpToCocosGame(
                        JumpCocosTeamInfo.build(context).setTid(cardInfo.tid)
                                .setGameType(cardInfo.gameType).setFromChat(true)
                                .setEnterVipReferScreenName(TrackButtonName.INVITE)
                                .setInvite(true).setTargetUid(cardInfo.sendUid),
                        CocosLaunchInfo.ENTER_MODE_INVITE_TO_COCOS);
            }
        } else if (subType == SUBTYPE_SAY_GUESS_INVITE_GROUP) {
            if (cardInfo.rid <= 0) {
                ToastUtil.show(R.string.common_team_error);
            } else {
                EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(context, cardInfo.rid, cardInfo.gameType, cardInfo.passWord).setInvite(true).setSource(TrackSource.INVITE);
                JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
            }
        } else {
            if (cardInfo.rid <= 0) {
                ToastUtil.show(R.string.common_room_error);
            } else {
                EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(context, cardInfo.rid, cardInfo.gameType, cardInfo.passWord).setInvite(true).setSource(TrackSource.INVITE);
                JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
            }
        }
    }

    public static boolean isXRoomInvite(int subType) {
        return subType == SUBTYPE_XROOM_INVITE || subType == SUBTYPE_SQUID_INVITE || subType == SUBTYPE_BOXING_AGENT_INVITE;
    }

}
