package com.wepie.wespy.module.chat.gamemodel;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.UserService;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.CenterAlignImageSpan;
import com.wepie.wespy.model.entity.InviteCardInfo;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.game.game.activity.TextJumpExtra;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

/**
 * 发送文本的model
 *
 * <AUTHOR>
 */
public class MsgReceiveTextModel extends LinearLayout {
    private final TextView textTv;
    private final View oldTextLayout;
    private final View receiveLayout;
    private final View inviteLayout;

    private final GiftTextViewHolder giftViewHolder;
    private final MsgCommonTextViewHolder commonViewHolder;

    public MsgReceiveTextModel(Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.msg_receive_text_view, this);

        // 初始化基本UI元素
        oldTextLayout = findViewById(R.id.text_cl);
        receiveLayout = findViewById(R.id.msg_receive_text_lay);
        textTv = findViewById(R.id.text_tv);
        inviteLayout = findViewById(R.id.invite_layout);

        // 初始化ViewHolder
        giftViewHolder = new GiftTextViewHolder(this.findViewById(R.id.new_gift_lay));
        commonViewHolder = new MsgCommonTextViewHolder(this);
    }

    public MsgReceiveTextModel(Context context) {
        this(context, null);
    }

    public void showNotSupportWithBubbleId(int id) {
        updateLayoutVisibility(false, false, true, true, false);
        textTv.setTextColor(commonViewHolder.getTextColor());
        textTv.setGravity(Gravity.START);
        textTv.setText(ResUtil.getStr(R.string.common_room_not_support_msg));
    }

    private void updateOldText(String content, String clickContent, OnLongClickListener onLongClickListener, View.OnClickListener onClickListener) {
        textTv.setTextColor(commonViewHolder.getTextColor());
        TextSpanUtil.setCustomSpan(getContext(), commonViewHolder.getBubbleItem().getDeepLinkColor(), textTv, this, content, clickContent, onClickListener,
                onLongClickListener);
        textTv.setGravity(textTv.length() <= 2 ? Gravity.CENTER : (Gravity.START | Gravity.CENTER_VERTICAL));
    }

    public void showInvite(final WPMessage msg, OnLongClickListener listener) {
        View.OnClickListener onClickListener = null;
        String clickContent = "";
        if (msg.getExtensionSubType() == WPMessage.EXTENSION_TYPE_CONVENE_PLAYER) {
            clickContent = ResUtil.getStr(R.string.common_click_enter);
            onClickListener = v -> MsgTextHelper.enterRoom(getContext(), msg);
        } else if (msg.getExtensionSubType() == WPMessage.EXTENSION_TYPE_INVITE_FRIEND) {
            clickContent = ResUtil.getStr(R.string.common_click_apply);
            onClickListener = v -> {
                // todo 阿语服原来不处理此类型
//                    MsgTextHelper.applyStay(getContext(), msg);
            };
        }
        InviteCardInfo cardInfo = msg.getWithInviteCardWithExtension();
        commonViewHolder.showBubble(msg.getBubbleId(), false);
        if (cardInfo.isNewInvite) {
            showInviteLayoutVisibilty();
            MsgInviteHelper.updateNewInvite(inviteLayout, msg.getContent(), cardInfo, commonViewHolder.getBubbleItem(), onClickListener, listener);
        } else {
            updateLayoutVisibility(false, false, true, true, false);
            if (null != onClickListener) {
                updateOldText(msg.getContent(), clickContent, listener, onClickListener);
            }
        }

        textTv.setGravity(textTv.length() <= 2 ? Gravity.CENTER : (Gravity.START | Gravity.CENTER_VERTICAL));
    }

    public void showText(WPMessage msg, OnLongClickListener listener) {
        if (!TextUtils.isEmpty(msg.getRefMid())) {
            updateLayoutVisibility(false, false, false, false, false);
            commonViewHolder.showQuoteItem();
            ChatManager.getInstance().findQuoteMsg(msg.getRefMid(), ILifeUtil.toLife(this), refMsg -> commonViewHolder.getQuoteItem().bind(msg, refMsg, listener));
            return;
        }
        InviteCardInfo cardInfo = InviteCardInfo.build(msg.getExtension());
        TextSpanUtil.DeepLinkCallback callback = null;
        if (UserService.get().isFaGuan(msg.getSend_uid())) {
            callback = s -> {
                Map<String, Object> map = new ArrayMap<>();
                if (s.startsWith(TextSpanUtil.WESPY_DEEP_LINK + "http")) {
                    String url = URLDecoder.decode(s);
                    Uri uri = Uri.parse(url.substring(25));
                    String actId = uri.getQueryParameter("act_id");
                    map.put("act_id", actId);
                }
                map.put("message_id", msg.getMid());
                map.put("message_type", TrackString.ACTIVITY_PUSH);
                ApiService.of(TrackApi.class).appClick(TrackScreenName.CHAT_FOR_JUDGE, TrackSource.JUDGE_MSG, map);
            };
        }
        if (cardInfo.isNewInvite) {
            showInviteLayoutVisibilty();
            String deeplink = TextSpanUtil.getDeepLink(msg.getContent());
            String content = TextSpanUtil.removeDeepLink(msg.getContent(), deeplink);
            TextSpanUtil.DeepLinkCallback finalCallback = callback;
            View.OnClickListener onClickListener = v -> {
                if (null != finalCallback) {
                    finalCallback.onLink(deeplink);
                }
                TextJumpExtra textJumpExtra = new TextJumpExtra(commonViewHolder.getBubbleItem().getDeepLinkColor(), "source");
                String source = TrackSource.LINK;
                if (cardInfo.inviteType == InviteCardInfo.TYPE_VOICE_ACT_INVITE) {
                    source = TrackSource.ACTIVITY_TIPS;
                }
                JumpCommon.gotoOtherPager(getContext(), deeplink, source, "", textJumpExtra, null);
            };
            commonViewHolder.showBubble(msg.getBubbleId(), false);
            MsgInviteHelper.updateNewInvite(inviteLayout, content, cardInfo, commonViewHolder.getBubbleItem(), onClickListener, listener);
        } else {
            List<EmojiItem> emojiItems = EmojiHelper.getContinuousEmoji(msg.getContent());
            boolean showBigEmoji = !emojiItems.isEmpty();
            if (showBigEmoji) {
                updateLayoutVisibility(false, false, false, false, true);
                commonViewHolder.showBigEmojis(emojiItems, listener);
            } else {
                updateChatView(msg);
                TextSpanUtil.checkTextSpan(getContext(), commonViewHolder.getBubbleItem().getDeepLinkColor(), msg.getSend_uid(), -1, receiveLayout, textTv, msg.getContent(), "", listener, callback);
            }
        }
    }

    public void showCocosInvite(final WPMessage msg, OnLongClickListener listener) {
        int tid = msg.getExtensionCocosGameTid();
        int gameType = msg.getExtensionCocosGameType();
        View.OnClickListener onClickListener = v -> MsgTextHelper.enterCocos(getContext(), msg, tid, gameType);
        InviteCardInfo cardInfo = InviteCardInfo.build(msg.getExtension());
        cardInfo.gameStyle = InviteCardInfo.COCOS_GAME_STYLE;
        commonViewHolder.showBubble(msg.getBubbleId(), false);
        if (cardInfo.isNewInvite) {
            showInviteLayoutVisibilty();
            MsgInviteHelper.updateNewInvite(inviteLayout, msg.getContent(), cardInfo, commonViewHolder.getBubbleItem(), onClickListener, listener);
        } else {
            updateLayoutVisibility(false, false, true, true, false);
            updateOldText(msg.getContent(), ResUtil.getStr(R.string.common_click_enter), listener, onClickListener);
        }
    }

    private void showInviteLayoutVisibilty() {
        updateLayoutVisibility(false, true, false, false, false);
    }

    private void updateChatView(final WPMessage msg) {
        setOnClickListener(null);
        int bubbleID = msg.getBubbleId();
        commonViewHolder.showBubble(bubbleID, false);
        boolean filter = giftViewHolder.showGift(msg, commonViewHolder.getBubbleItem(),false);
        if (filter) {
            updateLayoutVisibility(false, false, true, true);
        } else {
            updateLayoutVisibility(false, true, true, false);
            SpannableStringBuilder ssb = new SpannableStringBuilder();
            if (!TextUtils.isEmpty(msg.getExtension()) && msg.isExtensionTypeGift(msg.getExtensionType())) {
                handleGiftMsg(ssb);
            }
            textTv.setTextColor(commonViewHolder.getTextColor());
            EmojiHelper.parseEmoji2Ssb(textTv.getContext(), ssb, msg.getContent(), 16);
            textTv.setText(ssb);
            checkMeasureSelf();
        }
    }

    private void handleGiftMsg(SpannableStringBuilder ssb) {
        ssb.append(ResUtil.getStr(R.string.take));
        Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.chat_ic_gift);
        if (drawable != null) {
            drawable.setBounds(0, 0, ScreenUtil.dip2px(16), ScreenUtil.dip2px(16));
            ssb.setSpan(new CenterAlignImageSpan(drawable), 0, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        ssb.append(' ');
    }

    private void checkMeasureSelf() {
        textTv.setGravity(textTv.getText().length() > 3 ? Gravity.START | Gravity.CENTER_VERTICAL : Gravity.CENTER);
        textTv.requestLayout();
    }

    private void updateLayoutVisibility(boolean isNewInvite, boolean showOldContent, boolean showOldReceive, boolean showNewGiftStyle) {
        updateLayoutVisibility(isNewInvite, showOldContent, showOldReceive, showNewGiftStyle, false);
    }

    private void updateLayoutVisibility(boolean isNewInvite, boolean showOldContent, boolean showOldReceive, boolean showNewGiftStyle, boolean showBigEmoji) {
        int newInviteVis;
        if (isNewInvite) {
            newInviteVis = View.VISIBLE;
        } else {
            newInviteVis = View.GONE;
        }
        inviteLayout.setVisibility(newInviteVis);
        inviteLayout.setLayoutDirection(ScreenUtil.isRtl() ? LAYOUT_DIRECTION_RTL : LAYOUT_DIRECTION_LTR);

        int oldMsgReceiveVis;
        if (showOldReceive) {
            oldMsgReceiveVis = View.VISIBLE;
        } else {
            oldMsgReceiveVis = View.GONE;
        }
        oldTextLayout.setVisibility(oldMsgReceiveVis);

        int oldContentVis;
        if (showOldContent) {
            oldContentVis = View.VISIBLE;
        } else {
            oldContentVis = View.GONE;
        }
        textTv.setVisibility(oldContentVis);

        if (showBigEmoji) {
            commonViewHolder.showBubbleItem(false);
            receiveLayout.setVisibility(GONE);
            // ViewHolder处理emoji显示
        } else {
            commonViewHolder.showBubbleItem(true);
            receiveLayout.setVisibility(VISIBLE);
            commonViewHolder.hideEmojiLayout();
        }
        commonViewHolder.hideQuoteItem();
    }
}
