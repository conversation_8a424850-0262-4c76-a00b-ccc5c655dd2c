package com.wepie.wespy.module.chat.gamemodel;

import static android.widget.LinearLayout.LAYOUT_DIRECTION_LTR;
import static android.widget.LinearLayout.LAYOUT_DIRECTION_RTL;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.emoji.view.resource.MultipleEmojiHelper;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.ui.item.GiftCardUtil;

import java.util.List;

/**
 * 礼品文本消息的ViewHolder，用于MsgReceiveTextModel和MsgSendTextModel的共同功能
 *
 * <AUTHOR>
 */
public class GiftTextViewHolder {
    private final Context context;

    // 礼品相关UI元素
    private TextView giftTitleTv;
    private TextView giftDescTv;
    private View giftDivider;
    private TextView giftExtTv;
    private View giftExtraDivider;
    private TextView giftExtraReturnTv;
    private ViewGroup giftGainLay;
    private ImageView giftIcon;

    // 其他通用UI元素
    private ChatMsgBubbleItem bubbleItem;
    private LinearLayout emojiLayout;
    private MsgQuoteModel quoteItem;
    private ViewGroup mainLayout; // 主要的消息布局

    public GiftTextViewHolder(View rootView) {
        this.context = rootView.getContext();
    }

    /**
     * 初始化ViewHolder，绑定UI元素
     */
    public void initViews(View rootView, int giftTitleId, int giftDescId, int giftDividerId,
                          int giftExtId, int giftExtraDividerId, int giftExtraReturnId,
                          int giftGainLayId, int giftIconId, int bubbleItemId,
                          int emojiLayoutId, int quoteItemId, int mainLayoutId) {
        giftTitleTv = rootView.findViewById(giftTitleId);
        giftDescTv = rootView.findViewById(giftDescId);
        giftDivider = rootView.findViewById(giftDividerId);
        giftExtTv = rootView.findViewById(giftExtId);
        giftExtraDivider = rootView.findViewById(giftExtraDividerId);
        giftExtraReturnTv = rootView.findViewById(giftExtraReturnId);
        giftGainLay = rootView.findViewById(giftGainLayId);
        giftIcon = rootView.findViewById(giftIconId);
        bubbleItem = rootView.findViewById(bubbleItemId);
        emojiLayout = rootView.findViewById(emojiLayoutId);
        quoteItem = rootView.findViewById(quoteItemId);
        mainLayout = rootView.findViewById(mainLayoutId);
    }

    /**
     * 显示礼品图标
     */
    public void showGiftIcon(int giftId) {
        if (giftIcon == null) return;
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
        if (gift == null) return;
        String url = gift.getMedia_url();
        if (TextUtils.isEmpty(url)) return;
        giftIcon.setVisibility(View.VISIBLE);
        WpImageLoader.load(url, giftIcon, ImageLoadInfo.newGiftInfo());
    }

    /**
     * 隐藏礼品图标
     */
    public void hideGiftIcon() {
        if (giftIcon != null) {
            giftIcon.setVisibility(View.GONE);
        }
    }

    /**
     * 设置礼品布局方向
     */
    public void setGiftLayoutDirection() {
        if (mainLayout != null) {
            if (ScreenUtil.isRtl()) {
                mainLayout.setLayoutDirection(LAYOUT_DIRECTION_RTL);
            } else {
                mainLayout.setLayoutDirection(LAYOUT_DIRECTION_LTR);
            }
        }
    }

    /**
     * 更新礼品扩展信息的显示
     */
    public void updateGiftExtension(WPMessage msg, boolean isSend) {
        if (giftGainLay == null || bubbleItem == null) return;

        String giftRecCoin = msg.getGiftRecCoinFromExtension();
        boolean showGiftExtension = !TextUtils.isEmpty(giftRecCoin);

        if (showGiftExtension) {
            giftGainLay.setVisibility(View.VISIBLE);

            // 设置分隔线颜色
            if (giftDivider != null) {
                giftDivider.setVisibility(View.VISIBLE);
                giftDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(bubbleItem, msg.getBubbleId(), isSend));
            }

            // 设置礼品收益文本
            if (giftExtTv != null) {
                giftExtTv.setVisibility(View.VISIBLE);
                giftExtTv.setTextColor(isSend ? bubbleItem.getGiftDescColor() : bubbleItem.getGiftTipColor());
                giftExtTv.setText(MsgTextHelper.getGiftCoinText(giftRecCoin, bubbleItem));
            }

            // 处理额外返还金币
            int extraReturnCoin = msg.getGiftExtraReturnCoinFromExtension();
            if (extraReturnCoin > 0) {
                if (giftExtraDivider != null) {
                    giftExtraDivider.setVisibility(View.VISIBLE);
                    giftExtraDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(bubbleItem, msg.getBubbleId(), isSend));
                }

                if (giftExtraReturnTv != null) {
                    giftExtraReturnTv.setVisibility(View.VISIBLE);
                    giftExtraReturnTv.setTextColor(bubbleItem.getGiftTipColor());
                    MsgTextHelper.updateGiftExtraReturnCoin(giftExtraReturnTv, extraReturnCoin, bubbleItem.getGiftDescColor());
                }
            } else {
                if (giftExtraDivider != null) {
                    giftExtraDivider.setVisibility(View.GONE);
                }
                if (giftExtraReturnTv != null) {
                    giftExtraReturnTv.setVisibility(View.GONE);
                }
            }
        } else {
            giftGainLay.setVisibility(View.GONE);
        }
    }

    /**
     * 更新新礼品样式的标题和描述
     */
    public void updateNewGiftStyle(MsgTextHelper.NewChatGift chatGift) {
        if (giftTitleTv != null && giftDescTv != null && chatGift != null && chatGift.useNewGiftStyle) {
            giftTitleTv.setVisibility(View.VISIBLE);
            giftDescTv.setVisibility(View.VISIBLE);

            giftTitleTv.setTextColor(bubbleItem.getTextColor());
            giftDescTv.setTextColor(bubbleItem.getGiftTipColor());

            giftTitleTv.setText(chatGift.title);
            giftDescTv.setText(chatGift.desc);
        }
    }

    /**
     * 显示大表情
     */
    public void showBigEmojis(List<EmojiItem> emojiItems, View.OnLongClickListener listener) {
        if (emojiLayout != null && !emojiItems.isEmpty()) {
            emojiLayout.setVisibility(View.VISIBLE);
            MultipleEmojiHelper.showEmojis(emojiLayout, emojiItems, listener);
        }
    }

    /**
     * 隐藏大表情布局
     */
    public void hideEmojiLayout() {
        if (emojiLayout != null) {
            emojiLayout.setVisibility(View.GONE);
        }
    }

    /**
     * 显示/隐藏引用消息
     */
    public void showQuoteItem() {
        if (quoteItem != null) {
            quoteItem.setVisibility(View.VISIBLE);
        }
    }

    public void hideQuoteItem() {
        if (quoteItem != null) {
            quoteItem.setVisibility(View.GONE);
        }
    }

    /**
     * 获取引用消息组件
     */
    public MsgQuoteModel getQuoteItem() {
        return quoteItem;
    }

    /**
     * 获取ChatMsgBubbleItem
     */
    public ChatMsgBubbleItem getBubbleItem() {
        return bubbleItem;
    }

    /**
     * 重置新礼品样式的显示状态
     */
    public void resetNewGiftStyle() {
        if (giftTitleTv != null) {
            giftTitleTv.setVisibility(View.GONE);
        }
        if (giftDescTv != null) {
            giftDescTv.setVisibility(View.GONE);
        }
    }
}
