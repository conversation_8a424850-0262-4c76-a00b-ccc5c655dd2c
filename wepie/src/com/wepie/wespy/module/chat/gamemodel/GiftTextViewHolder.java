package com.wepie.wespy.module.chat.gamemodel;

import static android.widget.LinearLayout.LAYOUT_DIRECTION_LTR;
import static android.widget.LinearLayout.LAYOUT_DIRECTION_RTL;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.util.ScreenUtil;

import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;

import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.ui.item.GiftCardUtil;



/**
 * 礼品文本消息的ViewHolder，用于MsgReceiveTextModel和MsgSendTextModel的共同功能
 *
 * <AUTHOR>
 */
public class GiftTextViewHolder {
    private final Context context;

    // 礼品相关UI元素
    private final TextView giftTitleTv;
    private final TextView giftDescTv;
    private final View giftDivider;
    private final TextView giftExtTv;
    private final View giftExtraDivider;
    private final TextView giftExtraReturnTv;
    private final ViewGroup giftGainLay;
    private final ImageView giftIcon;

    private final ViewGroup mainLayout; // 主要的消息布局

    // 通用消息UI元素管理器
    private final CommonMessageViewHolder commonViewHolder;

    public GiftTextViewHolder(ViewGroup rootView) {
        this.context = rootView.getContext();
        this.mainLayout = rootView;

        // 初始化礼品相关UI元素
        giftTitleTv = rootView.findViewById(R.id.gift_title_tv);
        giftDescTv = rootView.findViewById(R.id.gift_desc_tv);
        giftDivider = rootView.findViewById(R.id.gift_div_view);
        giftExtTv = rootView.findViewById(R.id.gift_ext_tv);
        giftExtraDivider = rootView.findViewById(R.id.gift_extra_div_view);
        giftExtraReturnTv = rootView.findViewById(R.id.gift_extra_return_tv);
        giftGainLay = rootView.findViewById(R.id.msg_gift_gain_lay);
        giftIcon = rootView.findViewById(R.id.gift_icon);

        // 初始化通用消息UI元素管理器
        commonViewHolder = new CommonMessageViewHolder(rootView);
    }

    /**
     * 显示礼品图标
     */
    public void showGiftIcon(int giftId) {
        if (giftIcon == null) return;
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
        if (gift == null) return;
        String url = gift.getMedia_url();
        if (TextUtils.isEmpty(url)) return;
        giftIcon.setVisibility(View.VISIBLE);
        WpImageLoader.load(url, giftIcon, ImageLoadInfo.newGiftInfo());
    }

    /**
     * 隐藏礼品图标
     */
    public void hideGiftIcon() {
        if (giftIcon != null) {
            giftIcon.setVisibility(View.GONE);
        }
    }

    /**
     * 设置礼品布局方向
     */
    public void setGiftLayoutDirection() {
        if (mainLayout != null) {
            if (ScreenUtil.isRtl()) {
                mainLayout.setLayoutDirection(LAYOUT_DIRECTION_RTL);
            } else {
                mainLayout.setLayoutDirection(LAYOUT_DIRECTION_LTR);
            }
        }
    }

    /**
     * 更新礼品扩展信息的显示
     */
    public void updateGiftExtension(WPMessage msg, boolean isSend) {
        if (giftGainLay == null || commonViewHolder.getBubbleItem() == null) return;

        String giftRecCoin = msg.getGiftRecCoinFromExtension();
        boolean showGiftExtension = !TextUtils.isEmpty(giftRecCoin);

        if (showGiftExtension) {
            giftGainLay.setVisibility(View.VISIBLE);

            // 设置分隔线颜色
            if (giftDivider != null) {
                giftDivider.setVisibility(View.VISIBLE);
                giftDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(commonViewHolder.getBubbleItem(), msg.getBubbleId(), isSend));
            }

            // 设置礼品收益文本
            if (giftExtTv != null) {
                giftExtTv.setVisibility(View.VISIBLE);
                giftExtTv.setTextColor(isSend ? commonViewHolder.getBubbleItem().getGiftDescColor() : commonViewHolder.getBubbleItem().getGiftTipColor());
                giftExtTv.setText(MsgTextHelper.getGiftCoinText(giftRecCoin, commonViewHolder.getBubbleItem()));
            }

            // 处理额外返还金币
            int extraReturnCoin = msg.getGiftExtraReturnCoinFromExtension();
            if (extraReturnCoin > 0) {
                if (giftExtraDivider != null) {
                    giftExtraDivider.setVisibility(View.VISIBLE);
                    giftExtraDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(commonViewHolder.getBubbleItem(), msg.getBubbleId(), isSend));
                }

                if (giftExtraReturnTv != null) {
                    giftExtraReturnTv.setVisibility(View.VISIBLE);
                    giftExtraReturnTv.setTextColor(commonViewHolder.getBubbleItem().getGiftTipColor());
                    MsgTextHelper.updateGiftExtraReturnCoin(giftExtraReturnTv, extraReturnCoin, commonViewHolder.getBubbleItem().getGiftDescColor());
                }
            } else {
                if (giftExtraDivider != null) {
                    giftExtraDivider.setVisibility(View.GONE);
                }
                if (giftExtraReturnTv != null) {
                    giftExtraReturnTv.setVisibility(View.GONE);
                }
            }
        } else {
            giftGainLay.setVisibility(View.GONE);
        }
    }

    /**
     * 更新新礼品样式的标题和描述
     */
    public void updateNewGiftStyle(MsgTextHelper.NewChatGift chatGift) {
        if (giftTitleTv != null && giftDescTv != null && chatGift != null && chatGift.useNewGiftStyle) {
            giftTitleTv.setVisibility(View.VISIBLE);
            giftDescTv.setVisibility(View.VISIBLE);

            giftTitleTv.setTextColor(commonViewHolder.getBubbleItem().getTextColor());
            giftDescTv.setTextColor(commonViewHolder.getBubbleItem().getGiftTipColor());

            giftTitleTv.setText(chatGift.title);
            giftDescTv.setText(chatGift.desc);
        }
    }

    /**
     * 获取通用消息ViewHolder
     */
    public CommonMessageViewHolder getCommonViewHolder() {
        return commonViewHolder;
    }

    /**
     * 重置新礼品样式的显示状态
     */
    public void resetNewGiftStyle() {
        if (giftTitleTv != null) {
            giftTitleTv.setVisibility(View.GONE);
        }
        if (giftDescTv != null) {
            giftDescTv.setVisibility(View.GONE);
        }
    }
}
