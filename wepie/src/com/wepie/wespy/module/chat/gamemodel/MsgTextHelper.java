package com.wepie.wespy.module.chat.gamemodel;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.ReplacementSpan;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.FontUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.constants.GameType;
import com.huiwan.littlegame.cocos.CocosLaunchInfo;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.FLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.util.CommonUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.JumpCocosTeamInfo;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.game.room.roomlist.InputTextDialog;
import com.wepie.wespy.module.shop.dialogs.PropInfoConfig;
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

public class MsgTextHelper {
    private static final String TAG = "MsgTextHelper";
    public static final String MARK = "\u200b\u200b";
    public static final String SPACE_MARK = "\u2005";
    public static final String LINE_MARK = "\n";

    public static void enterRoom(Context context, WPMessage msg) {
        if (CommonUtil.isInGame()) {
            ToastUtil.show(R.string.common_in_gaming);
            return;
        }
        int rid = msg.getExtensionSubTypeRid();
        if (rid <= 0) {
            ToastUtil.show(R.string.common_room_error);
        } else {
            EventDispatcher.postChatGameInviteCancelEvent();
            EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(context, rid, msg.getExtensionCocosGameType(), msg.getPwdFromExtension()).setInviteUid(msg.getSend_uid()).setInvite(true).setSource(TrackSource.INVITE);
            JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
        }
    }

    public static void applyStay(Context context, WPMessage msg) {
        if (CommonUtil.isInGame()) {
            ToastUtil.show(R.string.common_in_gaming);
            return;
        }

        final int rid = msg.getExtensionSubTypeRid();
        if (rid <= 0) {
            ToastUtil.show(R.string.common_room_error);
        } else {
            InputTextDialog.showSearchGameRoomDialog(context, InputTextDialog.TYPE_APPLY, new InputTextDialog.Callback() {
                @Override
                public void onEnter(String reason) {
//                    RoomPacketSender.game_rq_room_applyStay(rid, LoginHelper.getLoginUid(), reason, msg.getSend_uid());
                }

                @Override
                public void onCancel() {

                }
            });
        }
    }

    public static void enterCocos(Context context, WPMessage msg, int tid, int gameType) {
        if (CommonUtil.isInGame()) {
            ToastUtil.show(R.string.common_in_gaming);
            return;
        }
        long interval = System.currentTimeMillis() - msg.getTime();
        if (tid <= 0) {
            ToastUtil.show(R.string.common_invite_error);
        } else if (interval > 1000 * 60 * 10) {
            ToastUtil.show(R.string.common_invite_expired);
        } else {
            EventDispatcher.postChatGameInviteCancelEvent();
            JumpCocosTeamUtil.jumpToCocosGame(
                    JumpCocosTeamInfo.build(context).setTid(tid)
                            .setGameType(gameType).setFromChat(true)
                            .setEnterVipReferScreenName(TrackButtonName.INVITE)
                            .setInvite(true),
                    CocosLaunchInfo.ENTER_MODE_INVITE_TO_COCOS);
        }
    }

    public static SpannableString getGiftCoinText(String coin, int color) {
        SpannableString ss = new SpannableString(ResUtil.getStr(R.string.chat_gift_msg_receiver_got, coin));
        int start = ss.toString().indexOf(coin);
        if (start < 0) {
            start = 0;
        }

        ss.setSpan(new ForegroundColorSpan(color), start, ss.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ss;
    }

    public static String getVoiceInviteSub(int labelType, int rid) {
        String res = "";
        VoiceLabelInfo labelInfo = ConfigHelper.getInstance().getLabelById(labelType);
        if (null != labelInfo) {
            res = labelInfo.getName() + " · " + IDRegionUtil.INSTANCE.getFinalIDStrByGameType(rid, GameType.GAME_TYPE_VOICE_ROOM);
        }
        return res;
    }

    public static SpannableString getGiftCoinText(String coin, ChatMsgBubbleItem textBubbleItem) {
        return getGiftCoinText(coin, textBubbleItem.getBubbleItem());
    }

    public static SpannableString getGiftCoinText(String coin, ChatBubbleItem textBubbleItem) {
        SpannableString ss = new SpannableString(ResUtil.getStr(R.string.chat_gift_msg_receiver_got, coin));
        int end = ss.toString().indexOf(coin);
        try {
            if (end < 0) {
                ss.setSpan(new ForegroundColorSpan(textBubbleItem.getGiftTipColor()), 0, ss.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else {
                ss.setSpan(new ForegroundColorSpan(textBubbleItem.getGiftTipColor()), 0, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                ss.setSpan(new ForegroundColorSpan(textBubbleItem.getGiftDescColor()), end, ss.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        } catch (Exception e) {
            FLog.e(e);
        }
        return ss;
    }

    public static void updateGiftExtraReturnCoin(TextView textView, int coin, int coinTextColor) {
        textView.setText(getGiftExtraReturnCoinText(coin, coinTextColor, textView.getLineSpacingExtra()));
        textView.setOnClickListener(v -> {
            PropInfoConfig config = PropInfoConfig.fromOutOfShop(1703100);
            config.setPreview(true);
            config.setBpPreview(true);
            PropInfoDialog.show(v.getContext(), config, null);
        });
    }

    public static CharSequence getGiftExtraReturnCoinText(int coin, int coinTextColor) {
        return getGiftExtraReturnCoinText(coin, coinTextColor, 0);
    }

    public static CharSequence getGiftExtraReturnCoinText(int coin, int coinTextColor, float lineSpace) {
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        try {
            if (ScreenUtil.isRtl()) {
                ssb.append("\u200F");
            }
            int index = ssb.length();
            String tagStr = ResUtil.getStr(R.string.gift_return_black_tag);
            ssb.append(tagStr);
            ssb.setSpan(new ReplacementSpan() {

                private final TextPaint mPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
                private final Rect rect = new Rect();
                private final int dp6 = ScreenUtil.dip2px(6F);
                private final int dp14 = ScreenUtil.dip2px(14F);
                private final int dp60 = ScreenUtil.dip2px(60F);
                private final int textColor = ColorUtil.getColor("#333333");
                private final int bgColor = ColorUtil.getColor("#F3D892");

                private LinearGradient gradient;

                {
                    mPaint.setTypeface(FontUtil.getTypeface(Typeface.BOLD));
                    mPaint.setTextSize(ScreenUtil.dip2px(10));
                    mPaint.setStyle(Paint.Style.FILL);
                }

                @Override
                public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
                    mPaint.getTextBounds(text.toString(), start, end, rect);
                    if (gradient == null) {
                        // 创建线性渐变着色器
                        gradient = new LinearGradient(
                                0F,
                                0F,
                                rect.right,
                                0F,
                                Color.parseColor("#333333"),
                                Color.parseColor("#555555"),
                                Shader.TileMode.CLAMP
                        );
                    }
                    return rect.right + dp6 * 2 + ScreenUtil.dip2px(4);
                }

                @Override
                public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
                    canvas.save();
                    mPaint.setShader(null);
                    mPaint.setColor(bgColor);
                    int height = (int) (bottom - top - lineSpace);
                    int d = (height - dp14) / 2;
                    int rTop = top + d;
                    if (ScreenUtil.isRtl()) {
                        canvas.drawRoundRect(canvas.getWidth() - (rect.right + dp6 * 2), rTop, canvas.getWidth(), rTop + dp14, dp60, dp60, mPaint);
                    } else {
                        canvas.drawRoundRect(x, rTop, rect.right + dp6 * 2, rTop + dp14, dp60, dp60, mPaint);
                    }
                    mPaint.setColor(textColor);
                    if (gradient != null) {
                        mPaint.setShader(gradient);
                    }
                    int yPos = (int) ((height - mPaint.descent() - mPaint.ascent()) / 2);
                    if (ScreenUtil.isRtl()) {
                        canvas.drawText(text.subSequence(start, end).toString(), canvas.getWidth() - rect.right - dp6, yPos, mPaint);
                    } else {
                        canvas.drawText(text.subSequence(start, end).toString(), x + dp6, yPos, mPaint);
                    }
                    canvas.restore();
                }
            }, index, index + tagStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            if (ScreenUtil.isRtl()) {
                ssb.append("\u200F");
            }
            ssb.append(ResUtil.getStr(R.string.gift_return_black_trigger_title))
                    .append(ResUtil.getStr(R.string.char_comma));
            int start = ssb.length();
            ssb.append(ResUtil.getStr(R.string.gift_return_black_extra_coin_title, coin));
            ssb.setSpan(new ForegroundColorSpan(coinTextColor), start, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        } catch (Exception e) {
            FLog.e(e);
        }
        return ssb;
    }

    public static class NewChatGift {
        public String title = "";
        public String desc = "";
        public boolean useNewGiftStyle = false;

        public static NewChatGift build(String msg) {
            NewChatGift chatGift = new NewChatGift();
            try {
                String comma = "，";
                String enComma = ",";
                String dot = ".";
                String check = MARK;
                int checkIndex = -1;
                do {
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = comma + SPACE_MARK;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = enComma + SPACE_MARK;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = comma;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = enComma;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = LINE_MARK;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = dot + SPACE_MARK;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                    check = dot;
                    if (msg.contains(check)) {
                        checkIndex = msg.indexOf(check);
                        break;
                    }
                } while (false);
                if (checkIndex >= 0) {
                    chatGift.title = msg.substring(0, checkIndex);
                    chatGift.desc = msg.substring(checkIndex + check.length());
                    removeTextSpace(chatGift);
                }
                chatGift.useNewGiftStyle = !TextUtils.isEmpty(chatGift.title) && !TextUtils.isEmpty(chatGift.desc);
            } catch (Exception e) {
                FLog.e(e);
            }
            return chatGift;
        }

        // 移除空格和逗号
        private static void removeTextSpace(NewChatGift chatGift) {
            try {
                chatGift.title = chatGift.title.trim();
                chatGift.desc = chatGift.desc.trim();
                if (chatGift.title.endsWith(",") || chatGift.title.endsWith("，")) {
                    chatGift.title = chatGift.title.substring(0, chatGift.title.length() - 1);
                }
                if (chatGift.desc.startsWith(",") || chatGift.desc.startsWith("，")) {
                    chatGift.desc = chatGift.desc.substring(1);
                }
            } catch (Exception e) {
                FLog.e(e);
            }
        }
    }

}
