package com.wepie.wespy.module.chat.ui.single;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.activity.ProcessRestartObserver;
import com.huiwan.base.common.MediaChooseLimit;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.NetWorkUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.ComboInfo;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.show.GiftContentView;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.HttpCode;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.friend.AddFriendCallback;
import com.huiwan.lib.api.plugins.friend.AddFriendInfo;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.libtcp.config.ChatPacketConstants;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.database.WPModel;
import com.huiwan.store.database.WPStore;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.NewFriendRecord;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.rv.NpaLinearLayoutManager;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.uploader.BucketType;
import com.wepie.lib.api.uploader.IUploadCallback;
import com.wepie.lib.api.uploader.SimpleFileUploader;
import com.wepie.lib.api.uploader.UploadConfig;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.base.ICareApiFactory;
import com.wepie.wespy.helper.WPHelper;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.helper.notice.NoticeHelper;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.ConversationInfo;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.emoticon.EmoticonItem;
import com.wepie.wespy.model.entity.match.GameInviteInfo;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingQuickEnter;
import com.wepie.wespy.model.event.ChatMsgShareEvent;
import com.wepie.wespy.model.event.ProtoMessageEvent;
import com.wepie.wespy.model.event.chat.NotifyChatDataChangeEvent;
import com.wepie.wespy.model.event.chat.PushNewFriend;
import com.wepie.wespy.model.event.chat.RefreshChatListEventWithoutScroll;
import com.wepie.wespy.model.event.chat.ShowRecallLoadingDialogEvent;
import com.wepie.wespy.model.event.chat.SingleChatDeleteMsgEvent;
import com.wepie.wespy.model.event.chat.SingleChatInputStatusEvent;
import com.wepie.wespy.model.event.chat.SingleChatRefreshList;
import com.wepie.wespy.model.event.chat.SingleChatResendMsgEvent;
import com.wepie.wespy.model.event.chat.SingleRecallEvent;
import com.wepie.wespy.model.event.chat.UnreadMsgNumChangeEvent;
import com.wepie.wespy.module.care.main.CareUtil;
import com.wepie.wespy.module.chat.ChatUtil;
import com.wepie.wespy.module.chat.conversation.ConversationManager;
import com.wepie.wespy.module.chat.conversation.CustomListPopupWindow;
import com.wepie.wespy.module.chat.invitegame.ChooseVoiceLinkDialog;
import com.wepie.wespy.module.chat.invitegame.GameInviteHelper;
import com.wepie.wespy.module.chat.invitegame.GameInviteWindow;
import com.wepie.wespy.module.chat.invitegame.all.ChatChooseGameView;
import com.wepie.wespy.module.chat.manager.AudioPlayNextManager;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.chat.manager.RedPacketManager;
import com.wepie.wespy.module.chat.model.ChatAudio;
import com.wepie.wespy.module.chat.model.ChatImage;
import com.wepie.wespy.module.chat.model.ChatVideo;
import com.wepie.wespy.module.chat.model.MediaModel;
import com.wepie.wespy.module.chat.presenter.MsgCacheUtil;
import com.wepie.wespy.module.chat.send.SendViewModel;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.common.jump.UserGameStatusUtil;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.game.game.activity.PullListView;
import com.wepie.wespy.module.gift.ComboCallback;
import com.wepie.wespy.module.gift.GiftComboUtil;
import com.wepie.wespy.module.gift.GiftComboView;
import com.wepie.wespy.module.gift.GiftInfoTransformHelper;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wepie.wespy.module.gift.SendGiftComboCallback;
import com.wepie.wespy.module.msgroaming.MsgRoamingUtil;
import com.wepie.wespy.module.redpacket.RedPacketPopUtil;
import com.wepie.wespy.module.shop.MyPropManager;
import com.wepie.wespy.net.http.api.MsgRoamingApi;
import com.wepie.wespy.net.tcp.packet.ChatPackets;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_rs_msg_send;
import com.wepie.wespy.net.tcp.sender.ChatPacketSender;
import com.zhihu.matisse.internal.entity.Item;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

/**
 * 个人聊天界面
 *
 * <AUTHOR>
 */
public class ChatActivity extends BaseActivity implements GameInviteWindow, CustomListPopupWindow.PopupWindowInterface {
    private static final String TAG = "ChatActivity";

    private static final String CHAT_UID = "chat_activity_chat_uid";

    private Context mContext;
    private TextView mTitle;
    private View mTitleLeft;
    private View mTitleRight;
    private SendViewModel sendView;
    private ChatAdapterNew adapter;
    private ImageView chatBgIv;
    private View chatBgIvMask;
    private GiftComboView comboView;
    private TextView unreadNumTv;
    private LinearLayout newMsgLay;
    private TextView newMsgNumTv;
    private LinearLayout hasNewUserLay;
    private LinearLayout canDownViewLay;

    private UserSimpleInfo mChatUser;
    private MsgSendListener msgSendListener;
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private int chatUid;
    private RelativeLayout addFriendLay;
    private ImageView addUserHead;
    private GiftContentView giftContentView;

    private SmartRefreshLayout refreshLayout;
    private PullListView mChatListView;
    private LinearLayoutManager llm;
    private boolean isFinish = false;
    private boolean hasNewMsg = false;

    private final ProgressDialogUtil recallLoadingDialog = new ProgressDialogUtil();
    private String sendGiftSource;
    private boolean isVisible = false;
    private final GameInviteHelper gameInviteHelper = new GameInviteHelper();
    private CustomListPopupWindow customListPopupWindow;

    private String currentBgPath = "";
    private RecyclerView quickEnterRv;
    private ChatQuickEnterAdapter quickEnterAdapter;
    private long inputTimeDDL = 0;
    boolean isInputtingVoice = false;
    long lastSendInputTyping = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = this;
        getWindow().setBackgroundDrawableResource(R.color.color_setting_bg);
        StatusBarUtil.initStatusBar(this);
        /* 调用时长有时会超过 500ms， 排除是 SmartRefreshLayout 造成的可能性 */
        setContentView(R.layout.personal_chat_view);
        initViews();
        customListPopupWindow = findViewById(R.id.list_popupwindow);

        init(getIntent());
        checkSendGift(getIntent());
        gameInviteHelper.attachActivity(this);
        gameInviteHelper.listen();
        HLog.d(TAG, HLog.USR, "onCreate, chatUid=" + chatUid);
        ProcessRestartObserver.INSTANCE.register(this);
    }

    private void init(Intent intent) {
        clearEventBus();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        UserSimpleInfo lastChatUser = mChatUser;
        chatUid = intent.getIntExtra(IntentConfig.KEY_REC_USER_UID, 0);
        mChatUser = UserService.get().getCacheSimpleLocalUser(chatUid);

        sendView.setNeedDice(true);
        initDraft();
        if (lastChatUser != null && mChatUser != null && lastChatUser.getUid() == mChatUser.getUid())
            return;

        if (mChatUser != null) {
            refreshUserInfo();
            return;
        }

        UserService.get().getCacheSimpleUser(chatUid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                mChatUser = userInfo;
                refreshUserInfo();
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
                finish();
            }
        });
    }

    private void checkShowAddFriend() {
        NewFriendRecord record = UserService.get().getNewFriend().getNewFriendRecordByUid(chatUid);
        if (record == null) {
            if (addFriendLay.getVisibility() == View.VISIBLE) addFriendLay.setVisibility(View.GONE);
            return;
        }
        boolean show = record.mType == NewFriendRecord.SERVER_TYPE_RECOMMEND;
        if (show) {
            if (addFriendLay.getVisibility() == View.GONE) addFriendLay.setVisibility(View.VISIBLE);
        } else {
            if (addFriendLay.getVisibility() == View.VISIBLE) addFriendLay.setVisibility(View.GONE);
        }
    }

    private void refreshUserInfo() {
        updateData();
        initEvents();
        onPullDown();
        ConversationManager.getInstance().resetUnReadMsgByTargetUid(chatUid);
    }

    private void initViews() {
        mTitle = findViewById(R.id.wp_title_content);

        mTitleLeft = findViewById(R.id.wp_title_left);
        mTitleRight = findViewById(R.id.wp_title_right);
        TextView right = findViewById(R.id.wp_title_right_tx);
        if (UserService.get().isSystemUser(chatUid)) {
            right.setBackgroundResource(R.drawable.public_title_info_white);
        } else {
            right.setBackgroundResource(R.drawable.ic_chat_action_bar_more);
        }
        TouchEffectUtil.addTouchEffect(right);

        refreshLayout = findViewById(R.id.chat_refresher);
        refreshLayout.setReboundDuration(150);

        newMsgLay = findViewById(R.id.new_msg_indication_lay);
        newMsgNumTv = findViewById(R.id.new_msg_num_tv);
        hasNewUserLay = findViewById(R.id.has_new_user_lay);
        canDownViewLay = findViewById(R.id.can_down_view_lay);
        mChatListView = findViewById(R.id.personal_chat_list_srl);
        adapter = new ChatAdapterNew(mContext, mChatListView, new ChatAdapterNew.Callback() {
            @Override
            public void doQuote(WPMessage msg) {
                doQuoteMsg(msg);
                if (msg != null) {
                    ConversationManager.getInstance().updateSingleChatRefMid(chatUid, msg.getMid());
                }
            }

            @Override
            public void doSendGift(WPMessage msg) {
                showSendGift(TrackSource.SINGLE_CHAT);
            }
        });
        llm = new NpaLinearLayoutManager(this);
        mChatListView.setLayoutManager(llm);
        mChatListView.setAdapter(adapter);
        mChatListView.setItemAnimator(null);

        mChatListView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (!recyclerView.canScrollVertically(1)) {
                    hasNewMsg = false;
                }
                int item = llm.findLastVisibleItemPosition();
                updateCanDownView(adapter.getItemCount() - item > 30);
            }
        });
        View.OnClickListener scrollToBottom = this::scrollToBottom;
        canDownViewLay.setOnClickListener(scrollToBottom);
        hasNewUserLay.setOnClickListener(scrollToBottom);

        sendView = findViewById(R.id.personal_chat_send_lay);
        quickEnterAdapter = new ChatQuickEnterAdapter();
        quickEnterRv = findViewById(R.id.quick_enter_rv);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        quickEnterRv.setLayoutManager(layoutManager);
        quickEnterRv.setAdapter(quickEnterAdapter);
        quickEnterAdapter.setOnItemClickListener(info ->
                JumpCommon.gotoOtherPager(mContext, info.getLinkUrl(), TrackSource.QUICK_INLET, null));
        comboView = findViewById(R.id.combo_view);

        addFriendLay = findViewById(R.id.add_friend_lay);
        addUserHead = findViewById(R.id.user_head_iv);
        TextView addBtn = findViewById(R.id.add_friend_button);
        chatBgIv = findViewById(R.id.personal_chat_bg_iv);
        chatBgIvMask = findViewById(R.id.chat_bg_iv_mask);
        giftContentView = findViewById(R.id.gift_content_view);
        unreadNumTv = findViewById(R.id.chat_unread_tv);
        addBtn.setOnClickListener(v -> {
            if (mChatUser == null) return;
            sendAddFriend(mChatUser);
        });

        comboView.setLightTheme();
        comboView.setComboCallback(new SendGiftComboCallback() {
            @Override
            public void comboGift(GiftSendInfo sendInfo, ComboCallback callback) {
                msgSendListener.onSendGiftMsg(sendInfo);
            }

            @Override
            public void hideSolderCombo() {

            }
        });

        setChatBg();
        mChatListView.setAutoScrollToBottom(true);
    }

    private void updataQuickEnterInfo() {
        if (UserService.get().isQuickEnterUser(chatUid)) {
            MsgRoamingApi.getChatQuickEntrance(MsgRoamingQuickEnter.CHAT_QUICK_ENTER_TYPE_NPC, chatUid,
                    LoginHelper.getLoginUid(), new LifeDataCallback<MsgRoamingQuickEnter>(this) {
                        @Override
                        public void onSuccess(Result<MsgRoamingQuickEnter> result) {
                            List<ChatQuickEnterInfo> quickEnterList = new ArrayList<>();
                            for (MsgRoamingQuickEnter.EntranceInfo quickEnter : result.data.entranceList) {
                                ChatQuickEnterInfo quickEnterInfo = new ChatQuickEnterInfo(quickEnter.getLinkIcon(), quickEnter.getLinkName()
                                        , quickEnter.getLinkUrl(), quickEnter.getIsApplySinger(), quickEnter.getStartTime(), quickEnter.getEndTime());
                                quickEnterList.add(quickEnterInfo);
                            }
                            if (!quickEnterList.isEmpty()) {
                                quickEnterRv.setVisibility(View.VISIBLE);
                                quickEnterAdapter.refresh(quickEnterList);
                            } else {
                                quickEnterRv.setVisibility(View.GONE);
                            }
                        }

                        @Override
                        public void onFail(int code, String msg) {
                            quickEnterRv.setVisibility(View.GONE);
                        }
                    });
        } else {
            quickEnterRv.setVisibility(View.GONE);
        }
    }

    private void scrollToBottom(View view) {
        mChatListView.setAutoScrollToBottom(true);
        int item = llm.findLastVisibleItemPosition();
        if (adapter.getItemCount() - item > 50) {
            mChatListView.scrollToPosition(adapter.getItemCount() - 1);
            updateCanDownView(false);
        } else {
            mChatListView.smoothScrollToPosition(adapter.getItemCount() - 1);
        }
        hasNewMsg = false;
    }

    private void checkNewMsgNum() {
        int newMsgNum = getIntent().getIntExtra(IntentConfig.UNREAD_MSG, 0);
        if (newMsgNum >= 10) {
            newMsgLay.setVisibility(View.VISIBLE);
            String newMsgNumStr = String.valueOf(newMsgNum);
            if (newMsgNum > 99) {
                newMsgNumStr = getString(R.string.c_x_number, "99+");
            }
            newMsgNumTv.setText(ResUtil.getStr(R.string.chat_group_x_new_message, newMsgNumStr));
            long lastMsgTime = getIntent().getLongExtra(IntentConfig.UNREAD_LAST_MSG_TIME, Long.MAX_VALUE);
            ILife life = ILifeUtil.toLife(this);
            ChatManager.getInstance().getUnreadTime(newMsgNum, lastMsgTime,life, unreadTime ->
                    newMsgLay.post(() ->
                            newMsgLay.setOnClickListener(v -> {
                                if (adapter.getItemCount() > newMsgNum) {
                                    mChatListView.setSelection(adapter.getItemCount() - 1 - newMsgNum);
                                } else {
                                    ChatManager.getInstance().getUnreadChatList(unreadTime, life, list -> {
                                        updateMsgAdapter();
                                        mChatListView.post(() -> mChatListView.setSelection(0));
                                    });
                                }
                                newMsgLay.setVisibility(View.GONE);
                            })));
        } else {
            newMsgLay.setVisibility(View.GONE);
        }
    }

    private void updateCanDownView(boolean canShowDownView) {
        hasNewUserLay.post(() -> {
            if (hasNewMsg && mChatListView.canScrollVertically(1)) {
                hasNewUserLay.setVisibility(View.VISIBLE);
                canDownViewLay.setVisibility(View.GONE);
            } else {
                hasNewUserLay.setVisibility(View.GONE);
                canDownViewLay.setVisibility(canShowDownView ? View.VISIBLE : View.GONE);
            }
        });
    }

    private void updateData() {
        mChatUser = UserService.get().getCacheSimpleLocalUser(chatUid);
        ChatManager.setChatUid(chatUid);
        sendView.setReceiverUid(chatUid);
        gameInviteHelper.setChatUid(chatUid);
        comboView.setTargetUid(chatUid);
        checkShowAddFriend();
        updateUnreadNum();
        checkNewMsgNum();
        adapter.updateMsgList(ChatManager.getInstance().getChats());
        mChatListView.scrollToPosition(adapter.getItemCount() - 1);
        if (UserService.get().isSystemUser(chatUid)) {
            sendView.showSystemUserSingleChatItem();
        } else if (chatUid == LoginHelper.getLoginUid()) {
            sendView.showMyselfSingleChatItem();
        }
        if (mChatUser != null) {
            mTitle.setText(mChatUser.getRemarkName());
            HeadImageLoader.loadHeadImage(mChatUser.headimgurl, 0, addUserHead);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        init(intent);
        checkSendGift(intent);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onOthersRecallMsg(SingleRecallEvent event) {
        WPMessage message = event.recallMsg;

        ChatManager.getInstance().updateOthersRecallMsg(message, true);
        refreshMsgListWithoutScroll();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onShareEvent(ChatMsgShareEvent event) {
        WPMessage msg = event.getChatMsg();
        if (!event.isGroup() && ChatManager.getInstance().isCurrentChatMsg(msg)) {
            ChatManager.getInstance().updateMemory(msg);
            refreshMsgListWithoutScroll();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshMsgListWithoutScroll(RefreshChatListEventWithoutScroll event) {
        refreshMsgListWithoutScroll();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNotifyChatDataChanged(NotifyChatDataChangeEvent event) {
        adapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onShowRecallLoading(ShowRecallLoadingDialogEvent event) {
        if (event.show) {
            recallLoadingDialog.showLoading(this, ResUtil.getStr(R.string.common_recall_loading), true);
        } else {
            recallLoadingDialog.hideLoading();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPushNewFriend(PushNewFriend event) {
        ConversationManager.getInstance().resetUnReadMsgByTargetUid(chatUid);
        checkShowAddFriend();
        updateUnreadNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDeleteMsg(SingleChatDeleteMsgEvent event) {
        String mid = event.msg.getMid();
        ChatManager.getInstance().deleteChat(mid);
        gameInviteHelper.checkCancelMsg(event.msg);
        MsgRoamingUtil.checkDeleteMsg(chatUid, event.msg);
        mHandler.post(this::updateMsgAdapter);

        List<WPMessage> mChats = ChatManager.getInstance().getChats();
        String lastMid = "";
        if (!mChats.isEmpty()) {
            lastMid = mChats.get(mChats.size() - 1).getMid();
        }
        boolean deleteLast = Objects.equals(mid, lastMid);
        if (deleteLast) {
            //检查删除的是否是talk的最后一项，如果是则要修改talk的数据
            WPMessage lastMsg;
            if (!mChats.isEmpty()) {
                lastMsg = mChats.get(mChats.size() - 1);
            } else {
                lastMsg = new WPMessage();
                lastMsg.setSend_uid(LoginHelper.getLoginUid());
                lastMsg.setRecv_uid(chatUid);
                lastMsg.setMedia_type(WPMessage.MEDIA_TYPE_TEXT);
                lastMsg.setMid(String.valueOf(System.currentTimeMillis()));
                lastMsg.setContent("");
                lastMsg.setTime(ConversationInfo.INVALID_TIME);
            }
            //TalkManager.getInstance().saveSingleTalk(lastMsg, false);
            //2018/2/1 删除消息时如何排序
            ConversationManager.getInstance().onNewOldSingleMsg(lastMsg, 0, null);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onResendMsg(SingleChatResendMsgEvent event) {
        try {
            WPMessage wpmsg = event.wpMessage;
            if (wpmsg == null) return;
            ChatManager.getInstance().deleteChat(wpmsg.getMid());
            mHandler.post(this::updateMsgAdapter);

            msgSendListener.resendMsg(wpmsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshList(SingleChatRefreshList event) {
        mHandler.post(() -> {
            updateMsgAdapter();
            mChatListView.setAutoScrollToBottom(true);
            mChatListView.scrollToPosition(ChatManager.getInstance().getChats().size());
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHandlerProtoMsg(ProtoMessageEvent event) {
        handlerProtoMsg(event.action, event.message);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleUnreadMsgNumChange(UnreadMsgNumChangeEvent event) {
        updateUnreadNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onOtherIsInputting(SingleChatInputStatusEvent event) {
//         与ios统一自己给自己发消息时也改变输入状态
//        if (event.fromUid == LoginHelper.getLoginUid()) return  // 屏蔽自己给自己发消息的显示
        if (event.fromUid == chatUid) {
            int res = event.type == ChatPacketSender.INPUT_TYPE_TYPING ? R.string.target_is_inputting_msg : R.string.target_is_inputting_voice;
            mTitle.setText(ResUtil.getStr(res));
            inputTimeDDL = System.currentTimeMillis() + event.ttl * 1000L;
            mHandler.postDelayed(() -> {
                if (System.currentTimeMillis() >= inputTimeDDL) {
                    updateTitle();
                }
            }, event.ttl * 1000L);
        }
    }

    private void refreshMsgListWithoutScroll() {
        updateMsgAdapter(true);
    }

    private void checkSendGift(Intent intent) {
        if (!intent.getBooleanExtra(IntentConfig.KEY_WILL_SEND_GIFT, false)) return;
        int selectedGiftId = intent.getIntExtra(IntentConfig.KEY_SEND_GIFT_ID, 0);
        showSendGift(intent.getStringExtra("source"), selectedGiftId);
    }

    private void showSendGift(String source) {
        showSendGift(source, 0);
    }

    private void showSendGift(String source, int selectedGiftId) {
        sendGiftSource = source;
        Map<String, Object> trackMap = new HashMap<>();
        trackMap.put("target_uid", chatUid);
        TrackUtil.appClick(TrackScreenName.SINGLE_CHAT_PAGE, TrackButtonName.SEND_GIFT_ICON, trackMap);
        GiftAnimUtil.showGiftView(mContext, GiftShowConfigHelper.chat(chatUid, selectedGiftId),
                info -> msgSendListener.onSendGiftMsg(info));
    }

    private void showChooseGame() {
        if (!gameInviteHelper.canSendNew()) {
            ToastUtil.show(R.string.chat_cancle_last_invite);
            return;
        }
        if (UserGameStatusUtil.checkUserStatus(this, UserGameStatusUtil.TYPE_CHAT_INVITE)) {
            return;
        }
        if (LoginHelper.getCoin() < 0) {
            ToastUtil.show(ResUtil.getStr(R.string.chat_your_golds_less_zero));
            PublicCoinPayBottomDialogView.showBottomDialog(this);
            return;
        }
        ChatChooseGameView.showChooseGameView(this, chatUid);
    }

    private void showVoiceLink() {
        if (!gameInviteHelper.canSendNew()) {
            ToastUtil.show(R.string.please_cancel_last_invite_link);
            return;
        }
        if (UserGameStatusUtil.checkUserStatus(this, UserGameStatusUtil.TYPE_LITTLE_GAME_INVITE)
                || UserGameStatusUtil.checkUserStatus(this, UserGameStatusUtil.TYPE_CHAT_INVITE)) {
            return;
        }
        if (PrefUserUtil.getInstance().getBoolean(PrefUserUtil.SHOW_VOICE_LINK_DIALOG, true)) {
            ChooseVoiceLinkDialog.showDialog(this,
                    () -> IDAuthCheckManager.doTaskOrShowNeedCertificate(ChatActivity.this, AuthApi.SCENE_CREATE_VOICE_ROOM, this::doSendVoiceLink));
            PrefUserUtil.getInstance().setBoolean(PrefUserUtil.SHOW_VOICE_LINK_DIALOG, false);
        } else {
            doSendVoiceLink();
        }
    }

    private void doSendVoiceLink() {
        IDAuthCheckManager.doTaskOrShowNeedCertificate(this, AuthApi.SCENE_CREATE_VOICE_ROOM, () -> {
            int gameType = RoomInfo.GAME_TYPE_VOICE_ROOM;
            GameConfig gameConfig = ConfigHelper.getInstance().getGameConfig().getGameConfig(gameType);
            GameInviteInfo gameInviteInfo = new GameInviteInfo();
            gameInviteInfo.gameIcon = gameConfig.getIconUrl();
            gameInviteInfo.gameTitle = ResUtil.getStr(R.string.voice_link_text);
            gameInviteInfo.gameLimit = "";
            gameInviteInfo.gameMode = 0;
            gameInviteInfo.betLevel = 0;
            gameInviteInfo.gameType = gameType;
            WPMessage wpMessage = ChatUtil.structMsg(chatUid, WPMessage.MEDIA_TYPE_GAME_INVITE, gameInviteInfo);
            ChatUtil.sendMsg(wpMessage);
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        checkShowAddFriend();
        updateTitle();
        //if(msgSendListener != null) msgSendListener.onScrollToBottom();
        updataQuickEnterInfo();
        AudioPlayNextManager.getInstance().register();
        isVisible = true;
        setChatBg();
    }

    @Override
    protected void onPause() {
        super.onPause();
        isInputtingVoice = false;
        BgMusicManager.getInstance().stop(MusicSource.SOURCE_CHAT_AUDIO);
        if (isFinishing()) {//singletask 的 onDestory问题
            finishClear();
        }
        AudioPlayNextManager.getInstance().setIsPlay(false);
        AudioPlayNextManager.getInstance().unregister();
        isVisible = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        finishClear();
    }

    /**
     * 在进入用户详细信息后回来时，更新用户备注名
     */
    private void updateTitle() {
        if (mChatUser != null) {
            mTitle.setText(mChatUser.getRemarkName());
        }
    }

    private void finishClear() {
        if (isFinish) return;
        isFinish = true;
        ChatManager.clear();
        RedPacketManager.getInstance().clearMap();
        mHandler.removeCallbacksAndMessages(null);
        BgMusicManager.getInstance().stop(MusicSource.SOURCE_CHAT_AUDIO);
        gameInviteHelper.cancelInviting();
        gameInviteHelper.clear();
        clearEventBus();
    }

    private void clearEventBus() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void setChatBg() {
        String path = WPHelper.INSTANCE.getChatBg(chatUid);
        if (!Objects.equals(currentBgPath, path)) {
            if (!TextUtils.isEmpty(path) && new File(path).exists()) {
                chatBgIv.setVisibility(View.VISIBLE);
                chatBgIvMask.setVisibility(View.VISIBLE);
                ImageLoaderUtil.loadNormalImage("file://" + path, chatBgIv);
            } else {
                chatBgIv.setVisibility(View.GONE);
                chatBgIvMask.setVisibility(View.GONE);
            }
            currentBgPath = path;
        }
    }

    private void sendAddFriend(UserSimpleInfo user) {
        final NewFriendRecord record = UserService.get().getNewFriend().getNewFriendRecordByUid(user.getUid());
        if (record == null) {
            return;
        }
        AddFriendManager.getInstance().acceptFriend(new AddFriendInfo(user.getUid(), TrackSource.SINGLE_CHAT), new AddFriendCallback() {
            @Override
            public void onSuccess(String msg) {
                //在PushNewFriend中统一处理，刷新界面
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void initEvents() {
        mTitleLeft.setOnClickListener(v -> finish());
        mTitleRight.setOnClickListener(v -> {
            if (chatUid == LoginHelper.getLoginUid() || UserService.get().isSystemUser(chatUid)) {
                JumpUtil.enterUserInfoDetailActivity(mContext, chatUid, TrackSource.SINGLE_CHAT);
            } else {
                JumpUtil.gotoChatMoreActivity(mContext, chatUid, TrackSource.SINGLE_CHAT);
            }
        });
        refreshLayout.setOnRefreshListener(refreshlayout -> onPullDown());

        mChatListView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN || event.getAction() == MotionEvent.ACTION_MOVE
                    || event.getAction() == MotionEvent.ACTION_CANCEL) {
                sendView.hideAll();
            }
            return false;
        });
        msgSendListener = new MsgSendListener(this);
        sendView.registerMessageSendListener(msgSendListener);
    }

    private void onPullDown() {
        ChatManager.getInstance().getChatList(ILifeUtil.toLife(this), list -> {
            updateMsgAdapter();
            mChatListView.scrollToPosition(list.size());
            mChatListView.setAutoScrollToBottom(false);
            onRefreshComplete();
        });
    }

    private void onRefreshComplete() {
        mHandler.post(() -> refreshLayout.finishRefresh(0));
        checkShowFlowerAnim();
    }

    private void checkShowFlowerAnim() {
        if (mChatUser == null) return;

        if (mChatUser.getUid() == LoginHelper.getLoginUid()) return;
        ChatManager.getInstance().getUnshowFlowerMessage(chatUid, list -> {
            List<WPModel> modelList = new ArrayList<>(list.size());
            for (WPMessage msg : list) {
                msg.setStatus(WPMessage.STATUS_VIEWED);
                GiftShowInfo showInfo = GiftInfoTransformHelper.fromWpMessage(msg);
                if (showInfo != null && showInfo.giftNum > 0) {
                    showGift(showInfo);
                }
                modelList.add(msg);
            }
            WPStore.saveListAsyncByTransaction(modelList);
        });
    }

    // 展示以小搏大活动爆出的大礼物动效
    private void showServerSendGiftAnim(ChatPackets.chat_pu_msg_chat msgChat) {
        boolean isServerGift = false;
        try {
            JSONObject jsonObject = new JSONObject(msgChat.getExtension());
            isServerGift = jsonObject.getBoolean("server_sent_gift");
        } catch (JSONException e) {
            HLog.d(TAG, HLog.USR, "showServerSendGiftAnim JSONException = {}", e);
        }
        if (isServerGift) {
            ChatManager.getInstance().getUnshowFlowerMessage(msgChat.getSendUid(), msgChat.getRecvUid(), list -> {
                List<WPModel> modelList = new ArrayList<>(list.size());
                for (WPMessage msg : list) {
                    if (!msg.getIsServerSendGiftFromExtension()) {
                        continue;
                    }
                    msg.setStatus(WPMessage.STATUS_VIEWED);
                    GiftShowInfo showInfo = GiftInfoTransformHelper.fromWpMessage(msg);
                    if (showInfo != null && showInfo.giftNum > 0) {
                        showGift(showInfo);
                    }
                    modelList.add(msg);
                }
                WPStore.saveListAsyncByTransaction(modelList);
            });
        }
    }

    private void handlerProtoMsg(String action, GeneratedMessageLite<?, ?> protoMsg) {
        if (ChatPacketConstants.CHAT_RS_MSG_SEND.equals(action)) {
            boolean isRecall = false;
            chat_rs_msg_send msg = (chat_rs_msg_send) protoMsg;
            if (msg.getCode() == HttpCode.CODE_NEED_EXCHANGE_DOUDOU) {
                if (isVisible) DialogUtil.showDouDouNotEnough();
            } else if (msg.getCode() == HttpCode.CODE_COIN_NOT_ENOUGH) {
                if (isVisible) {
                    WPMessage wpMsg = ChatManager.getInstance().getChat(msg.getMid());
                    if (wpMsg != null) {
                        ICareApiFactory.Companion.checkMsgFailedJump(wpMsg);
                    } else {
                        DialogUtil.showPublicCoinNotEnoughDialog();
                    }
                }
            } else if (msg.getCode() == HttpCode.CODE_GIFT_CARD_NOT_ENOUGH) {
                if (isVisible) DialogUtil.showGiftCardNotEnoughDialogInTop();
            } else if (msg.getCode() == HttpCode.CODE_CHAT_MESSAGE_LIMIT) {
                showChatMessageLimitTip(msg.getDesc(), msg.getMid());
            } else if (msg.getCode() != 200) {
                ToastUtil.show(msg.getDesc());
                recallLoadingDialog.hideLoading();
            } else {
                String mid = msg.getMid();
                WPMessage wpMsg = ChatManager.getInstance().getChat(mid);
                if (wpMsg != null) {
                    GiftShowInfo showInfo = GiftInfoTransformHelper.fromWpMessage(wpMsg);
                    if (showInfo != null && showInfo.giftNum > 0) {
                        ComboInfo comboInfo = GiftComboUtil.parseComboInfo(wpMsg);
                        showGift(showInfo);
                        showComboAnim(comboInfo);
                        saveLastComboData(wpMsg.getGiftSendInfoFromExtension());

                        if (isVisible) {
                            ShenceGiftUtil.reportSendGift(GiftInfoTransformHelper.toSendInfo(showInfo), sendGiftSource);
                        }
                    }
                }

                if (ChatManager.getInstance().containRecallMsgMid(msg.getMid())) {
                    isRecall = true;
                    ChatManager.getInstance().updateSelfRecallMsg(msg.getMid());
                }
                recallLoadingDialog.hideLoading();
            }
            if (!isRecall) {
                mChatListView.scrollToPosition(ChatManager.getInstance().getChats().size());
                mChatListView.setAutoScrollToBottom(true);
            } else {
                mChatListView.setAutoScrollToBottom(false);
            }
            mHandler.post(() -> {
                updateMsgAdapter();
                String extraInfo = msg.getExtraInfo();
                checkShowChatMessageLimitTip(extraInfo, msg.getMid());
            });
        } else if (ChatPacketConstants.CHAT_PU_MSG_CHAT.equals(action)) {
            final ChatPackets.chat_pu_msg_chat msg_chat = (ChatPackets.chat_pu_msg_chat) protoMsg;

            mHandler.post(() -> {
                WPMessage wpMessage = ChatManager.getInstance().getChat(msg_chat.getMid());
                if (wpMessage != null) {
                    showComboAnim(GiftComboUtil.parseComboInfo(wpMessage));
                }
                refreshMsgListWithoutScroll();
                //收到一条消息，则清除顶部的正在输入
                updateTitle();
                if (wpMessage != null && (isListViewReachBottomEdge(mChatListView) || wpMessage.getSend_uid() == LoginHelper.getLoginUid())) {
                    mChatListView.scrollToPosition(ChatManager.getInstance().getChats().size());
                }
                String extraInfo = msg_chat.getExtension();
                checkShowChatMessageLimitTip(extraInfo, msg_chat.getMid());
            });
            if (ActivityTaskManager.getInstance().isActivityAtTop(ChatActivity.class)) {
                checkShowFlowerAnim();
                // 自己送礼物且是以小搏大爆出的大礼物，播放大礼物动效
                if (msg_chat.getSendUid() == LoginHelper.getLoginUid()) {
                    showServerSendGiftAnim(msg_chat);
                }
            }
            ConversationManager.getInstance().resetUnReadMsgByTargetUid(chatUid);

        } else if (ChatPacketConstants.CHAT_PU_MSG_SYSTEM.equals(action)) {
            mHandler.post(this::refreshMsgListWithoutScroll);
        } else if (ChatPacketConstants.CHAT_RS_GRAB_RED_PACKET.equals(action)) {
            ChatPackets.chat_rs_msg_grabRedPacketPrivate msg = (ChatPackets.chat_rs_msg_grabRedPacketPrivate) protoMsg;

            getProDialogUtil().hideLoading();
            if (msg.getCode() != 200) {
                ToastUtil.show(msg.getDesc());
            } else {
                String extra = msg.getExtra();
                RedPacketPopUtil.showDetailView(mContext, RedPacketManager.generateRedpacket(extra).getRp_id(), true, RedPacketPopUtil.getRpSkinId(extra));
            }
        } else if (ChatPacketConstants.CHAT_RS_FRIEND_ADD.equals(action)) {
            ChatPackets.chat_rs_friend_add msg = (ChatPackets.chat_rs_friend_add) protoMsg;
            ToastUtil.show(msg.getCode() == 200 ? ResUtil.getStr(R.string.common_add_success) : ResUtil.getStr(R.string.common_add_fail));
        }
    }

    private void checkShowChatMessageLimitTip(String extraInfo, String mainMsgMid) {
        if (TextUtils.isEmpty(extraInfo)) {
            HLog.d(TAG, HLog.USR, "extraInfo is empty");
            return;
        }

        try {
            JSONObject jsonObject = new JSONObject(extraInfo);
            String limitTip = jsonObject.optString("limit_tip");
            HLog.d(TAG, HLog.USR, "extraInfo: {}, limitTip: {}", extraInfo, limitTip);
            if (TextUtils.isEmpty(limitTip)) {
                return;
            }
            showChatMessageLimitTip(limitTip, mainMsgMid);
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "extra parse error");
        }
    }

    private void showChatMessageLimitTip(String content, String mainMsgMid) {
        if (TextUtils.isEmpty(content) || mChatUser == null) {
            return;
        }
        WPMessage message = ChatUtil.structChatLimitMessage(LoginHelper.getLoginUid(), chatUid, content, mainMsgMid);
        ChatManager.getInstance().addChat(message);
        updateMsgAdapter();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (sendView != null) {
            sendView.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onBackPressed() {
        if (sendView == null) {
            super.onBackPressed();
            return;
        }
        sendView.hideCoverView();
        if (sendView.isEmoticonShowing()) {
            sendView.hideEmoticon();
            return;
        }

        if (sendView.isMoreShowing()) {
            sendView.hideMoreLay();
            return;
        }

        finish();
    }

    private void updateMsgAdapter() {
        updateMsgAdapter(false);
    }

    private void updateMsgAdapter(boolean checkNewMsg) {
        List<WPMessage> msgList = ChatManager.getInstance().getChats();
        if (checkNewMsg) {
            hasNewMsg = msgList.size() > adapter.getItemCount();
            if (hasNewMsg) {
                updateCanDownView(false);
            }
        }
        adapter.updateMsgList(msgList);
    }

    private void showGift(GiftShowInfo giftShowInfo) {
        TimeLogger.msg(String.valueOf(giftShowInfo));
        giftContentView.showGiftAnimWithAbTest(giftShowInfo);
    }

    private void showComboAnim(ComboInfo comboInfo) {
        comboView.showGiftAnim(comboInfo);
    }

    private void saveLastComboData(GiftSendInfo sendInfo) {
        if (mChatUser == null || sendInfo == null) return;
        comboView.sendGiftSuccess(sendInfo);
        comboView.setComboTimes(sendInfo.comboTimes);
    }

    private void updateUnreadNum() {
        int unreadNum = ConversationManager.getInstance().getAllUnreadMsgNum() + UserService.get().getNewFriend().getUnreadRecordsNumber();
        if (unreadNum > 99) {
            unreadNumTv.setVisibility(View.VISIBLE);
            unreadNumTv.setText(getString(R.string.c_x_number, "99+"));
        } else if (unreadNum <= 0) {
            unreadNumTv.setVisibility(View.GONE);
            unreadNumTv.setText("");
        } else {
            unreadNumTv.setVisibility(View.VISIBLE);
            unreadNumTv.setText(getString(R.string.c_x_number, String.valueOf(unreadNum)));
        }
    }

    @Override
    public CustomListPopupWindow getListPopupWindow() {
        return customListPopupWindow;
    }

    private static class MsgSendListener implements SendViewModel.MessageSendListener {

        private final WeakReference<ChatActivity> weakReference;
        private final Handler handler = new Handler(Looper.getMainLooper());
        private long lastMsgTime = 0;
        private static final long INTERVAL = 4500;

        MsgSendListener(ChatActivity chatActivity) {
            this.weakReference = new WeakReference<>(chatActivity);
        }

        @Override
        public void onSendTextMsg(String content, String quoteId, String quoteType) {
            WPMessage msg = structMsg(content, WPMessage.MEDIA_TYPE_TEXT);
            msg.setRefMid(quoteId);
            msg.setQuoteType(quoteType);
            sendMsg(msg);
            ConversationManager.getInstance().updateSingleChatDraft(msg.getRecv_uid(), "");
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity != null) {
                chatActivity.lastSendInputTyping = 0;
            }
        }

        @Override
        public void onSendPasswordPacketMsg(String content, String rp_id, int rpSkinId) {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            if (RedPacketManager.getInstance().isPacketGrabed(rp_id, -1)) return;
            IDAuthCheckManager.doTaskOrShowNeedCertificate(chatActivity, AuthApi.SCENE_RECEIVE_RED_PACKET, () ->
                    ChatPacketSender.chatRqGrabRedPacketPrivate(rp_id, content, rpSkinId, new HWTCPSocketThread.WriteCallback() {
                        @Override
                        public void onWriteFailed() {
                        }

                        @Override
                        public void onWriteSuccess() {

                        }
                    }));
        }

        @Override
        public void onSendAudioMsg(String audioPath, int duration) {
            MediaModel media = new ChatAudio(audioPath, duration);
            WPMessage msg = structMsg(media.toMsgString(), WPMessage.MEDIA_TYPE_AUDIO);
            uploadFileAndSendMsg(audioPath, msg);
        }

        @Override
        public void onSendPhotoMsg(String photoPath, int age) {
            MediaModel media = new ChatImage(photoPath, age);
            WPMessage msg = structMsg(media.toMsgString(), WPMessage.MEDIA_TYPE_PHOTO);
            uploadFileAndSendMsg(photoPath, msg);
        }

        @Override
        public void onSendNormalPhotoMsg(List<Item> items, boolean origin, boolean isCamera) {
            if (null == items) {
                HLog.d(TAG, "onSendNormalPhotoMsg! paths is null!");
                return;
            }
            List<Pair<String, WPMessage>> pairs = new ArrayList<>();
            for (Item item : items) {
                WPMessage msg;
                if (item.isVideo()) {
                    msg = structChatVideoMsg(ChatVideo.build(item).toMsgString());
                } else {
                    msg = structChatImageMsg(new ChatImage(item.path, 0));
                }
                msg.setUseOrigin(origin);
                pairs.add(new Pair<>(item.path, msg));
            }
            uploadFilesAndSendMsgArray(pairs, 0);
        }

        @Override
        public void onSendEmoticon(EmoticonItem item) {
            WPMessage msg = structChatImageMsg(ChatImage.emoticon(item.url, TextUtils.isEmpty(item.name) ? ResUtil.getStr(R.string.chat_group_emote_text) : item.name));
            sendMsgWithoutScroll(msg);
            Map<String, Object> eventInfo = new ArrayMap<>(3);
            eventInfo.put("source", TrackSource.SINGLE_CHAT);
            eventInfo.put("btn_type", item.type);
            eventInfo.put("gif_name", item.name);
            ShenceEvent.appClickSource(TrackSource.SINGLE_CHAT, TrackButtonName.SEND_EMOJI, eventInfo);
        }

        @Override
        public void onReSendNormalPhotoMsg(String photoPath) {
            MediaModel media = new ChatImage(photoPath, 0);
            WPMessage msg = structMsg(media.toMsgString(), WPMessage.MEDIA_TYPE_NORMAL_PHOTO);
            uploadFileAndSendMsg(photoPath, msg);
        }

        @Override
        public void onSendDiceMsg() {
            int diceNum = new Random().nextInt(6) + 1;
            WPMessage msg = structMsg(String.valueOf(diceNum), WPMessage.MEDIA_TYPE_DICE);
            sendMsg(msg);
        }

        @Override
        public void onScrollToBottom() {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            chatActivity.mChatListView.setAutoScrollToBottom(true);
            int size = ChatManager.getInstance().getChats().size();
            chatActivity.mChatListView.scrollToPosition(size);
            chatActivity.mChatListView.smoothScrollToPosition(size);
        }

        @Override
        public void onSendRedPacketMsg() {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            IDAuthCheckManager.doTaskOrShowNeedCertificate(chatActivity, AuthApi.SCENE_SEND_RED_PACKET,
                    () -> JumpUtil.gotoSendRedPacketFromSingleChat(chatActivity, chatActivity.chatUid));
        }

        @Override
        public void onShowEmoji(boolean isShow) {

        }

        @Override
        public void onSendIdCard() {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            JumpUtil.gotoIdCardActivity(chatActivity, chatActivity.chatUid);
        }

        public void onSendGiftMsg(GiftSendInfo sendInfo) {
            final String SEND_FLOWER_TEXT = CareUtil.getComboGiftSendText(-1, sendInfo.giftId, sendInfo.giftNum, null, sendInfo.comboTimes);
            if (SEND_FLOWER_TEXT == null) {
                return;
            }
            WPMessage msg = structMsg(SEND_FLOWER_TEXT, WPMessage.MEDIA_TYPE_TEXT, sendInfo);
            sendMsg(msg);
        }

        public void refreshList() {
            final ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            handler.post(chatActivity::updateMsgAdapter);
        }

        //非礼物消息
        private WPMessage structMsg(String content, int mediaType) {
            return structMsg(content, mediaType, new GiftSendInfo());
        }

        private WPMessage structChatImageMsg(ChatImage chatImage) {
            return structMsg(chatImage.toMsgString(), WPMessage.MEDIA_TYPE_NORMAL_PHOTO, new GiftSendInfo(), chatImage);
        }

        private WPMessage structChatVideoMsg(String content) {
            return structMsg(content, WPMessage.MEDIA_TYPE_CHAT_VIDEO, new GiftSendInfo(), null);
        }

        private WPMessage structMsg(String content, int mediaType, GiftSendInfo sendInfo) {
            return structMsg(content, mediaType, sendInfo, null);
        }

        private WPMessage structMsg(String content, int mediaType, GiftSendInfo sendInfo, ChatImage chatImage) {
            final WPMessage msg = new WPMessage();
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return msg;
            int sendUid = LoginHelper.getLoginUid();
            int recvUid = chatActivity.chatUid;
            long time = TimeUtil.getServerTime();
            if (time <= lastMsgTime) {
                time = lastMsgTime + 1;
            }

            msg.setSend_uid(sendUid);
            msg.setRecv_uid(recvUid);
            msg.setMedia_type(mediaType);
            msg.updateContent(content);
            msg.setTime(time);
            msg.setMid(sendUid + "_" + recvUid + "_" + time);
            msg.setStatus(WPMessage.STATUS_SENDING);
            msg.setBubbleId(MyPropManager.getInstance().getMyBubbleId());
            //if(flowerNum > 0) msg.setExtensionWithFlowerNum(flowerNum);
            if (sendInfo.giftNum > 0) {
                msg.setExtensionWithSendInfo(sendInfo);
            } else if (chatImage != null) {
                msg.setSubType(chatImage.isEmoticon() ? ChatMsg.SUBTYPE_EMOTICON : ChatMsg.SUBTYPE_NORMAL_PHOTO);
            }

            ChatManager.getInstance().addChat(msg);
            ConversationManager.getInstance().onNewOldSingleMsg(msg, 0, null);
            //TalkManager.getInstance().saveSingleTalk(msg, true);

            chatActivity.mHandler.postDelayed(() -> {
                if (msg.getStatus() != WPMessage.STATUS_SENDING) return;
                ChatManager.getInstance().updateChat(msg.getMid(), WPMessage.STATUS_FAIL, msg.getTime(), "");
                refreshList();
            }, 1000 * 30);

            refreshList();
            onScrollToBottom();

            lastMsgTime = time;
            return msg;
        }

        private void uploadFileAndSendMsg(String path, final WPMessage msg) {
            if (path.startsWith("http://")) {
                sendMsg(msg);
                return;
            }
            SimpleFileUploader.upload(BucketType.chat, path, new IUploadCallback() {
                @Override
                public void onSuccess(String localPath, String url) {
                    MediaModel model;
                    if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                        model = ChatImage.fromString(msg.getContent());
//                        FileUtil.safeDeleteFile(model.getUrl());
                    } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_PHOTO) {
                        model = ChatImage.fromString(msg.getContent());
//                        FileUtil.safeDeleteFile(model.getUrl());
                    } else {
                        model = ChatAudio.fromString(msg.getContent());
                    }

                    model.setUrl(url);
                    msg.updateContent(model.toMsgString());
                    WPStore.saveAsync(msg);

                    sendMsg(msg);
                }

                @Override
                public void onFailed(int code, String m) {
                    msg.setStatus(WPMessage.STATUS_FAIL);
                    WPStore.saveAsync(msg);
                    handler.post(() -> {
                        refreshList();
                        onScrollToBottom();
                    });
                }
            });
        }

        private void uploadFilesAndSendMsgArray(List<Pair<String, WPMessage>> pairs, final int index) {
            if (index < 0 || index >= pairs.size()) {
                return;
            }
            String path = pairs.get(index).first;
            final WPMessage msg = pairs.get(index).second;
            HLog.d(TAG, HLog.USR, "uploadFilesAndSendMsgArray,path=" + path);
            if (path == null) {
                return;
            }
            if (path.startsWith("http://")) {
                sendMsgWithoutScroll(msg);
                uploadFilesAndSendMsgArray(pairs, index + 1);
                return;
            }
            boolean isVideo = msg.isVideoMsg();
            UploadConfig config = UploadConfig.build(msg.isUseOrigin());
            MediaChooseLimit m = ConfigHelper.getInstance().getConstConfig().getMsgFileUploadLimit();
            config.setLimitSize(isVideo, m.getVideoRawSizeLimit(), m.getOriginPicSizeLimit());
            SimpleFileUploader.upload(BucketType.getBucketType(isVideo, BucketType.chat), path, config, new IUploadCallback() {
                @Override
                public void onSuccess(String localPath, String url) {
                    if (MsgCacheUtil.msgIsDeleted(msg.getMid())) {
                        HLog.d(TAG, HLog.USR, "uploadFilesAndSendMsgArray error! chatMsg={}", msg);
                        return;
                    }
                    HLog.d(TAG, HLog.USR, "onSuccess! localPath={}, url={}", localPath, url);
                    MediaModel model;
                    if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO || msg.getMedia_type() == WPMessage.MEDIA_TYPE_PHOTO) {
                        model = ChatImage.fromString(msg.getContent());
                    } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_CHAT_VIDEO) {
                        model = ChatVideo.fromString(msg.getExtension());
                    } else {
                        model = ChatAudio.fromString(msg.getContent());
                    }

                    model.setUrl(url);
                    msg.updateContent(model.toMsgString());
                    WPStore.saveAsync(msg);
                    sendMsgWithoutScroll(msg);
                    uploadFilesAndSendMsgArray(pairs, index + 1);
                }

                @Override
                public void onFailed(int code, String m) {
                    HLog.d(TAG, HLog.USR, "onFailed! code={}, m={}", code, m);
                    msg.setStatus(WPMessage.STATUS_FAIL);
                    WPStore.saveAsync(msg);
                    ConversationManager.getInstance().updateSingleChatStatus(msg);
                    handler.post(() -> refreshList());
                    uploadFilesAndSendMsgArray(pairs, index + 1);
                }
            });
        }

        private void sendMsg(final WPMessage wpmsg) {
            ChatPacketSender.chatRqMsgSend(wpmsg, new HWTCPSocketThread.WriteCallback() {
                @Override
                public void onWriteFailed() {
                    if (!NetWorkUtil.isNetworkConnected()) {
                        ToastUtil.show(R.string.common_net_close);
                    }
                    wpmsg.setStatus(WPMessage.STATUS_FAIL);
                    WPStore.saveAsync(wpmsg);
                    ConversationManager.getInstance().updateSingleChatStatus(wpmsg);
                    refreshList();
                    onScrollToBottom();
                }

                @Override
                public void onWriteSuccess() {
                    wpmsg.setStatus(WPMessage.STATUS_SENDING);
                    ConversationManager.getInstance().updateSingleChatStatus(wpmsg);
                    WPStore.saveAsync(wpmsg);
                    refreshList();
                    onScrollToBottom();
                }
            });

            NoticeHelper.getInstance().playSendMsgBeep();//播放发送声音的响声
        }

        private void sendMsgWithoutScroll(final WPMessage wpmsg) {
            ChatPacketSender.chatRqMsgSend(wpmsg, new HWTCPSocketThread.WriteCallback() {
                @Override
                public void onWriteFailed() {
                    if (!NetWorkUtil.isNetworkConnected()) {
                        ToastUtil.show(R.string.common_net_close);
                    }
                    wpmsg.setStatus(WPMessage.STATUS_FAIL);
                    WPStore.saveAsync(wpmsg);
                    refreshList();
                }

                @Override
                public void onWriteSuccess() {
                    wpmsg.setStatus(WPMessage.STATUS_SENDING);
                    WPStore.saveAsync(wpmsg);
                    refreshList();
                }
            });

            NoticeHelper.getInstance().playSendMsgBeep();//播放发送声音的响声
        }

        public void resendMsg(WPMessage wpmsg) {
            int type = wpmsg.getMedia_type();
            if (type == WPMessage.MEDIA_TYPE_TEXT) {
                GiftSendInfo sendInfo = wpmsg.getGiftSendInfoFromExtension();
                if (sendInfo != null) {
                    onSendGiftMsg(sendInfo);
                } else {
                    onSendTextMsg(wpmsg.getContent(), wpmsg.getRefMid(), wpmsg.getQuoteType());
                }

            } else if (type == WPMessage.MEDIA_TYPE_DICE) {
                onSendDiceMsg();

            } else if (type == WPMessage.MEDIA_TYPE_PHOTO) {
                MediaModel media = ChatImage.fromString(wpmsg.getContent());
                onSendPhotoMsg(media.getUrl(), media.getAge());
            } else if (type == WPMessage.MEDIA_TYPE_AUDIO) {
                ChatAudio media = ChatAudio.fromString(wpmsg.getContent());
                onSendAudioMsg(media.getUrl(), media.getDuration());
            } else if (type == WPMessage.MEDIA_TYPE_CHAT_VIDEO) {
                onSendVideoMsg(wpmsg.getExtension());
            } else if (type == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                ChatImage media = ChatImage.fromString(wpmsg.getContent());
                if (wpmsg.isEmoticon()) {
                    WPMessage msg = structChatImageMsg(ChatImage.emoticon(media.url, media.getName()));
                    sendMsgWithoutScroll(msg);
                } else {
                    onReSendNormalPhotoMsg(media.getUrl());
                }
            } else if (type == WPMessage.MEDIA_TYPE_GAME_INVITE_NEW) {
                WPMessage wpMessage = ChatUtil.structMsg(wpmsg.getRecv_uid(), wpmsg.getMedia_type(), wpmsg.getGameInviteFromExtension());
                ChatUtil.sendMsg(wpMessage);
            } else if (type == WPMessage.MEDIA_TYPE_GAME_INVITE) {
                WPMessage wpMessage = ChatUtil.structMsg(wpmsg.getRecv_uid(), wpmsg.getMedia_type(), wpmsg.getGameInviteFromExtension());
                ChatUtil.sendMsg(wpMessage);
            }
        }

        @Override
        public void onSendGift() {
            if (weakReference.get() == null) return;
            weakReference.get().showSendGift(TrackSource.SINGLE_CHAT_PLUS);
        }

        @Override
        public void onSendGameInvite() {
            if (weakReference.get() == null) return;
            weakReference.get().showChooseGame();
        }

        @Override
        public void onAtUser() {

        }

        @Override
        public void onSendFamilyBox() {

        }

        @Override
        public void onDraft(String draft) {
            ChatActivity activity = weakReference.get();
            if (activity != null) {
                ConversationManager.getInstance().updateSingleChatDraft(activity.chatUid, draft);
                checkSendInputTyping(activity);
            }
        }

        @Override
        public void onVoiceLink() {
            if (weakReference.get() == null) return;
            weakReference.get().showVoiceLink();
        }

        @Override
        public void onRecordStateChanged(boolean begin) {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity == null) return;
            chatActivity.isInputtingVoice = begin;
            if (begin) {
                ChatPacketSender.chatRqInputStatus(
                        chatActivity.chatUid,
                        ChatPacketSender.INPUT_TYPE_VOICE,
                        new HWTCPSocketThread.SimpleWriteCallback()
                );
                inputVoiceReqLoop();
            }
        }

        @Override
        public void onSendVideoMsg(String content) {
            ChatVideo chatVideo = ChatVideo.fromString(content);
            WPMessage msg = structChatVideoMsg(chatVideo.toMsgString());
            List<Pair<String, WPMessage>> pairs = new ArrayList<>();
            pairs.add(new Pair<>(chatVideo.videoPath, msg));
            uploadFilesAndSendMsgArray(pairs, 0);
        }

        @Override
        public void onQuote(String quoteMid) {
            ChatActivity chatActivity = weakReference.get();
            if (chatActivity != null) {
                ConversationManager.getInstance().updateSingleChatRefMid(chatActivity.chatUid, quoteMid);
            }
        }

        private void checkSendInputTyping(ChatActivity chatActivity) {
            if (chatActivity == null) return;
            long time = System.currentTimeMillis();
            if (time < chatActivity.lastSendInputTyping + INTERVAL) {
                return;
            }
            chatActivity.lastSendInputTyping = time;
            ChatPacketSender.chatRqInputStatus(
                    chatActivity.chatUid,
                    ChatPacketSender.INPUT_TYPE_TYPING,
                    new HWTCPSocketThread.SimpleWriteCallback()
            );
        }

        private void inputVoiceReqLoop() {
            handler.postDelayed(() -> {
                ChatActivity chatActivity = weakReference.get();
                if (chatActivity == null || !chatActivity.isInputtingVoice) {
                    return;
                }
                ChatPacketSender.chatRqInputStatus(
                        chatActivity.chatUid,
                        ChatPacketSender.INPUT_TYPE_VOICE,
                        new HWTCPSocketThread.SimpleWriteCallback());

                inputVoiceReqLoop();
            }, INTERVAL);
        }
    }

    public SendViewModel getSendView() {
        return sendView;
    }

    @Override
    public GameInviteHelper provideInviteHelper() {
        return gameInviteHelper;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            customListPopupWindow.setPosition(ev.getRawX(), ev.getRawY());
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(CHAT_UID, chatUid);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        chatUid = savedInstanceState.getInt(CHAT_UID, 0);
        if (chatUid == 0) {
            ThreadUtil.runOnUiThread(this::finish);
            return;
        }
        updateData();
    }

    @Override
    protected void filterStartup() {
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }

    /**
     * 判断ListView是否到底部
     *
     * @param listView 列表
     * @return 是否到底部
     */
    public boolean isListViewReachBottomEdge(final PullListView listView) {
        return !listView.canScrollVertically(1);
    }

    private void initDraft() {
        if (sendView == null || sendView.editText == null) {
            return;
        }
        String singleChatDraft = ConversationManager.getInstance().getSingleChatDraft(chatUid);
        if (!TextUtils.isEmpty(singleChatDraft)) {
            sendView.editText.setText(
                    EmojiHelper.parseEmoji2Ssb(sendView.editText.getContext(), singleChatDraft, 18));
        }
        String refMid = ConversationManager.getInstance().getSingleChatRefMid(chatUid);
        if (!TextUtils.isEmpty(refMid)) {
            ChatManager.getInstance().findQuoteMsg(refMid, ILifeUtil.toLife(this), this::doQuoteMsg);
        }
        try {
            sendView.editText.setSelection(sendView.editText.getText().length());
            if (sendView.editText.length() > 0) {
                IMMHelper.showSoftInputDelay(sendView.editText, 300);
            }
        } catch (Exception e) {
            TimeLogger.err("error set selection index" + e);
        }
    }

    private void doQuoteMsg(WPMessage msg) {
        if (msg == null) {
            return;
        }
        sendView.doQuote(msg);
    }



    public static void jumpFromConversation(Context mContext, int uid, int unread_num, long last_msg_time) {
        HLog.d(TAG, HLog.USR, "jumpFromConversation, uid=" + uid + ", unread_num=" + unread_num + ", last_msg_time=" + last_msg_time);
        Intent intent = new Intent(mContext, ChatActivity.class);
        intent.putExtra(IntentConfig.KEY_REC_USER_UID, uid);
        intent.putExtra(IntentConfig.UNREAD_MSG, unread_num);
        intent.putExtra(IntentConfig.UNREAD_LAST_MSG_TIME, last_msg_time);
        mContext.startActivity(intent);
    }
}
