package com.wepie.wespy.module.chat.ui.item;

import android.graphics.Color;

import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;

public class GiftCardUtil {
    public static int getGiftDividerColor(ChatMsgBubbleItem bubbleItem, int bubbleID, boolean isSelf) {
        return getGiftDividerColor(bubbleItem.getBubbleItem(), bubbleID, isSelf);
    }

    public static int getGiftDividerColor(ChatBubbleItem bubbleItem, int bubbleID, boolean isSelf) {
        int giftDividerColor = bubbleItem.getGiftDivColor();
        if (bubbleID == 0) {
            if (isSelf) {
                giftDividerColor = Color.parseColor("#C9DEF0");
            } else {
                giftDividerColor = Color.parseColor("#F3F3F3");
            }
        }
        return giftDividerColor;
    }
}
