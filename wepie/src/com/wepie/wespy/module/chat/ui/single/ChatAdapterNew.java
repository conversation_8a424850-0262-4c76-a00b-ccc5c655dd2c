package com.wepie.wespy.module.chat.ui.single;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.database.WPModel;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.TaskItem;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.event.chat.ShowRecallLoadingDialogEvent;
import com.wepie.wespy.module.chat.ChatUtil;
import com.wepie.wespy.module.chat.conversation.CustomListPopupWindow;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.chat.model.ChatImage;
import com.wepie.wespy.module.chat.send.face.EmoticonHelper;
import com.wepie.wespy.module.chat.ui.single.holder.ChatHolder;
import com.wepie.wespy.module.chat.ui.single.holder.ChatLimitHolder;
import com.wepie.wespy.module.chat.ui.single.holder.DefaultChatHolder;
import com.wepie.wespy.module.chat.ui.single.holder.KnownHolder;
import com.wepie.wespy.module.chat.ui.single.holder.RecallHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomAllGameInviteHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomCocosGameInviteHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomDiceHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomGameInviteNewHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomGroupShareHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomH5ShareHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomIdCardHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomInviteHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomNormalPhotoHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomPhotoAndAudioHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomRedPacketHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomRedPacketTipsHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomSystemHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomTextHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomVideoHolder;
import com.wepie.wespy.module.chat.ui.single.holder.room.RoomVipDonateHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfAllGameInviteHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfAudioHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfDiceHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfGameInviteHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfGroupShareHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfH5ShareHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfIdCardHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfNormalImageHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfPhotoHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfRedPacketHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfRedPacketTipsHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfTextHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfVideoHolder;
import com.wepie.wespy.module.chat.ui.single.holder.self.SelfVipHolder;
import com.wepie.wespy.module.game.game.activity.PullListView;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Created by bigwen on 2017/7/28.
 */

public class ChatAdapterNew extends RecyclerView.Adapter<ChatHolder> {

    private final Context mContext;
    private final LayoutInflater mInflater;
    private final List<WPMessage> chatMsgs = new ArrayList<>();

    private static final String TAG = "ChatAdapterNew";
    private static final long INTERVAL = 1000 * 60 * 5;

    private boolean disableClick = false;
    public final PullListView listView;

    private final List<String> showTimeMidList = new LinkedList<>();
    private final int mUid = LoginHelper.getLoginUid();
    public final User mUser = LoginHelper.getLoginUser();
    private Callback callback;
    public int chatMsgSource = 0;

    public ChatAdapterNew(Context context, PullListView listView, Callback callback) {
        this.listView = listView;
        this.mContext = context;
        this.mInflater = LayoutInflater.from(context);
        this.callback = callback;
        setHasStableIds(true);
    }


    @NonNull
    @Override
    public ChatHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == ChatItemType.TYPE_RECALL) {
            return new RecallHolder(mInflater.inflate(R.layout.single_recall_msg_item, parent, false));
        } else if (viewType == ChatItemType.TYPE_CHAT_LIMIT) {
            return new ChatLimitHolder(mInflater.inflate(R.layout.chat_limit_msg_item, parent, false));
        } else if (viewType == ChatItemType.TYPE_UNKNOWN) {
            return new KnownHolder(mInflater.inflate(R.layout.single_msg_unknown_item, parent, false));
        } else if (viewType == ChatItemType.TYPE_SELF_TEXT) {
            return new SelfTextHolder(mInflater.inflate(R.layout.single_self_text_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_DICE) {
            return new SelfDiceHolder(mInflater.inflate(R.layout.single_self_dice_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_NORMAL_PHOTO) {
            return new SelfNormalImageHolder(mInflater.inflate(R.layout.single_self_normal_image_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_CHAT_VIDEO) {
            return new SelfVideoHolder(mInflater.inflate(R.layout.single_self_video_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_PHOTO) {
            return new SelfPhotoHolder(mInflater.inflate(R.layout.single_self_photo_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_AUDIO) {
            return new SelfAudioHolder(mInflater.inflate(R.layout.single_self_audio_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_RED_PACKET) {
            return new SelfRedPacketHolder(mInflater.inflate(R.layout.single_self_red_packet_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_RED_PACKET_TIP) {
            return new SelfRedPacketTipsHolder(mInflater.inflate(R.layout.single_self_red_packet_tips_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_SEND_CARD) {
            return new SelfIdCardHolder(mInflater.inflate(R.layout.single_self_id_card_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_GAME_INVITE_NEW) {
            return new SelfGameInviteHolder(mInflater.inflate(R.layout.single_self_game_invite_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_VIP_DONATE) {
            return new SelfVipHolder(mInflater.inflate(R.layout.single_self_vip_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_GAME_INVITE) {
            return new SelfAllGameInviteHolder(mInflater.inflate(R.layout.single_self_all_game_invite_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_H5_SHARE) {
            return new SelfH5ShareHolder(mInflater.inflate(R.layout.single_self_h5_share_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_SELF_GROUP_SHARE) {
            return new SelfGroupShareHolder(mInflater.inflate(R.layout.single_self_group_share_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_TEXT) {
            return new RoomTextHolder(mInflater.inflate(R.layout.single_room_text_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_DICE) {
            return new RoomDiceHolder(mInflater.inflate(R.layout.single_room_dice_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_NORMAL_PHOTO) {
            return new RoomNormalPhotoHolder(mInflater.inflate(R.layout.single_room_normal_photo_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_CHAT_VIDEO) {
            return new RoomVideoHolder(mInflater.inflate(R.layout.single_room_video_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_PHOTO || viewType == ChatItemType.TYPE_ROOM_AUDIO) {
            return new RoomPhotoAndAudioHolder(mInflater.inflate(R.layout.single_room_photo_audio_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_RED_PACKET) {
            return new RoomRedPacketHolder(mInflater.inflate(R.layout.single_room_red_packet_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_RED_PACKET_TIP) {
            return new RoomRedPacketTipsHolder(mInflater.inflate(R.layout.single_room_red_packet_tips_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_SEND_CARD) {
            return new RoomIdCardHolder(mInflater.inflate(R.layout.single_room_id_card_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_INVITE) {
            return new RoomInviteHolder(mInflater.inflate(R.layout.single_room_invite_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_COCOS_GAME_INVITE) {
            return new RoomCocosGameInviteHolder(mInflater.inflate(R.layout.single_room_cocos_invite_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_GAME_INVITE_NEW) {
            return new RoomGameInviteNewHolder(mInflater.inflate(R.layout.single_room_game_invite_new_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_SYSTEM) {
            return new RoomSystemHolder(mInflater.inflate(R.layout.single_room_system_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_VIP_DONATE) {
            return new RoomVipDonateHolder(mInflater.inflate(R.layout.single_room_vip_donate_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_GAME_INVITE) {
            return new RoomAllGameInviteHolder(mInflater.inflate(R.layout.single_room_all_game_invite_item, parent, false), this);
//        } else if (viewType == ChatItemType.TYPE_ROOM_XROOM_INVITE) {
//            return new RoomXRoomInviteHolder(mInflater.inflate(R.layout.single_room_xroom_invite_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_H5_SHARE) {
            return new RoomH5ShareHolder(mInflater.inflate(R.layout.single_room_h5_share_item, parent, false), this);
        } else if (viewType == ChatItemType.TYPE_ROOM_GROUP_SHARE) {
            return new RoomGroupShareHolder(mInflater.inflate(R.layout.single_room_group_share_item, parent, false), this);
        } else {
            return new DefaultChatHolder(mInflater.inflate(R.layout.single_room_default_item, parent, false), this);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull ChatHolder holder, int position) {
        WPMessage msg = chatMsgs.get(position);
        holder.bind(msg, position);
    }

    @Override
    public int getItemCount() {
        return chatMsgs.size();
    }

    public void updateMsgList(List<WPMessage> chats) {
        this.chatMsgs.clear();
        this.chatMsgs.addAll(chats);
        notifyDataSetChanged();
        if (listView.isAutoScrollToBottom() && !chats.isEmpty()) {
            listView.post(new Runnable() {
                @Override
                public void run() {
                    listView.scrollToPosition(getItemCount() - 1);
                }
            });
        }
    }

    public void setDisableClick(boolean disableClick) {
        this.disableClick = disableClick;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        if (position >= chatMsgs.size()) return ChatItemType.TYPE_NULL;

        WPMessage msg = chatMsgs.get(position);
        if (msg == null) {
            HLog.d(TAG, "GameAdapter getItemViewType: null msg ! position=" + position);
            return ChatItemType.TYPE_NULL;
        }
        if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_RECALL) return ChatItemType.TYPE_RECALL;
        if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_CHAT_LIMIT) {
            return ChatItemType.TYPE_CHAT_LIMIT;
        }

        if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_SYSTEM || msg.getSend_uid() != mUid) {
            if (msg.getMedia_type() == ChatMsg.MEDIA_TYPE_TEXT) {
                return ChatItemType.TYPE_ROOM_TEXT;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_DICE) {
                return ChatItemType.TYPE_ROOM_DICE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                return ChatItemType.TYPE_ROOM_NORMAL_PHOTO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_CHAT_VIDEO) {
                return ChatItemType.TYPE_ROOM_CHAT_VIDEO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_PHOTO) {
                return ChatItemType.TYPE_ROOM_PHOTO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_AUDIO) {
                return ChatItemType.TYPE_ROOM_AUDIO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_RED_PACKET) {
                return ChatItemType.TYPE_ROOM_RED_PACKET;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_RED_PACKET_TIP) {
                return ChatItemType.TYPE_ROOM_RED_PACKET_TIP;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_SEND_CARD) {
                return ChatItemType.TYPE_ROOM_SEND_CARD;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_INVITE) {
                return ChatItemType.TYPE_ROOM_INVITE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_COCOS_GAME_INVITE) {
                return ChatItemType.TYPE_ROOM_COCOS_GAME_INVITE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GAME_INVITE_NEW) {
                return ChatItemType.TYPE_ROOM_GAME_INVITE_NEW;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_SYSTEM) {
                return ChatItemType.TYPE_ROOM_SYSTEM;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_VIP_DONATE) {
                return ChatItemType.TYPE_ROOM_VIP_DONATE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GAME_INVITE) {
                return ChatItemType.TYPE_ROOM_GAME_INVITE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_XROOM_INVITE) {
                return ChatItemType.TYPE_ROOM_XROOM_INVITE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_H5_SHARE) {
                return ChatItemType.TYPE_ROOM_H5_SHARE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GROUP_SHARE) {
                return ChatItemType.TYPE_ROOM_GROUP_SHARE;
            } else {
                //系统不支持的消息类型
                return ChatItemType.TYPE_UNKNOWN;
            }
        } else if (msg.getSend_uid() == mUid) {
            if (msg.getMedia_type() == ChatMsg.MEDIA_TYPE_TEXT) {
                return ChatItemType.TYPE_SELF_TEXT;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_DICE) {
                return ChatItemType.TYPE_SELF_DICE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                return ChatItemType.TYPE_SELF_NORMAL_PHOTO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_CHAT_VIDEO) {
                return ChatItemType.TYPE_SELF_CHAT_VIDEO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_PHOTO) {
                return ChatItemType.TYPE_SELF_PHOTO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_AUDIO) {
                return ChatItemType.TYPE_SELF_AUDIO;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_RED_PACKET) {
                return ChatItemType.TYPE_SELF_RED_PACKET;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_RED_PACKET_TIP) {
                return ChatItemType.TYPE_SELF_RED_PACKET_TIP;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_SEND_CARD) {
                return ChatItemType.TYPE_SELF_SEND_CARD;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GAME_INVITE_NEW) {
                return ChatItemType.TYPE_SELF_GAME_INVITE_NEW;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_VIP_DONATE) {
                return ChatItemType.TYPE_SELF_VIP_DONATE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GAME_INVITE) {
                return ChatItemType.TYPE_SELF_GAME_INVITE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_H5_SHARE) {
                return ChatItemType.TYPE_SELF_H5_SHARE;
            } else if (msg.getMedia_type() == WPMessage.MEDIA_TYPE_GROUP_SHARE) {
                return ChatItemType.TYPE_SELF_GROUP_SHARE;
            } else {
                //默认消息类型
                return ChatItemType.TYPE_DEFAULT;
            }
        } else {
            //系统不支持的消息类型
            return ChatItemType.TYPE_UNKNOWN;
        }
    }

    public boolean isTimeShow = false;

    public void showTime(RelativeLayout timeLay, TextView timeView, Space iocnPadding,
                         WPMessage msg, int position) {

        timeLay.setVisibility(View.GONE);
        isTimeShow = false;
        if (position == 0) {
            timeLay.setVisibility(View.VISIBLE);
            iocnPadding.setVisibility(View.VISIBLE);
            timeView.setText(formatChatTime(msg.getTime()));
            isTimeShow = true;

            showTimeMidList.add(msg.getMid());
        } else if (position > 0) {
            long ctime = msg.getTime();
            long ltime = chatMsgs.get(position - 1).getTime();
            long cinterval = ctime - ltime;

            if (cinterval > INTERVAL || showTimeMidList.contains(msg.getMid())) {
                timeLay.setVisibility(View.VISIBLE);
                iocnPadding.setVisibility(View.VISIBLE);
                timeView.setText(formatChatTime(msg.getTime()));
                isTimeShow = true;
            }
        }
    }

    private String formatChatTime(long time) {
        if (TimeUtil.isToday(time)) {
            return TimeUtil.longToMin(time);
        } else if (TimeUtil.IsYesterday(time)) {
            return ResUtil.getStr(R.string.common_yesterday, TimeUtil.longToMin(time));
        } else if (TimeUtil.isBeforeDays(time, 7)) {
            return TimeUtil.longToWeek(time) + " " + TimeUtil.longToMin(time);
        } else {
            return TimeUtil.longToYearAndDayAndMin(time);
        }
    }

    public HashMap<WPModel, View> viewObjMap = new HashMap<WPModel, View>();

    public class ChatLongClickListener implements View.OnLongClickListener {
        private int position;

        public ChatLongClickListener(int position) {
            this.position = position;
        }

        @Override
        public boolean onLongClick(View v) {
            doLongClick(v, position);
            return true;
        }
    }

    private void doLongClick(View v, final int position) {
        HLog.d(TAG, HLog.USR, "doLongClick!");
        if (disableClick || position >= chatMsgs.size()) {
            HLog.d(TAG, HLog.USR, "return! disableClick=" + disableClick + ", position=" + position + ", size=" + chatMsgs.size());
            return;
        }
        CustomListPopupWindow customListPopupWindow = CustomListPopupWindow.getListPopupWindow(mContext);
        if (customListPopupWindow == null) {
            HLog.d(TAG, HLog.USR, "return! customListPopupWindow is null");
            return;
        }

        final WPMessage msg = chatMsgs.get(position);
        final boolean isText = msg.getMedia_type() == WPMessage.MEDIA_TYPE_TEXT;
        final boolean canRecall = msg.canRecall();
        final boolean isEmoticon = msg.isEmoticon();
        // 单聊场景下不能选择给自己送礼
        final boolean canSendGift = callback != null && !UserService.get().isSystemUser(msg.getSend_uid()) && LoginHelper.getLoginUid() != msg.getSend_uid();

        final String text = msg.getContent();
        List<TaskItem> taskItems = new LinkedList<>();
        if (callback != null && msg.isCanQuote()) {
            taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_reply), R.drawable.chat_item_quote, () -> doQuote(msg)));
        }
        if (isText) {
            taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_copy), R.drawable.chat_item_copy, () -> doCopyMsg(TextSpanUtil.getCustomSpanStr(text))));
        } else if (isEmoticon) {
            ChatImage image = ChatImage.fromString(msg.getContent());
            if (!EmoticonHelper.getHelper().hasFavoriteEmoticon(image.url)) {
                taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_add_emoji), R.drawable.chat_item_addstickers, () -> doAddEmoticon(msg)));
            }
        }
        if (canRecall) {
            taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_text_recall), R.drawable.chat_item_undo, () -> handleRecall(msg)));
        } else {
            taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_text_remove), R.drawable.chat_item_delete, () -> doDeleteMsg(msg)));
        }
        if (canSendGift) {
            taskItems.add(new TaskItem(ResUtil.getStr(R.string.common_text_gift), R.drawable.chat_item_gift, () -> doSendGift(msg)));
        }
        customListPopupWindow.showView(v, true, taskItems);
    }

    private void handleRecall(final WPMessage msg) {
        if (PrefUtil.getInstance().getBoolean(PrefUtil.FIRST_RECALL, true)) {
            DialogBuild.newBuilder(mContext).setSingleBtn(true).setContent(mContext.getResources().getString(R.string.common_text_konw)).setSureTx(ResUtil.getStr(R.string.common_text_remove)).setCanCancel(false).setDialogCallback(new DialogBuild.DialogCallback() {
                @Override
                public void onClickSure() {
                    doRecall(msg);
                }

                @Override
                public void onClickCancel() {

                }
            }).show();
            PrefUtil.getInstance().setBoolean(PrefUtil.FIRST_RECALL, false);
        } else {
            doRecall(msg);
        }
    }

    private void doQuote(WPMessage msg) {
        if (callback != null) callback.doQuote(msg);
        Map<String, Object> map = new HashMap<>();
        map.put("scene", TrackSource.SINGLE_CHAT);
        map.put("target_id", msg.getRecv_uid());
        TrackUtil.appClick(TrackScreenName.MSG_LONG_CLICK_WINDOW, TrackButtonName.REPLY, map);
    }

    private void doRecall(WPMessage msg) {
        EventBus.getDefault().post(new ShowRecallLoadingDialogEvent(true));
        WPMessage recallMsg = ChatUtil.structRecallMsg(msg.getRecv_uid(), msg.getMid(), msg.getTime());
        ChatManager.getInstance().addRecallMsgMid(recallMsg.getMid(), msg.getMid());
        ChatUtil.sendRecallMsg(recallMsg);
    }

    private void doCopyMsg(String text) {
        ClipboardManager clipboard = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("data", text);
        clipboard.setPrimaryClip(clip);
        ToastUtil.show(R.string.common_copied);
    }

    private void doAddEmoticon(WPMessage msg) {
        if (msg.isEmoticon()) {
            ChatImage chatImage = ChatImage.fromString(msg.getContent());
            if (chatImage.url != null && chatImage.url.startsWith("http")) {
                showLoadingDelay();
                EmoticonHelper.getHelper().addFavorite(chatImage.url, mContext, new LifeDataCallback<Object>(ContextUtil.getLife(mContext)) {
                    @Override
                    public void onSuccess(Result<Object> result) {
                        hideLoading();
                        ToastUtil.show(R.string.add_success);
                        ShenceEvent.appClickSource(TrackSource.SINGLE_CHAT, TrackButtonName.ADD_FAVORITE);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        hideLoading();
                        ToastUtil.show(msg);
                    }
                });
            }
        }
    }

    private void showLoadingDelay() {
        DialogUtil.showProgressDialogDelay(mContext);
    }

    private void hideLoading() {
        DialogUtil.hideProgressDialog(mContext);
    }

    private void doDeleteMsg(WPMessage msg) {
        EventDispatcher.postSingleChatDeleteMsg(msg);
    }

    private void doSendGift(WPMessage msg) {
        if (callback != null) callback.doSendGift(msg);
        Map<String, Object> map = new HashMap<>();
        map.put("scene", TrackSource.SINGLE_CHAT);
        map.put("target_id", msg.getRecv_uid());
        TrackUtil.appClick(TrackScreenName.MSG_LONG_CLICK_WINDOW, TrackButtonName.SEND_GIFT, map);
    }

    public interface Callback {
        void doQuote(WPMessage msg);

        void doSendGift(WPMessage msg);
    }
}
