package com.wepie.wespy.module.voiceroom.main.enter.car.queue.items

import com.huiwan.base.util.ToastUtil
import com.huiwan.user.entity.UserHomeAnimInfo
import com.huiwan.user.http.UserApi
import com.three.http.callback.ContinuationDataCallback
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.ILifeRegistry
import com.wejoy.weplay.helper.chain.queue.ActionItem
import com.wepie.liblog.main.HLog
import com.wepie.wespy.module.contact.detail.UserInfoDetailActivity
import com.wepie.wespy.module.voiceroom.main.enter.car.helper.CarAnimHelper.Companion.HOME_ANIM_INFO
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

/**
 * Created by lsy on 2022/10/10.
 */
class UserHomeAnimApiAction(val mUid: Int) : ActionItem() {
    override fun process(finishAction: (success: Boolean) -> Unit) {
        val job = MainScope().launch {
            val result = suspendCancellableCoroutine {
                UserApi.getUserHomeAnim(
                    mUid,
                    object : ContinuationDataCallback<UserHomeAnimInfo>(it) {})
            }
            if (result is KtResultSuccess) {
                shareStoreMap?.run {
                    put(HOME_ANIM_INFO, result.data)
                    HLog.d(
                        UserHomeAnimApiAction::class.java.simpleName,
                        HLog.USR,
                        "RESULT useCarId:${result.data.useCarId} " +
                                "useHomeAnimationId:${result.data.useHomeAnimationId}"
                    )
                }
            } else if (result is KtResultFailed) {
                ToastUtil.show(result.failedDesc)
                HLog.d(
                    UserInfoDetailActivity.TAG,
                    HLog.USR,
                    "showUserHomeAnim onFail ,i=${result.code}, msg=${result.failedDesc}"
                )
            }
            finishAction.invoke(true)
        }
        life.register(object : ILifeRegistry() {
            override fun onDestroy() {
                job.cancel()
            }
        })
    }
}