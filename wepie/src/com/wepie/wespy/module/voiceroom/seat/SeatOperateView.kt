package com.wepie.wespy.module.voiceroom.seat

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.RelativeLayout
import android.widget.TextView
import com.huiwan.base.util.TouchEffectUtil
import com.huiwan.user.LoginHelper
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo

class SeatOperateView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : RelativeLayout(context, attrs) {

    private var listener: DialogListener? = null

    private val upBt: View
    private val forbidSpeakLay: View
    private val forceUpLay: View
    private val invitePeopleSpeakLay: View
    private val operateLay: View
    private val silentUpLay: View

    private val silentTx: TextView


    init {
        LayoutInflater.from(context).inflate(R.layout.seat_dialog_operate_view, this, true)
        upBt = findViewById(R.id.seat_dialog_self_up_bt)
        forbidSpeakLay = findViewById(R.id.seat_dialog_silent_lay)
        forceUpLay = findViewById(R.id.seat_dialog_force_up_lay)
        invitePeopleSpeakLay = findViewById(R.id.invite_people_speak_lay)
        operateLay = findViewById(R.id.seat_dialog_operate_lay)
        silentTx = findViewById(R.id.seat_dialog_silent_tx)
        silentUpLay = findViewById(R.id.seat_dialog_su_lay)

    }

    fun showOperate(
        roomInfo: VoiceRoomInfo,
        seatInfo: VoiceRoomInfo.SeatInfo
    ) {
        val isSeaterSelf = seatInfo.uid == LoginHelper.getLoginUid()
        val isSeaterOwner: Boolean = roomInfo.isOwner(seatInfo.uid)
        val isSelfOwner: Boolean = roomInfo.isSelfOwner()
        val isSelfAdmin: Boolean = roomInfo.isSelfAdmin()

        if (roomInfo.isLoveHome() || roomInfo.isAudioMatchRoom()) {
            operateLay.visibility = GONE
            return
        }

        if (isSeaterSelf && !isSelfOwner) {
            operateLay.visibility = VISIBLE
            silentUpLay.visibility = GONE
            upBt.visibility = VISIBLE
            TouchEffectUtil.addTouchEffect(upBt)
            upBt.setOnClickListener(OnClickListener { view: View? ->
                if (listener != null) listener!!.operateStandUp()
            })
        } else if (!isSeaterSelf && !isSeaterOwner && (isSelfOwner || isSelfAdmin)) {
            operateLay.visibility = VISIBLE
            silentUpLay.visibility = VISIBLE
            upBt.visibility = GONE

            TouchEffectUtil.addTouchEffect(forceUpLay)
            forceUpLay.setOnClickListener(OnClickListener { view: View? ->
                if (listener != null) listener!!.operateForceUp()
            })

            val can_speak: Boolean = seatInfo.can_speak
            silentTx.setText(if (can_speak) R.string.room_seat_ctrl_mic_close else R.string.room_seat_ctrl_mic_open)
            TouchEffectUtil.addTouchEffect(forbidSpeakLay)
            forbidSpeakLay.setOnClickListener(OnClickListener {
                if (listener != null) listener!!.operateForbidMic()
            })
        } else {
            operateLay.visibility = GONE
        }

        invitePeopleSpeakLay.setOnClickListener(OnClickListener { v: View? ->
            if (listener != null) listener!!.operateInvitePeople()
        })
    }


    fun registerCloseListener(listener: DialogListener) {
        this.listener = listener
    }

    interface DialogListener {
        fun onClose()

        fun operateStandUp()

        fun operateForceUp()

        fun operateForbidMic()

        fun operateInvitePeople()
    }
}
