package com.wepie.wespy.module.voiceroom.seat

import android.app.Dialog
import android.content.Context
import android.view.View
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.collection.ListWrapper
import com.huiwan.configservice.ConfigHelper
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.DialogBuild
import com.wepie.wespy.helper.dialog.DialogUtil
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo.SeatInfo
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.match.start.AudioMatchManager
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic
import com.wepie.wespy.module.voiceroom.main.requestRecordPermission
import com.wepie.wespy.module.voiceroom.mic.VoiceMicDialog
import com.wepie.wespy.module.voiceroom.pk445.ISeatIntercept
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.module.voiceroom.user.SeatUserDialogView
import com.wepie.wespy.module.voiceroom.voicegame.VoieGameDialogUtil.showUserDialogInGameSeat
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.GetSeatQueueUidListRsp
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

internal class BaseSeatViewHelper(private val baseSeatView: BaseSeatView) {
    private var lastState: BaseSeatViewState? = null
    private var seatUserDialogView: SeatUserDialogView? = null
    private var oldUserInfo: UserSimpleInfo? = null
    private var seatInterceptList: MutableList<ISeatIntercept> = ArrayList()

    private var seatOperateView: SeatOperateView = SeatOperateView(baseSeatView.context)

    private val seatCallback = object : IBaseSeatCallback {
        override fun getUserInfo(uid: Int, onSuccess: (UserSimpleInfo?, UserSimpleInfo) -> Unit) {
            if (baseSeatView.forceUpdateSeatUserInfo) {
                showServerUserInfo(uid) {
                    onSuccess.invoke(oldUserInfo, it)
                    oldUserInfo = it.clone()
                }
            } else {
                showCachedUserInfo(uid) {
                    onSuccess.invoke(oldUserInfo, it)
                    oldUserInfo = it.clone()
                }
            }
        }

        private fun showServerUserInfo(uid: Int, onSuccess: (UserSimpleInfo) -> Unit) {
            UserService.get().getCacheSimpleUserFromServer(uid,
                object : LifeUserSimpleInfoCallback(baseSeatView) {
                    override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo) {
                        onSuccess.invoke(simpleInfo)
                    }

                    override fun onUserInfoFailed(description: String?) {
                        showCachedUserInfo(uid, onSuccess)
                    }
                })
        }

        private fun showCachedUserInfo(uid: Int, onSuccess: (UserSimpleInfo) -> Unit) {
            UserService.get().getCacheSimpleUser(uid,
                object : LifeUserSimpleInfoCallback(baseSeatView) {
                    override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo) {
                        onSuccess.invoke(simpleInfo)
                    }

                    override fun onUserInfoFailed(description: String?) = Unit
                })
        }
    }

    fun update(roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
        val currentState = when {
            seatInfo.isEmpty -> {
                if (seatInfo.uid == roomInfo.owner) {
                    BaseSeatViewState.RoomOwnerOffline(
                        baseSeatView,
                        roomInfo,
                        seatInfo,
                        seatCallback
                    )
                } else {
                    oldUserInfo = null
                    BaseSeatViewState.Empty(baseSeatView, roomInfo, seatInfo)
                }
            }

            seatInfo.isSealed -> {
                oldUserInfo = null
                BaseSeatViewState.Sealed(baseSeatView, roomInfo, seatInfo)
            }

            else -> {
                BaseSeatViewState.Seated(baseSeatView, roomInfo, seatInfo, seatCallback)
            }
        }
        currentState.update(lastState)

        // 更新弹窗信息
        if (seatUserDialogView != null) {
            seatUserDialogView!!.showOperate(roomInfo, seatInfo)
        }
        //更新语音动画
        if (currentState is BaseSeatViewState.Empty || !seatInfo.can_speak) {
            baseSeatView.stopSpeakAnim()
        } else {
            baseSeatView.speakerAnimView.update(
                seatInfo.getMemberLevel(),
                seatInfo.isMemberFreeze()
            )
        }
        // 更新分数
        updateScoreInfo(seatInfo)
        // 点击事件
        baseSeatView.clickView.setOnClickListener {
            when (currentState) {
                is BaseSeatViewState.Empty -> {
                    stateEmptyClick(roomInfo, seatInfo)
                }

                is BaseSeatViewState.RoomOwnerOffline, is BaseSeatViewState.Seated -> {
                    stateHasUserClick(baseSeatView.context, roomInfo, seatInfo)
                }

                is BaseSeatViewState.Sealed -> {
                    stateSealedClick(roomInfo, seatInfo)
                }
            }
        }
        baseSeatView.clickView.setOnLongClickListener { _ ->
            if (seatInfo.isEmpty || seatInfo.isSealed || roomInfo.isAudioMatchRoom) {
                return@setOnLongClickListener false
            } else {
                baseSeatView.mainPlugin.doAtUser(seatInfo.uid)
                return@setOnLongClickListener true
            }
        }
        lastState = currentState
    }

    /**
     * 更新座位分数信息
     */
    fun updateScoreInfo(seatInfo: SeatInfo) {
        if (baseSeatView.scoreTv != null) {
            if (seatInfo.scoreOpen) {
                baseSeatView.scoreTv.text = StringUtil.formatSimpleNum(seatInfo.score.toLong())
            }
            val t = if (seatInfo.scoreOpen) View.VISIBLE else View.GONE
            if (baseSeatView.scoreTv.visibility != t) {
                baseSeatView.scoreTv.visibility = t
            }
        }
    }

    /**
     * 座位Empty点击事件
     */
    private fun stateEmptyClick(roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
        fun showCloseSeatDialog(
            context: Context,
            roomInfo: VoiceRoomInfo,
            seatInfo: SeatInfo
        ) {
            val data = java.util.ArrayList<Int>()
            val canSpeak = seatInfo.can_speak
            val isSelfSeated = roomInfo.isUserSeated(LoginHelper.getLoginUid())
            if (!isSelfSeated) {
                data.add(R.string.room_seat_ctrl_sit)
            }
            data.add(R.string.room_seat_ctrl_close)
            data.add(if (canSpeak) R.string.room_seat_ctrl_mic_close else R.string.room_seat_ctrl_mic_open)
            data.add(R.string.room_seat_ctrl_invite)
            if (roomInfo.isSelfAdmin && isSelfSeated) {
                data.add(R.string.room_seat_ctrl_change_seat)
            }
            data.add(R.string.cancel)
            DialogUtil.showBottomDialog(
                context,
                ListWrapper<Int>(data).map<String> { resId: Int? ->
                    ResUtil.getStr(
                        resId!!
                    )
                }
            ) { position: Int ->
                if (!isSelfSeated) {
                    when (position) {
                        0 -> {
                            doSitAction(context, roomInfo.rid, seatInfo.seat_num)
                        }

                        1 -> {
                            RoomSenderPresenter.sealSeat(roomInfo.rid, seatInfo.seat_num, true)
                        }

                        2 -> {
                            RoomSenderPresenter.forbidSpeak(
                                roomInfo.rid,
                                seatInfo.seat_num,
                                canSpeak
                            )
                        }

                        3 -> {
                            JumpUtil.gotoRoomMemberActivityWithSeatNum(
                                context,
                                roomInfo.rid,
                                VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                                seatInfo.seat_num
                            )
                        }
                    }
                } else if (roomInfo.isSelfAdmin) {
                    when (position) {
                        0 -> {
                            RoomSenderPresenter.sealSeat(roomInfo.rid, seatInfo.seat_num, true)
                        }

                        1 -> {
                            RoomSenderPresenter.forbidSpeak(
                                roomInfo.rid,
                                seatInfo.seat_num,
                                canSpeak
                            )
                        }

                        2 -> {
                            JumpUtil.gotoRoomMemberActivityWithSeatNum(
                                context,
                                roomInfo.rid,
                                VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                                seatInfo.seat_num
                            )
                        }

                        3 -> {
                            seatInterceptList.forEach { seatIntercept ->
                                val interceptFlag = seatIntercept.switchSeatIntercept(
                                    seatInfo.seat_num,
                                    context
                                )
                                if (interceptFlag) {
                                    return@showBottomDialog
                                }
                            }
                            DialogBuild.newBuilder(context).setSingleBtn(false)
                                .setContent(ResUtil.getStr(R.string.room_seat_ctrl_change_seat_tips))
                                .setCanCancel(false)
                                .setDialogCallback {
                                    doSitAction(
                                        context,
                                        roomInfo.rid,
                                        seatInfo.seat_num
                                    )
                                }.show()
                        }
                    }
                } else {
                    when (position) {
                        0 -> {
                            RoomSenderPresenter.sealSeat(roomInfo.rid, seatInfo.seat_num, true)
                        }

                        1 -> {
                            RoomSenderPresenter.forbidSpeak(
                                roomInfo.rid,
                                seatInfo.seat_num,
                                canSpeak
                            )
                        }

                        2 -> {
                            JumpUtil.gotoRoomMemberActivityWithSeatNum(
                                context,
                                roomInfo.rid,
                                VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                                seatInfo.seat_num
                            )
                        }
                    }
                }
            }
        }
        if (baseSeatView.forbidClickEmptySeat()) {
            return
        } else if (baseSeatView.isWeddingOwnerSeat && !roomInfo.isSelfOwner) {
            ToastUtil.show(R.string.room_seat_for_g_b)
            return
        }
        if (roomInfo.isSelfOwner || roomInfo.isSelfAdmin) {
            if (!baseSeatView.isWeddingOwnerSeat) {
                showCloseSeatDialog(baseSeatView.context, roomInfo, seatInfo)
            }
            return
        }
        if (roomInfo.isUserSeated(LoginHelper.getLoginUid())) {
            seatInterceptList.forEach { seatIntercept ->
                val interceptFlag =
                    seatIntercept.switchSeatIntercept(seatInfo.seat_num, baseSeatView.context)
                if (interceptFlag) {
                    return
                }
            }
            DialogBuild.newBuilder(baseSeatView.context).setSingleBtn(false)
                .setContent(ResUtil.getStr(R.string.room_seat_ctrl_change_seat_tips))
                .setCanCancel(false)
                .setDialogCallback {
                    doSitAction(
                        baseSeatView.context,
                        roomInfo.rid,
                        seatInfo.seat_num
                    )
                }.show()
            return
        }
        if (roomInfo.inSeatQueueOpen &&
            (roomInfo.roomMemberInfo == null ||
                    roomInfo.isFreeze ||
                    roomInfo.roomMemberInfo.level < 9
                    )
        ) {
            VoiceRoomPacketSender.getMicUidList(
                roomInfo.rid,
                object : LifeSeqCallback(baseSeatView) {
                    override fun onSuccess(head: RspHeadInfo) {
                        val rsp = head.message as GetSeatQueueUidListRsp
                        VoiceMicDialog.showDialog(
                            baseSeatView.context,
                            roomInfo,
                            rsp.uidLitList
                        )
                    }

                    override fun onFail(head: RspHeadInfo) {
                        ToastUtil.show(head.desc)
                    }
                })
        } else {
            doSitAction(baseSeatView.context, roomInfo.rid, seatInfo.seat_num)
        }
    }

    /**
     * 发起服务器请求的同时，申请权限，权限是否申请成功不影响上麦
     */
    private fun doSitAction(context: Context, rid: Int, seatNum: Int) {
        RoomViewLogic.sit(context, rid, seatNum)
        requestRecordPermission(context) {}
    }

    /**
     * 座位Sealed点击事件
     */
    private fun stateSealedClick(roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
        fun showOpenSeatDialog(roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
            val data = ArrayList<Int>()
            val canSpeak = seatInfo.can_speak
            data.add(R.string.room_seat_ctrl_open)
            data.add(if (canSpeak) R.string.room_seat_ctrl_mic_close else R.string.room_seat_ctrl_mic_open)
            data.add(R.string.room_seat_ctrl_invite)
            data.add(R.string.cancel)
            DialogUtil.showBottomDialog(
                baseSeatView.context,
                ListWrapper<Int>(data).map<String> { resId: Int? ->
                    ResUtil.getStr(
                        resId!!
                    )
                }
            ) { position: Int ->
                when (position) {
                    0 -> {
                        RoomSenderPresenter.sealSeat(roomInfo.rid, seatInfo.seat_num, false)
                    }

                    1 -> {
                        RoomSenderPresenter.forbidSpeak(roomInfo.rid, seatInfo.seat_num, canSpeak)
                    }

                    2 -> {
                        JumpUtil.gotoRoomMemberActivityWithSeatNum(
                            baseSeatView.context,
                            roomInfo.rid,
                            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                            seatInfo.seat_num
                        )
                    }
                }
            }
        }
        if (roomInfo.isSelfOwner || roomInfo.isSelfAdmin) {
            showOpenSeatDialog(
                roomInfo,
                seatInfo
            )
        }
    }

    /**
     * 座位RoomOwnerOffline或者Seated点击事件
     */
    private fun stateHasUserClick(context: Context, roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
        fun checkShown(roomInfo: VoiceRoomInfo, seatInfo: SeatInfo): Boolean {
            return if (!roomInfo.isAudioMatchRoom) {
                true
            } else if (seatInfo.uid == LoginHelper.getLoginUid() && !AudioMatchManager.getInstance().myselfShown) {
                false
            } else seatInfo.uid == LoginHelper.getLoginUid() || AudioMatchManager.getInstance().otherShown
        }
        if (checkShown(roomInfo, seatInfo)) {
            if (roomInfo.isInSeat(seatInfo.uid)) {
                if (roomInfo.isVoiceGameRoom) {
                    val dialogCallback = OperateCallback(baseSeatView, roomInfo, seatInfo)
                    seatOperateView.registerCloseListener(dialogCallback)
                    seatOperateView.showOperate(roomInfo,seatInfo)
                    val dialog = showUserDialogInGameSeat(context, seatOperateView, seatInfo.uid, false)
                    dialogCallback.dialog = dialog
                } else {
                    showUserInfoDialog(context, roomInfo, seatInfo)
                }
            } else {
                JumpUtil.enterUserInfoDetailActivity(context, seatInfo.uid, "")
            }
        }
    }

    class OperateCallback(private val baseSeatView: BaseSeatView, private val roomInfo: VoiceRoomInfo, private val seatInfo: SeatInfo): SeatOperateView.DialogListener {
        var dialog:Dialog? = null

        override fun onClose() {
            dialog?.dismiss()
        }

        override fun operateStandUp() {
            dialog?.let {
                baseSeatView.standUp(roomInfo, seatInfo, it)
            }
        }

        override fun operateForceUp() {
            dialog?.let {
                baseSeatView.forceUp(roomInfo, seatInfo, it)
            }
        }

        override fun operateForbidMic() {
            RoomSenderPresenter.forbidSpeak(roomInfo.rid, seatInfo.seat_num, seatInfo.can_speak)
            dialog?.dismiss()
        }

        override fun operateInvitePeople() {
            JumpUtil.gotoRoomMemberActivityWithSeatNum(
                baseSeatView.context,
                roomInfo.rid,
                VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                seatInfo.seat_num
            )
            dialog?.dismiss()
        }

    }


    private fun showUserInfoDialog(context: Context, roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) {
        val dialog = BaseFullScreenDialog(context, R.style.dialog_style_custom)
        val dialogView = SeatUserDialogView(context)
        if (baseSeatView is ISeatViewTrack) {
            dialogView.setSubSource(baseSeatView.getSubSource())
        }
        dialogView.setAddFriendCallback(baseSeatView.seatAddFriendCallback)
        dialogView.update(roomInfo, seatInfo)
        dialog.setContentView(dialogView)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setOnDismissListener { _ ->
            seatUserDialogView = null
        }
        dialogView.registerCloseListener(object : SeatUserDialogView.DialogListener {
            override fun onClose() {
                dialog.dismiss()
            }

            override fun operateStandUp() {
                baseSeatView.standUp(roomInfo, seatInfo, dialog)
            }

            override fun operateForceUp() {
                baseSeatView.forceUp(roomInfo, seatInfo, dialog)
            }

            override fun operateForbidMic() {
                RoomSenderPresenter.forbidSpeak(roomInfo.rid, seatInfo.seat_num, seatInfo.can_speak)
                dialog.dismiss()
            }

            override fun operateInvitePeople() {
                JumpUtil.gotoRoomMemberActivityWithSeatNum(
                    context,
                    roomInfo.rid,
                    VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE,
                    seatInfo.seat_num
                )
                dialog.dismiss()
            }
        })
        dialog.show()
        this.seatUserDialogView = dialogView
    }

    fun addSeatIntercept(intercept: ISeatIntercept) {
        seatInterceptList.add(intercept)
    }

    fun removeSeatIntercept(intercept: ISeatIntercept) {
        seatInterceptList.remove(intercept)
    }

    fun getSeatInterceptList(): List<ISeatIntercept> {
        return seatInterceptList
    }
}

internal interface IBaseSeatCallback {
    fun getUserInfo(uid: Int, onSuccess: (UserSimpleInfo?, UserSimpleInfo) -> Unit)
}

internal sealed class BaseSeatViewState(
    protected val baseSeatView: BaseSeatView,
    protected val roomInfo: VoiceRoomInfo,
    val seatInfo: SeatInfo
) {
    open fun update(lastState: BaseSeatViewState?) {
        if (seatInfo.isEmpty || (lastState != null && lastState.seatInfo.uid != seatInfo.uid)) {
            baseSeatView.faceView.clearFace()
            baseSeatView.headImg.clearAnimInfo()
        }
    }

    protected fun showMemberInfo(lastSeatInfo: SeatInfo?, seatInfo: SeatInfo) {
        if (baseSeatView.memberLay == null || baseSeatView.memberIv == null) {
            HLog.d(
                "BaseSeatView",
                "does not support show member icon, voice game type={}, seat type={}, seat num={}",
                roomInfo.game_type,
                seatInfo.seatType,
                seatInfo.seat_num
            )
            return
        }
        val memberLevel = seatInfo.getMemberLevel()
        val isFrozen = seatInfo.isMemberFreeze()
        if (lastSeatInfo?.memberLevel == memberLevel && lastSeatInfo.isMemberFreeze == isFrozen) {
            return
        }
        if (memberLevel >= 8) {
            baseSeatView.memberLay!!.visibility = View.VISIBLE
            baseSeatView.memberIv!!.visibility = View.VISIBLE
            val level = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
                .getPrivilegeLevel(memberLevel)
            var url = ""
            if (level != null) {
                url = level.smallIcon
            }
            if (isFrozen) {
                baseSeatView.memberLay!!.setBlackWhiteEnable(true)
                baseSeatView.memberIv!!.setAlpha(0.8f)
            } else {
                baseSeatView.memberLay!!.setBlackWhiteEnable(false)
                baseSeatView.memberIv!!.setAlpha(1f)
            }
            WpImageLoader.load<String>(url, baseSeatView.memberIv)
        }
    }

    class Empty(
        baseSeatView: BaseSeatView,
        roomInfo: VoiceRoomInfo,
        seatInfo: SeatInfo
    ) : BaseSeatViewState(baseSeatView, roomInfo, seatInfo) {
        override fun update(lastState: BaseSeatViewState?) {
            super.update(lastState)
            when (lastState) {
                is Empty -> Unit
                else -> {
                    baseSeatView.userInfoLay.visibility = View.INVISIBLE
                    baseSeatView.nameTv.setVipLevel(0)
                    baseSeatView.nameTv.text = ""
                    baseSeatView.memberLay?.visibility = View.GONE
                    baseSeatView.memberIv?.visibility = View.GONE
                    baseSeatView.emptyIv.visibility = View.VISIBLE
                    baseSeatView.emptyIv.setBackgroundResource(baseSeatView.getEmptySeatRes(roomInfo.isGaming))
                    baseSeatView.showEmpty()
                }
            }
        }
    }

    class RoomOwnerOffline(
        baseSeatView: BaseSeatView,
        roomInfo: VoiceRoomInfo,
        seatInfo: SeatInfo,
        private val seatCallback: IBaseSeatCallback
    ) : BaseSeatViewState(baseSeatView, roomInfo, seatInfo) {
        override fun update(lastState: BaseSeatViewState?) {
            super.update(lastState)
            when (lastState) {
                is RoomOwnerOffline -> Unit
                else -> {
                    baseSeatView.userInfoLay.visibility = View.VISIBLE
                    baseSeatView.emptyIv.visibility = View.VISIBLE
                    baseSeatView.emptyIv.background = null
                    baseSeatView.emptyIv.setImageDrawable(RoomLeaveDrawable())
                }
            }
            seatCallback.getUserInfo(seatInfo.uid) { old, current ->
                baseSeatView.refreshUserInfo(old, current)
            }
            showMemberInfo(lastState?.seatInfo, seatInfo)
        }
    }

    class Sealed(baseSeatView: BaseSeatView, roomInfo: VoiceRoomInfo, seatInfo: SeatInfo) :
        BaseSeatViewState(baseSeatView, roomInfo, seatInfo) {
        override fun update(lastState: BaseSeatViewState?) {
            super.update(lastState)
            when (lastState) {
                is Sealed -> Unit
                else -> {
                    baseSeatView.userInfoLay.visibility = View.INVISIBLE
                    baseSeatView.memberLay?.visibility = View.GONE
                    baseSeatView.memberIv?.visibility = View.GONE
                    baseSeatView.nameTv.setVipLevel(0)
                    baseSeatView.nameTv.text = ""
                    baseSeatView.emptyIv.visibility = View.VISIBLE
                    baseSeatView.emptyIv.setBackgroundResource(baseSeatView.sealedSeatRes)
                }
            }
        }
    }

    class Seated(
        baseSeatView: BaseSeatView,
        roomInfo: VoiceRoomInfo,
        seatInfo: SeatInfo,
        private val seatCallback: IBaseSeatCallback
    ) : BaseSeatViewState(baseSeatView, roomInfo, seatInfo) {
        override fun update(lastState: BaseSeatViewState?) {
            super.update(lastState)
            when (lastState) {
                is Seated -> Unit
                else -> {
                    baseSeatView.userInfoLay.visibility = View.VISIBLE
                    baseSeatView.emptyIv.visibility = View.INVISIBLE
                }
            }
            seatCallback.getUserInfo(seatInfo.uid) { old, current ->
                baseSeatView.refreshUserInfo(old, current)
            }
            showMemberInfo(lastState?.seatInfo, seatInfo)
        }
    }
}