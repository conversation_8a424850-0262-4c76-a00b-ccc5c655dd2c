package com.wepie.wespy.module.common;

import static com.wejoy.weplay.login.WejoyLoginFragment.FROM_TAG;
import static com.wejoy.weplay.login.WejoyPhoneLoginActivity.LAST_SELECT_AREA_CODE;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.activity.result.contract.ActivityResultContract;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.interfaces.SingleCallback;
import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.international.service.GlobalConfigObserver;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.constants.HttpCode;
import com.huiwan.constants.LoginSource;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.IFunctionShieldApi;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.module.webview.PurchaseInfo;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.BlockUser;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.User;
import com.huiwan.user.http.callback.UserCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.helper.GlobalConfigUtils;
import com.wejoy.weplay.login.WejoyLoginConfig;
import com.wejoy.weplay.login.WejoyLoginFragment;
import com.wejoy.weplay.login.WejoyLoginInfo;
import com.wejoy.weplay.module.settings.main.WeJoySettingActivity;
import com.wepie.lib.api.plugins.track.LoginTraceHelper;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.coin.GameCoinPayBottomDialog;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayDialogFactory;
import com.wepie.wespy.helper.seletefriend.FriendChooseDialog;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.SmsSendResult;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.common.jump.UserGameStatusUtil;
import com.wepie.wespy.module.game.room.roomcreate.RoomSecretActivity;
import com.wepie.wespy.module.locationslecet.LocationSelectActivity;
import com.wepie.wespy.module.login.helper.LogoutHelper;
import com.wepie.wespy.module.login.login.UserLoginCallback;
import com.wepie.wespy.module.login.start.StartActivity;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomListService;
import com.wepie.wespy.module.pay.commonapi.InnerPayApi;
import com.wepie.wespy.module.pay.commonapi.JumpPay;
import com.wepie.wespy.module.pay.commonapi.WpPayResult;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.settings.main.FeedbackUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.http.api.LoginApi;
import com.wepie.wespy.net.tcp.packet.GroupPackets;
import com.wepie.wespy.net.tcp.sender.ChatPacketSenderNew;
import com.wepie.wpdd.DeviceInfoUtil;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlinx.coroutines.flow.Flow;

public class HwPluginApiImpl implements HwApi {
    private static final String TAG = "HwPluginApiImpl";

    @Override
    public void gotoVipMainActivity(Context context, String screenName) {
        JumpUtil.gotoVipMainActivity(context, screenName);
    }

    @Override
    public void gotoGiftVipToFriendActivity(Context context, int uid) {
        JumpUtil.gotoGiftToFriendActivity(context, uid);
    }

    @Override
    public void gotoGoodsListActivity(Context context) {
        JumpUtil.gotoGoodsListActivity(context);
    }

    @Override
    public void gotoGoodsListActivity(Context context, boolean subCoinFirst, Map<String, Object> trackParam) {
        JumpPay.showGoods(context, subCoinFirst, trackParam);
    }

    @Override
    public void gotoHorizontalPay(Context context, String scene, int gameType) {
        PublicCoinPayDialogFactory.INSTANCE.createAndShow(context, false, scene, gameType);
    }

    @Override
    public void giftShowBottomCoinNotEnough(Context context, int rid) {
        if (rid > 0) {
            VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
            if (roomInfo != null && roomInfo.rid == rid && roomInfo.isSupportVoiceRoom()) {
                ToastUtil.show(ResUtil.getStr(R.string.coin_not_enough_hint));
                PublicCoinPayBottomDialogView.showBottomDialog(context);
            } else {
                DialogUtil.showPublicCoinNotEnoughDialog();
            }
        } else {
            DialogUtil.showPublicCoinNotEnoughDialog();
        }
    }

    @Override
    public void showCoinNotEnoughDialog(boolean bottomFirst) {
        showCoinNotEnoughDialog(bottomFirst, Collections.emptyMap());
    }

    @Override
    public void showCoinNotEnoughDialog(boolean bottomFirst, Map<String, Object> map) {
        Map<String, Object> data = new ArrayMap<>();
        if (map != null && !map.isEmpty()) {
            data.putAll(map);
            User user = UserService.get().getLoginUser();
            data.put("is_vip", user.isVip());
            data.put("account_coin", user.coin);
        }
        if (bottomFirst) {
            Activity activity = ActivityTaskManager.getInstance().getTopActivity();
            if (activity != null && !activity.isFinishing()) {
                PublicCoinPayBottomDialogView.showBottomDialog(activity, data);
            } else {
                DialogUtil.showPublicCoinNotEnoughDialog(data);
            }
        } else {
            DialogUtil.showPublicCoinNotEnoughDialog(data);
        }
    }

    @Override
    public Flow<Integer> showGameChipExchangeDialog(int gameType, int betLevel, Map<String, Object> trackExt) {
        return showGameChipExchangeDialog(gameType, -1, -1, betLevel, trackExt);
    }

    @Nullable
    @Override
    public Flow<Integer> showGameChipExchangeDialog(int gameType, int gameMode, int mode, int betLevel, Map<String, Object> trackExt) {
        Activity activity = ActivityTaskManager.getInstance().getTopActivity();
        if (activity instanceof FragmentActivity && !activity.isFinishing()) {
            Map<String, Object> trackMap = new HashMap<>();
            if (betLevel > 0 && gameMode > 0 && mode > 0) {
                trackMap.put("bet_level", betLevel);
                trackMap.put("mode_type", TeamInfo.trackModeType(gameType, gameMode));
                trackMap.put("match_mode", TeamInfo.trackMatchMode(gameMode, mode));
            }
            // js接口被调用时已包含了referScreenName和gamdid字段，前提是web那边得传 （CocosApiShowNotEnoughAlert 和 CocosBridgeInterface.ShowChipBuyAlert）
            trackMap.putAll(trackExt);
            return GameCoinPayBottomDialog.show((FragmentActivity) activity, trackMap);
        }
        return null;
    }

    @Override
    public boolean giftShowVoiceBroad(int rid) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (roomInfo != null && roomInfo.rid == rid) {
            return !roomInfo.isLoveHome() && !roomInfo.isAudioMatchRoom();
        }
        return false;
    }

    @Override
    public void showBuyDialog(String json, DataCallback<Integer> callback) {
        try {
            PurchaseInfo info = JsonUtil.fromJson(json, PurchaseInfo.class);
            Activity activity = ActivityTaskManager.getInstance().getTopActivity();
            WespyGoods goods = new WespyGoods();
            goods.goods_id = info.goodsId;
            goods.goods_name = info.goodsName;
            goods.goods_price = String.valueOf(info.goodsPrice);
            goods.apple_product_id = info.appleProductId;
            goods.setGpProductId(info.productId);
            goods.setFirstCharge(info.firstChargePrice ? 1 : 0);
            Map<String, String> extParam = new ArrayMap<>();
            if (info.gameType != 0) {
                extParam.put("game_type", String.valueOf(info.gameType));
                extParam.put("bet_level", String.valueOf(info.betLevel));
                extParam.put("mode", String.valueOf(info.mode));
                extParam.put("game_mode", String.valueOf(info.gameMode));
            }
            IDAuthCheckManager.doTaskOrShowNeedCertificate(activity, AuthApi.SCENE_CHARGE, () -> InnerPayApi.showPay(goods, extParam, activity, new InnerPayApi.PayResultCallback() {
                @Override
                public void onSuccess(WpPayResult payResult) {
                    if (callback != null) callback.onCall(0);
                }

                @Override
                public void onFail() {
                    if (callback != null) callback.onFailed(0, ResUtil.getStr(R.string.cancel));
                }
            }));
        } catch (Exception e) {
            if (callback != null) callback.onFailed(0, e.getMessage());
        }
    }

    @Override
    public void showFriendChooseDialog(Context context, boolean showSelf, int selectionLimit, SingleCallback<Object> callback) {
        FriendChooseDialog.INSTANCE.show(context, showSelf, selectionLimit, userInterfaces -> {
            callback.onCall(userInterfaces);
            return null;
        });
    }

    @Override
    public void handleCustomCommand(String payload) {
        CustomPushHandler.handleCustomPush(payload);
    }

    @Override
    public void gotoUserInfoDetailActivityFromGame(Context context, int uid, int gameType, String trackJsonExt) {
        JumpUtil.enterUserInfoDetailFromCocosGame(context, uid, gameType, trackJsonExt);
    }

    @Override
    public void gotoUserInfoDetailActivity(Context context, int uid, String source) {
        JumpUtil.enterUserInfoDetailActivity(context, uid, source);
    }

    @Override
    public void gotoUserInfoDetailActivity(Context context, int uid, String source, String trackJsonExt) {
        JumpUtil.enterUserInfoDetailActivity(context, uid, source, trackJsonExt);
    }

    @Override
    public void gotoReportGameUserActivity(Context context, int uid, int gameType, int rid) {
        String source = ShenceGameTypeSource.getGameTypeSource(gameType);
        JumpUtil.gotoReportGameUserActivity(context, ReportBuilder.newBuilder()
                .setTargetUid(uid)
                .setRid(rid)
                .setGameType(gameType)
                .setSource(source));
    }

    @Override
    public void gotoReportVoiceRoomUserActivity(Context context, int uid, int gameType, int rid) {
        JumpUtil.gotoReportVoiceRoomUserActivity(context, uid, rid);
    }

    @Override
    public boolean interceptHttpCode(int code, boolean coinUseBottom) {
        if (code == HttpCode.CODE_COIN_NOT_ENOUGH) {
            Activity activity = ActivityTaskManager.getInstance().getTopActivity();
            if (coinUseBottom && activity != null) {
                ToastUtil.show(ResUtil.getStr(R.string.coin_not_enough_hint));
                PublicCoinPayBottomDialogView.showBottomDialog(activity);
            } else {
                DialogUtil.showPublicCoinNotEnoughDialog();
            }
            return true;
        } else if (code == HttpCode.CODE_CHIP_NOT_ENOUGH) {
            Activity activity = ActivityTaskManager.getInstance().getTopActivity();
            if (activity instanceof FragmentActivity) {
                Map<String, Object> trackMap = Collections.emptyMap();
                ApiService.of(HwApi.class).showGameChipExchangeDialog(-1, -1, trackMap);
            } else {
                DialogUtil.showPublicCoinNotEnoughDialog();
            }
        } else if (code == HttpCode.CODE_NEED_EXCHANGE_DOUDOU) {
            DialogUtil.showDouDouNotEnough();
        } else if (code == HttpCode.CODE_GIFT_CARD_NOT_ENOUGH) {
            DialogUtil.showGiftCardNotEnoughDialogInTop();
        } else {
            return false;
        }
        return true;
    }

    @Override
    public void gotoMainActivity(Context context) {
        JumpUtil.gotoMainActivity(context);
    }

    @Override
    public void gotoLoginActivity(Activity activity, boolean fromStartActivity, int from) {
        if (fromStartActivity) {
            if (activity instanceof FragmentActivity) {
                FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                Fragment f = manager.findFragmentByTag(StartActivity.LOGIN_FRAGMENT_TAG);
                if (f instanceof WejoyLoginFragment) {
                    // 从 StartActivity 跳转时，如果
                    // 发现 fragment 已存在，则不进行额外的处理。
                    ((WejoyLoginFragment) f).requestNetConfigs();
                    return;
                }
                WejoyLoginFragment fragment = new WejoyLoginFragment();
                Bundle bundle = new Bundle();
                bundle.putInt(WejoyLoginFragment.FROM_TAG, from);
                fragment.setArguments(bundle);
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.add(android.R.id.content, fragment, StartActivity.LOGIN_FRAGMENT_TAG);
                //添加后先隐藏登录页面,等登录页面那边数据准备好后再显示出来
                transaction.hide(fragment);
                //马上添加到FragmentManager中
                transaction.commitAllowingStateLoss();
            }
        } else {
            Intent intent = new Intent(activity, StartActivity.class);
            intent.putExtra(FROM_TAG, from);
            activity.startActivity(intent);
        }
    }

    @Override
    public void loginFacebook(Activity activity, String token, String userId) {
        LoginApi.facebookLogin(token, userId, new UserLoginCallback(activity, LoginSource.facebook));
    }

    @Override
    public void loginHuawei(Activity activity, String token) {
        LoginApi.huaweiLogin(token, new UserLoginCallback(activity, LoginSource.huawei));
    }

    @Override
    public void loginGoogle(Activity activity, String accessToken, String audience) {
        LoginApi.googleLogin(accessToken, audience, new UserLoginCallback(activity, LoginSource.google));
    }

    @Override
    public void loginTwitter(Activity activity, String accessToken, String accessSecret, int authType) {
        LoginApi.twitterLogin(accessToken, accessSecret, authType, new UserLoginCallback(activity, LoginSource.twitter));
    }

    @Override
    public void sendLoginSms(String phone, int codeType, DataCallback<Integer> callback) {
        String snsType = WejoyLoginInfo.getSnsByCode(codeType);
        com.three.http.callback.DataCallback<SmsSendResult> cb = new com.three.http.callback.DataCallback<SmsSendResult>() {
            @Override
            public void onSuccess(Result<SmsSendResult> result) {
                LoginTraceHelper.trackProcess("1", "get_code", snsType, "");
                callback.onCall(result.data.getLeftSeconds());
            }

            @Override
            public void onFail(int code, String msg) {
                LoginTraceHelper.trackProcess("0", "get_code", snsType, msg);
                callback.onFailed(code, msg);
            }
        };
        Map<String, String> map = new HashMap<>();
        map.put("confirm_region", GlobalConfigManager.getInstance().getRegion());
        LoginApi.overseaSmsSend(phone, codeType, map, cb);
    }

    @Override
    public void sendSms(Activity activity, String phone, int phoneCodeType, Map<String, String> map, Function1<Integer, Unit> onSucc) {
        String snsType = WejoyLoginInfo.getSnsByCode(phoneCodeType);
        com.three.http.callback.LifeDataCallback<SmsSendResult> cb = new com.three.http.callback.LifeDataCallback<>(ContextUtil.getLife(activity)) {
            @Override
            public void onSuccess(Result<SmsSendResult> result) {
                HLog.d(TAG, HLog.USR, "sendSms onSuccess!  code={}", result.code);
                if (result.code == HttpCode.GLOBAL_SERVER_CONFIG_ERROR) {
                    handleGlobalServerError(activity, phone, phoneCodeType, result.response, onSucc);
                } else {
                    onSucc.invoke(result.data.getLeftSeconds());
                }
                LoginTraceHelper.trackProcess("1", "get_code", snsType, "");
            }

            @Override
            public void onFail(int code, String msg) {
                LoginTraceHelper.trackProcess("0", "get_code", snsType, msg);
                ToastUtil.show(msg);
            }

            @Override
            public void onGlobalConfigException(Result result) {
                LoginTraceHelper.trackProcess("0", "get_code", snsType, "onGlobalConfigChange");
                handleGlobalServerError(activity, phone, phoneCodeType, result.response, onSucc);
            }
        };
        LoginApi.overseaSmsSend(phone, phoneCodeType, map, cb);
    }

    private void handleGlobalServerError(Activity activity, String phone, int phoneCodeType, Object result, Function1<Integer, Unit> onSucc) {
        if (null != result) {
            GlobalConfigObserver observer = new GlobalConfigObserver() {
                @Override
                public void forceAllUpdate(boolean success, String msg) {
                    if (success) {
                        Map<String, String> map = new HashMap<>();
                        map.put("confirm_region", GlobalConfigManager.getInstance().getRegion());
                        sendSms(activity, phone, phoneCodeType, map, onSucc);
                    } else {
                        globalConfigUpdateFail(activity, phone, phoneCodeType, result, onSucc);
                    }
                    ApiService.of(HwApi.class).hideProgressDialog(activity);
                    HLog.d(TAG, "forceAllUpdate! success={}, msg={}", success, msg);
                }
            };
            GlobalConfigManager.getInstance().handleConfigException(result.toString(), observer);
        }
        HLog.d(TAG, HLog.USR, "handleGlobalServerError!");
    }

    private void globalConfigUpdateFail(Activity activity, String phone, int phoneCodeType, Object result, Function1<Integer, Unit> onSucc) {
        GlobalConfigUtils.UserClickBack clickBack = isRetry -> {
            if (isRetry) {
                handleGlobalServerError(activity, phone, phoneCodeType, result, onSucc);
            }
        };
        GlobalConfigUtils.showDialogWhenFail(clickBack);
    }

    public void smsLogin(Activity activity, String phone, LoginSource loginSource, String vCode, DataCallback<Object> callback) {
        final UserLoginCallback userLoginCallback = new UserLoginCallback(activity, loginSource);
        UserCallback cb = new UserCallback() {
            @Override
            public void onSuccess(User user, List<FriendInfo> friendList, List<BlockUser> blockUserList, List<User> userList, boolean newUser, boolean needMobile, boolean hasPushId) {
                userLoginCallback.onSuccess(user, friendList, blockUserList, userList, newUser, needMobile, hasPushId);
                callback.onCall(user);
            }

            @Override
            public void onFailure(int code, String description) {
                callback.onFailed(code, description);
                userLoginCallback.onFailure(code, description);
            }

            @Override
            public void onStart() {
                userLoginCallback.onStart();
            }

            @Override
            public void onFinish() {
                userLoginCallback.onFinish();
            }
        };
        LoginApi.overseaSmsLogin(phone, vCode, loginSource, cb);
    }

    @Override
    public void selectLocal(Activity activity, int requestCode) {
        JumpUtil.gotoLocationSelectActivity(activity, requestCode);
    }

    @Override
    public void gotoCustomServiceActivity(Context context) {
        FeedbackUtil.gotoFeedback(context);
    }

    @Override
    public void getRecommendGroupByGameTypeReq(int gameType, DataCallback<List<?>> callback) {
        ChatPacketSenderNew.getInstance().getRecommendGroupByGameTypeReq(gameType, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                List<GroupPackets.SimpleGroupInfo> list = ((GroupPackets.RecommendGroupByGameTypeRsp) head.message).getListList();
                callback.onCall(list);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                callback.onCall(Collections.emptyList());
            }
        });
    }

    @Override
    public Class<? extends Activity> getLocationSelectActivityClass() {
        return LocationSelectActivity.class;
    }

    @Override
    public void gotoPropose2Activity(Context context, int selectUid) {
        JumpUtil.gotoPropose2Activity(context, selectUid);
    }

    @Override
    public void gotoLoverHomeActivity(Context context, int targetId) {
        JumpUtil.gotoLoverHomeActivity(context, targetId);
    }

    @Override
    public void gotoSuperiorRoomDetailActivity(Context context, String screenName) {
        JumpUtil.gotoSuperiorRoomDetailActivity(context);
        ShenceEvent.appClick(screenName, TrackButtonName.CREATE_ADVANCED_ROOM, new HashMap<>());
    }

    @Override
    public void gotoVoiceRoomActivity(Context context, int roomId, int gameType, String source) {
        EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(
                        context, roomId, gameType)
                .setSource(source);
        JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
    }

    @Override
    public void gotoFamilyMainActivity(Context context, String screenName) {
        ApiService.of(TrackApi.class).appClick(screenName, TrackSource.FAMILY);
        JumpUtil.gotoFamilyMainActivity(context);
    }

    @Override
    public void gotoFamilyDetailActivity(Context mContext, int familyId, String source) {
        JumpUtil.gotoFamilyDetailActivity(mContext, familyId, source);
    }

    @Override
    public void followRoomWithRefreshFollowRoom(int rid, boolean isFollow, DataCallback<Integer> callback) {
        IReference<DataCallback<Integer>> reference = IReferenceKt.wrap(callback);
        VoiceRoomListService.getInstance().followRoomWithRefreshFollowRoom(rid, isFollow, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                IReferenceKt.run(reference, callback -> callback.onCall(rid));
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                IReferenceKt.run(reference, callback -> callback.onFailed(head.code, head.desc));
            }
        });
    }

    @Override
    public void gotoOtherPager(Context context, String schemeText, String source) {
        JumpCommon.gotoOtherPager(context, schemeText, source, null);
    }

    @Override
    public void gotoSetting(Context mContext) {
        Intent intent = new Intent(mContext, WeJoySettingActivity.class);
        mContext.startActivity(intent);
    }

    @Override
    public String getSimCountry() {
        String simArea = PrefUtil.getInstance().getString(PrefUtil.SIM_AREA, "");
        if (simArea == null || simArea.isEmpty()) {
            simArea = DeviceInfoUtil.getSimCountry();
        }
        return simArea;
    }

    @Override
    public String getSimOperator() {
        return DeviceInfoUtil.getSimOperator();
    }

    @Override
    public void getLoginConfig(DataCallback<Object> callback) {
        ILife life = null;
        if (callback instanceof LifeApiDataCallback) {
            life = ((LifeApiDataCallback<Object>) callback).getLife();
        }
        LoginApi.getLoginConfig(new com.three.http.callback.LifeDataCallback<WejoyLoginConfig>(life) {
            @Override
            public void onSuccess(Result<WejoyLoginConfig> result) {
                if (callback != null) {
                    callback.onCall(result.data);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                if (callback != null) {
                    callback.onFailed(code, msg);
                }
            }
        });
    }

    @Override
    public void loginTourist(Activity activity) {
        LoginApi.touristLogin(new UserLoginCallback(activity, LoginSource.guest));
    }

    @Override
    public void showProgressDialog(Activity activity, String content, boolean cancel) {
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).showProgressDialog(content, cancel);
        }
    }

    @Override
    public void hideProgressDialog(Activity activity) {
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).hideProgressDialog();
        }
    }

    /**
     * 退出登录后续操作
     */
    @Override
    public void logoutHelperOnLogout() {
        LogoutHelper.onLogout();
    }

    @Override
    public void logoutRestart(Activity activity) {
        Intent mStartActivity = new Intent(activity, StartActivity.class);
        int mPendingIntentId = 123456;
        int flags = PendingIntent.FLAG_CANCEL_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent mPendingIntent = PendingIntent.getActivity(activity, mPendingIntentId, mStartActivity, flags);
        AlarmManager mgr = (AlarmManager) activity.getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 100, mPendingIntent);
        ActivityTaskManager.getInstance().finishAllActivities();
        System.exit(0);
    }

    @Override
    public String getHuaweiLoginUrl() {
        return UrlConfig.URL_LOGIN_HUAWEI;
    }

    @Override
    public boolean checkGameStatus(Context context, int type, int gameType, int targetRid) {
        return UserGameStatusUtil.checkUserStatusFromQuickStart(context, type, gameType, targetRid);
    }

    @Override
    public boolean isDebug() {
        return UrlConfig.isDebug();
    }

    @Override
    public int getRecordVocCount() {
        return AbTestManager.getInstance().getAudioSec();
    }

    @Override
    public int getBackupAddrCount() {
        return AbTestManager.getInstance().getBackupAddrCount();
    }

    @Override
    public int getBackupAddrSecond() {
        return AbTestManager.getInstance().getBackupAddrSecond();
    }

    @Override
    public void showGoods(Context context) {
        JumpPay.showGoods(context);
    }

    @Override
    public int getRecordVocValidSec() {
        return AbTestManager.getInstance().getAudioValidSec();
    }

    @Override
    public boolean isInVoiceRoom() {
        return VoiceRoomService.getInstance().isInVoiceRoom();
    }

    @Override
    public void exitLocalVoiceRoom() {
        VoiceRoomService.getInstance().exitLocal();
    }

    @Override
    public String getSimAreaCode() {
        String areaCode = ApiService.of(IFunctionShieldApi.class).getAreaCode();

        String lastSelectAreaCode = PrefUtil.getInstance().getString(LAST_SELECT_AREA_CODE, "");
        if (!TextUtil.isEmpty(lastSelectAreaCode)) {
            return lastSelectAreaCode;
        }

        String simCountryCode = ApiService.of(HwApi.class).getSimCountry();
        List<AreaConfig.Area> areaList = ConfigHelper.getInstance().getAreaConfig().getConfig().getAreaList();
        if (TextUtil.isEmpty(simCountryCode) || areaList == null) {
            return areaCode;
        }

        for (int i = 0; i < areaList.size(); i++) {
            AreaConfig.Area area = areaList.get(i);
            if (area != null && !TextUtils.isEmpty(area.area)
                    && simCountryCode.equalsIgnoreCase(area.area)) {
                areaCode = area.areaCode;
                break;
            }
        }

        return areaCode;
    }

    @Override
    public ActivityResultContract<Void, String> genPwdActivityResultContract() {
        return RoomSecretActivity.genActivityResultContract();
    }
}
