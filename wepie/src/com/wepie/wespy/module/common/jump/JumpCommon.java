package com.wepie.wespy.module.common.jump;

import android.app.Activity;
import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StyleSpan;
import android.util.Log;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.game.chip.GameLotteryDialog;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.FriendInfo;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.jackaroo.create.JackarooRoomCreateActivity;
import com.wejoy.jackaroo.vip.JackarooVipRoomListActivity;
import com.wejoy.weplay.module.settings.main.WeJoyAccountManagerSettingActivity;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.config.BaseConfig;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.ChargeUserDataInfo;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.family.FamilyVoiceRoomRsp;
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.other.JumpMainSpecificGamePage;
import com.wepie.wespy.module.activity.ActivityUtil;
import com.wepie.wespy.module.activity.coupon.CouponUtil;
import com.wepie.wespy.module.chat.invitegame.Cocos243Helper;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.family.dialogs.FamilyCallDialog;
import com.wepie.wespy.module.family.dialogs.FamilyDonateDialog;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.FirstChargeView;
import com.wepie.wespy.module.game.game.activity.TextJumpExtra;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;
import com.wepie.wespy.module.main.HomeConst;
import com.wepie.wespy.module.marry.regret.RegretDetailActivity;
import com.wepie.wespy.module.match.AudioMatchActivity;
import com.wepie.wespy.module.msgroaming.MsgRoamingUtil;
import com.wepie.wespy.module.pay.commonapi.JumpPay;
import com.wepie.wespy.module.settings.main.FeedbackUtil;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.wepie.wespy.net.http.api.FamilyApi;
import com.wepie.wespy.net.http.api.FirstChargeApi;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

// Created by bigwen on 2018/6/29.
//用做运营需求，应用外push，活动页面的跳转等等
public class JumpCommon {

    private static final Pattern P = Pattern.compile("wespydeeplink://+[^/]+/");

    private static final String TAG = "JumpCommon";

    public static final String DEEP_LINK = "wespydeeplink://";

    public static final String LITTLE_GAME = "little_game";//小游戏 （参数：game_type:int）
    private static final String VOICE_MATCH = "voice_match";//脱单神器
    private static final String CHARM_RANK = "charm_rank";//人气榜
    private static final String QUALIFYING_HOME = "qualifying";//排位赛首页
    private static final String RANK_PAGE = "rank_page";//排行榜
    private static final String DISCOVER_SQUARE = "discover_square";//广场
    private static final String DISCOVER_TOPIC = "discover_topic";//广场话题
    private static final String DISCOVER_CIRCLE = "discover_circle";//玩友圈
    private static final String VOICE_ROOM_LIST = "voice_room_list";//语音房列表
    private static final String JUDGE_CIRCLE = "judge_circle";//法官卧友圈
    private static final String COUPON = "coupon";//优惠码
    private static final String HTTP = "http";//web跳转
    private static final String SHOP = "shop";//道具商店，每个tab(参数：index:int)
    private static final String PROP_DETAIL = "prop_detail";//已废弃 道具详情，每个戒指等等 （参数：prop_id:int）
    private static final String DRAW_BOARD = "draw_board";//画板商店

    private static final String ACTIVITY_CENTER_PAGE = "activity_center_page";//跳转到活动中心页面
    private static final String PAY = "pay";//充值页面
    public static final String VOICE_ROOM = "voice_room";//进某个语音房
    public static final String CREATE_VOICE_ROOM = "create_voice_room";// 创建语音房
    public static final String CREATE_ADVANCE_VOICE_ROOM = "create_advance_voice_room";// 创建高级语音房
    private static final String USER_INFO = "user_info";//进某个用户个人资料页
    public static final String SOCIAL_GAME = "social_game";
    public static final String HOT_VOICE_ROOM = "hot_voice_room";
    private static final String CHURCH = "church";//教堂
    private static final String MY_PACKET = "my_packet";//我的背包
    private static final String ENTER_ROOM = "enter_room";//进入新的桌游房间
    private static final String TAB_MAIN = "tab_main";
    private static final String TAB_MSG = "tab_msg";
    private static final String TAB_VOICEROOM = "tab_voiceroom";
    private static final String TAB_DISCOVER = "tab_discover";
    private static final String TAB_ME = "tab_me";
    private static final String LOVER_ROOM = "lover_room";
    private static final String NEARBY = "nearby";
    private static final String SHARE_INVITE_FRIEND = "share_invite_friend";
    private static final String MARRY_INVITATION = "marry_invitation";
    private static final String MARRY_PROPOSE = "marry_propose";
    private static final String MARRY_WEDDING = "marry_wedding";
    private static final String MARRY_PROPOSE_DETAIL = "marry_propose_detail";
    private static final String MARRY_DIVORCE = "marry_divorce";
    private static final String BROADCAST = "broadcast";
    private static final String HOME_TAB = "home_tab";
    private static final String DISCOVER_POST_DETAIL = "discover_post_detail";
    private static final String VIP_VIEW = "vip_view";
    private static final String FAMILY_MAIN = "family_main";
    private static final String FAMILY_DETAIL = "family_detail";
    private static final String FAMILY_STEAL = "family_steal";
    private static final String FAMILY_DONATE = "family_donate";
    private static final String FAMILY_CALL = "family_call";
    private static final String FAMILY_GROUP = "family_group";
    private static final String FAMILY_WHEEL = "family_wheel";
    private static final String FAMILY_VOICE_ROOM = "family_voice_room";
    private static final String LOOK_ME = "look_me";
    private static final String BATTLE_PASS = "battle_pass"; //游玩卡
    private static final String MUSIC_HUM_TO_SING = "musichum_to_sing";
    private static final String FEED_BACK = "feedback";
    private static final String TEAM_MATE_MATCH = "teammate_match";
    private static final String MSG_SYNC = "msg_sync"; // 消息漫游
    private static final String AUDIO_MATCH = "teammate_voice_match";
    private static final String MENTOR_SHIP_FAMOUS_TEACHER = "mentorship_famous_teacher";
    private static final String MENTOR_SHIP_MAIN_PAGE = "mentorship_main_page";
    public static final String AVATAR_STORE = "avatar_show";
    private static final String BOARD_GAME_LIST = "boardgame_list";
    private static final String GIFT_PACKAGE = "gift_package";
    private static final String ACCOUNT_MANAGE = "account_manage";
    private static final String CONTRIBUTIONS = "contributions";
    private static final String VOICE_ROOM_RANDOM = "voice_room_random";
    private static final String EDIT_SITUATION_PUZZLE = "edit_situation_puzzle";
    private static final String JUMP_CHAMBERKILL = "jump_chamberkill";
    private static final String JUMP_APPLY_SINGER = "apply_singer";
    private static final String JUMP_MEDAL_PAGE = "medal_page";
    private static final String LANG_MANAGE = "lang_manage"; //切换语言
    private static final String JUMP_HOME_TASK = "home_task";
    private static final String JUMP_SEE_RECORD = "see_record";
    private static final String JACKAROO_VIP_ROOM_LIST = "jackaroo_vip_room_list";
    //跳游戏转创房页
    private static final String JACKAROO_CREATE_GAME_ROOM = "create_game_room";

    // 直接跳转到 cocos 内
    public static final String COCOS = "cocos";

    private static final String COMPETITION_CREATE = "competition_create";
    private static final String COMPETITION_LIST = "competition_list";
    private static final String COMPETITION_DETAIL = "competition_detail";

    private static final String CHIP_LOTTERY = "chip_lottery";
    private static final String REGRET_DIVORCE_DETAIL = "marry_resumption_info";

    public static void gotoOtherPager(final Context mContext, String schemeText, String source, JumpCallback callback) {
        gotoOtherPager(mContext, schemeText, source, "", new TextJumpExtra(), callback);
    }

    public static void gotoOtherPager(final Context mContext, String schemeText, String source, String scene, JumpCallback callback) {
        gotoOtherPager(mContext, schemeText, source, scene, new TextJumpExtra(), callback);
    }

    public static void gotoOtherPager(final Context mContext, String schemeText, String source, String scene, TextJumpExtra jumpExtra, JumpCallback callback) {
        try {
            HLog.d(TAG, HLog.USR, "goto other page, {}, {}, {}", schemeText, source, callback);
            if (TextUtils.isEmpty(schemeText)) {
                HLog.d(TAG, "gotoOtherPager, deepLink empty");
                if (callback != null) callback.onFail(ResUtil.getStr(R.string.protocol_link_empty));
                return;
            }

            if (!schemeText.startsWith(DEEP_LINK)) {
                HLog.d(TAG, "gotoOtherPager, deepLink type error: " + DEEP_LINK);
                if (callback != null)
                    callback.onFail(ResUtil.getStr(R.string.not_deeplink_protocol_link));
                return;
            }

            //去掉消息最后的最后一个/
            if (schemeText.endsWith("/")) {
                schemeText = schemeText.substring(0, schemeText.length() - 1);
            }

            Uri uri = Uri.parse(schemeText);
            String toast = "";

            for (String key : uri.getQueryParameterNames()) {
                HLog.i(TAG, "gotoOtherPager: " + key + " " + uri.getQueryParameter(key));
                String value = uri.getQueryParameter(key);
                if ("toast".equals(key)) {
                    toast = value;
                }
            }

            String scheme = uri.getAuthority();

            if (TextUtils.isEmpty(scheme)) return;
            if (LITTLE_GAME.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                JumpRoomUtil.jumpToLittleGame(gameType, mContext, scene);
            } else if (VOICE_MATCH.equals(scheme)) {
                ToastUtil.show(R.string.feature_has_been_removed);
            } else if (CHARM_RANK.equals(scheme)) {
                JumpUtil.gotoRankActivity(mContext);
            } else if (QUALIFYING_HOME.equals(scheme)) {
                JumpUtil.gotoQualifyingActivity(mContext);
            } else if (DISCOVER_SQUARE.equals(scheme)) {
                JumpUtil.gotoDiscoverCircleActivity(mContext, true);
            } else if (DISCOVER_TOPIC.equals(scheme)) {
                int id = getInt(uri, "topic_id");
                JumpUtil.gotoSquareTopicActivity(mContext, id);
            } else if (VOICE_ROOM_LIST.equals(scheme)) {
                JumpUtil.gotoMainActivity(mContext);
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_FRIEND, 0);
            } else if (JUDGE_CIRCLE.equals(scheme)) {
                JumpUtil.gotoUserCircleActivity(mContext, BaseConfig.JUDGE_UID);
            } else if (COUPON.equals(scheme)) {
                CouponUtil.getCoupon(mContext);
            } else if (HTTP.equals(scheme)) {
                String url = uri.getQueryParameter("url");
                url = Uri.parse(url).buildUpon().appendQueryParameter("scene", scene).toString();
                Uri newUri = Uri.parse(url);
                String isAct = newUri.getQueryParameter("is_act");
                int actId = ActivityUtil.getActId(newUri);
                String isDialog = newUri.getQueryParameter("is_half_window");
                if ("1".equals(isAct)) {
                    ShenceEvent.clickActivity(actId, 0, TrackScreenName.DEEP_LINK);
                    if (actId != 0) {
                        JumpUtil.gotoActivityTabActivity(mContext, url);
                    } else {
                        WebApi.WebViewConfig config = new WebApi.WebViewConfig(url);
                        config.setFromDeeplink(true);
                        ApiService.of(WebApi.class).gotoWebActivity(mContext, config);
                    }

                } else if ("1".equals(isDialog)) {
                    int height = getInt(newUri, "height");
                    int alpha = getInt(newUri, "alpha");
                    ApiService.of(WebApi.class).showWebDialog(mContext, url, height, alpha);
                } else {
                    WebApi.WebViewConfig config = new WebApi.WebViewConfig(url);
                    String head = newUri.getQueryParameter("head");
                    if ("0".equals(head)) {
                        config.setShowTitleBar(false);
                    }
                    config.setFromDeeplink(true);
                    ApiService.of(WebApi.class).gotoWebActivity(mContext, config);
                }

            } else if (SHOP.equals(scheme)) {
                int index = getInt(uri, "index");
                int itemType = getInt(uri, "item_type");
                JumpUtil.gotoShopActivity(mContext, itemType, index, false);
            } else if (PROP_DETAIL.equals(scheme) || DRAW_BOARD.equals(scheme)) {
                JumpUtil.gotoShopActivity(mContext, false);
            } else if (PAY.equals(scheme)) {
                boolean chipFirst = false;
                try {
                    String target = uri.getQueryParameter("target");
                    chipFirst = "chip".equals(target); // diamond/chip. 默认钻石。
                } catch (Exception e) {
                    HLog.e(TAG, HLog.USR, "error parse arg {}", e);
                }
                HashMap<String, Object> trackParam = new HashMap<>();
                int gameid = getInt(uri, "gameid", -1);
                int betLevel = getInt(uri, "bet_level", -1);
                int gameMode = getInt(uri, "game_mode", -1);
                int gameType = getInt(uri, "game_type", -1);
                int mode = getInt(uri, "mode", -1);
                if (gameid != -1) {
                    trackParam.put("gameid", gameid);
                }
                if (betLevel != -1) {
                    trackParam.put("bet_level", betLevel);
                }
                if (gameType != -1 && gameMode != -1) {
                    trackParam.put("mode_type", TeamInfo.trackModeType(gameType, gameMode));
                }
                if (gameMode != -1 && mode != -1) {
                    trackParam.put("match_mode", TeamInfo.trackMatchMode(gameMode, mode));
                }
                trackParam.put("refer_screen_name", source);
                JumpPay.showGoods(mContext, chipFirst, trackParam);
            } else if (CREATE_VOICE_ROOM.equals(scheme)) {
                JumpUtil.gotoCreateVoiceRoomActivity(mContext);
            } else if (CREATE_ADVANCE_VOICE_ROOM.equals(scheme)) {
                JumpUtil.gotoCreateAdvanceVoiceRoomActivity(mContext);
            } else if (VOICE_ROOM.equals(scheme)) {
                int rid = getInt(uri, "rid");
                if (rid > 0) {
                    int gameType = getInt(uri, "game_type");
                    String msgSource = uri.getQueryParameter("source");
                    msgSource = TextUtils.isEmpty(msgSource) ? source : msgSource;
                    EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, rid, gameType).setSource(msgSource);
                    JumpRoomUtil.getInstance().searchRoom(enterRoomInfo, JumpRoomUtil.TMP_ROOM_SCENE);
                } else {
                    HLog.e(TAG, HLog.USR, "error jump voice room: {}", uri);
                }

            } else if (USER_INFO.equals(scheme)) {
                int uid = getInt(uri, "uid");
                if (jumpExtra.getGid() > 0) {
                    JumpUtil.enterUserInfoDetailActivityFromGroup(mContext, uid, jumpExtra.getGid());
                } else {
                    JumpUtil.enterUserInfoDetailActivity(mContext, uid, TrackSource.LINK);
                }
            } else if (DISCOVER_CIRCLE.equals(scheme)) {
                JumpUtil.gotoDiscoverCircleActivityForceCircle(mContext);
            } else if (SOCIAL_GAME.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                if (gameType > 0 && mContext instanceof Activity) {
                    jumpSocialGame(gameType, (Activity) mContext, scene);
                }
            } else if (HOT_VOICE_ROOM.equals(scheme)) {
                jumpToHotVoiceRoom(mContext, source);
            } else if (CHURCH.equals(scheme)) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_MARRY,
                        () -> JumpUtil.gotoChurch2Activity(mContext));
            } else if (MY_PACKET.equals(scheme)) {
                JumpUtil.gotoMyPropActivity(mContext);
            } else if (ENTER_ROOM.equals(scheme)) {
                int rid = getInt(uri, "rid");
                if (rid > 0) {
                    int gameType = getInt(uri, "game_type");
                    String msgSource = uri.getQueryParameter("source");
                    msgSource = TextUtils.isEmpty(msgSource) ? source : msgSource;
                    EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, rid, gameType).setSource(msgSource);
                    JumpRoomUtil.getInstance().searchRoom(enterRoomInfo, JumpRoomUtil.BOARD_GAME_SCENE);
                } else {
                    HLog.e(TAG, HLog.CLR, "enter_room error {}", uri);
                }
            } else if (TAB_MAIN.equals(scheme)) {
                int gameType = getInt(uri, "jump_game_type");
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_GMAE, 0);
                JumpUtil.gotoMainActivityClickGameItem(mContext, gameType);
            } else if (TAB_MSG.equals(scheme)) {
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_MESSAGE, 0);
                JumpUtil.gotoMainActivity(mContext);
            } else if (TAB_VOICEROOM.equals(scheme)) {
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_FRIEND, 0);
                JumpUtil.gotoMainActivity(mContext);
            } else if (TAB_DISCOVER.equals(scheme)) {
//                EventDispatcher.postChangeTabEvent(HomeConst.TAB_DISCOVER, 0);
                JumpUtil.gotoMainActivity(mContext);
            } else if (TAB_ME.equals(scheme)) {
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_ME, 0);
                JumpUtil.gotoMainActivity(mContext);
            } else if (LOVER_ROOM.equals(scheme)) {
                JumpUtil.gotoLoverHomeActivity(mContext, LoginHelper.getLoginUid());
            } else if (SHARE_INVITE_FRIEND.equals(scheme)) {
                JumpUtil.gotoInviteFriendEarnCoin(mContext);
            } else if (MARRY_INVITATION.equals(scheme)) {
                int uid = getInt(uri, "target_uid");
                JumpUtil.gotoInvitationTemplateActivity(mContext, uid);
            } else if (MARRY_PROPOSE.equals(scheme)) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_MARRY, () -> JumpUtil.gotoPropose2Activity(mContext, 0));
            } else if (MARRY_PROPOSE_DETAIL.equals(scheme)) {
                int proposeId = getInt(uri, "propose_id");
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_MARRY, () -> {
                    JumpUtil.gotoProposeMsgDetailActivity(mContext, proposeId);
                });
            } else if (MARRY_WEDDING.equals(scheme)) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_MARRY, () -> JumpUtil.gotoWedding2Activity(mContext));
            } else if (MARRY_DIVORCE.equals(scheme)) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_MARRY, () -> JumpUtil.gotoDivorces2Activity(mContext));
            } else if (BROADCAST.equals(scheme)) {
                int uid = getInt(uri, "uid");
                if (uid == LoginHelper.getLoginUid()) {
                    JumpUtil.gotoBroadSendActivity(mContext, 0);
                } else {
                    DialogBuild.newBuilder(mContext).setSingleBtn(true).setTitle(null).setContent(R.string.not_your_message).setSureTx(ResUtil.getStr(R.string.sure)).setCanCancel(true).setDialogCallback(null).show();
                }
            } else if (HOME_TAB.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                int index = getInt(uri, "index");
                EventDispatcher.postChangeTabEvent(HomeConst.TAB_GMAE, index + 10, -1, new JumpMainSpecificGamePage(gameType));
                JumpUtil.gotoMainActivity(mContext);
            } else if (DISCOVER_POST_DETAIL.equals(scheme)) {
                int postId = getInt(uri, "post_id");
                int uid = getInt(uri, "uid");
                JumpUtil.gotoPostDetailActivityDeepLink(mContext, postId, uid);
            } else if (VIP_VIEW.equals(scheme)) {
                int hideDialogValue = getInt(uri, "hide_dialog");
                JumpUtil.gotoVipMainActivity(mContext, source, hideDialogValue == 1, false);
            } else if (FAMILY_MAIN.equals(scheme)) {
                int index = getInt(uri, "index");
                JumpUtil.gotoFamilyMainActivity(mContext, index);
            } else if (FAMILY_DETAIL.equals(scheme)) {
                int familyId = getInt(uri, "family_id");
                JumpUtil.gotoFamilyDetailActivity(mContext, familyId, source);
            } else if (FAMILY_STEAL.equals(scheme)) {
                JumpUtil.gotoFamilyBoxStealActivity(mContext);
            } else if (FAMILY_DONATE.equals(scheme)) {
                FamilyDonateDialog.showDonateDialog(mContext, -1, -1, -1, null);
            } else if (FAMILY_CALL.equals(scheme)) {
                FamilyCallDialog.showCallDialog(mContext, -1, null);
            } else if (FAMILY_GROUP.equals(scheme)) {
                FamilyMainInfo familyMainInfo = FamilyManager.getInstance().getSelfFamilyInfo();
                if (familyMainInfo.isHasFamily()) {
                    JumpUtil.gotoGroupChatActivity(mContext, familyMainInfo.getFamily().getGroupChatId());
                } else {
                    ToastUtil.show(R.string.do_not_hava_family);
                }
            } else if (FAMILY_WHEEL.equals(scheme)) {
                JumpUtil.gotoFamilyLotteryActivity(mContext);
            } else if (FAMILY_VOICE_ROOM.equals(scheme)) {
                if (UserGameStatusUtil.checkInCoGamer()) {
                    ToastUtil.show(R.string.matching_quit_retry);
                    return;
                }
                FamilyApi.getVoiceRoomId(0, new LifeDataCallback<>(ContextUtil.getLife(mContext)) {
                    @Override
                    public void onSuccess(Result<FamilyVoiceRoomRsp> result) {
                        if (result.data.rid > 0) {
                            EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, result.data.rid, RoomInfo.GAME_TYPE_FAMILY_ROOM).setSource(TrackSource.FAMILY);
                            JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
                        } else {
                            RoomViewLogic.createFamilyRoom(new RoomCallback(ContextUtil.getLife(mContext)) {
                                @Override
                                public void onSuccess(int rid) {
                                    EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, rid, RoomInfo.GAME_TYPE_FAMILY_ROOM).setSource(TrackSource.FAMILY);
                                    JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
                                }

                                @Override
                                public void onFail(String msg) {
                                    ToastUtil.show(msg);
                                }
                            });
                        }
                    }

                    @Override
                    public void onFail(int i, String s) {
                        ToastUtil.show(s);
                    }
                });
            } else if (LOOK_ME.equals(scheme)) {
                JumpUtil.gotoLookMeActivity(mContext, 0);
            } else if (BATTLE_PASS.equals(scheme)) {
                int index = getInt(uri, "index");
                JumpUtil.gotoBattlePassFlutterPage(mContext, index);
            } else if (FEED_BACK.equals(scheme)) {
                FeedbackUtil.gotoFeedback(mContext);
            } else if (TEAM_MATE_MATCH.equals(scheme)) {
                int type = getInt(uri, "type");
                JumpUtil.checkGotoMatchGameMateActivity(mContext, type, toast);
            } else if (MSG_SYNC.equals(scheme)) {
                if (LoginHelper.getVipLevel() > 0) {
                    MsgRoamingUtil.jumpMsgRoamingMain(mContext);
                } else {
                    ToastUtil.show(R.string.vip_no_privilege_to_become_vip);
                }
            } else if (AUDIO_MATCH.equals(scheme)) {
                RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_AUDIO_MATCH);
                JumpUtil.checkGotoMatchGameMateActivity(mContext, AudioMatchActivity.TYPE_GAME_AUDIO_MATCH, "");
            } else if (BOARD_GAME_LIST.equals(scheme)) {
                JumpUtil.gotoSocialGameRoomActivity(mContext);
            } else if (GIFT_PACKAGE.equals(scheme)) {
                int type = getInt(uri, "type");
                FirstChargeApi.getChargeUserDataInfo(LoginHelper.getLoginUid(), new LifeDataCallback<>(ContextUtil.getLife(mContext)) {
                    @Override
                    public void onSuccess(Result<ChargeUserDataInfo> result) {
                        FirstChargeView.showDialog(mContext, result.data, type, FirstChargeConfig.TRACK_SOURCE_CLICK_OPEN, FirstChargeConfig.SHOW_SCENE_DEEP_LINK, false);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
            } else if (ACCOUNT_MANAGE.equals(scheme)) {
                WeJoyAccountManagerSettingActivity.go(mContext);
            } else if (RANK_PAGE.equals(scheme)) {
                int index = getInt(uri, "index");
                JumpUtil.gotoRankActivity(mContext, index);
            } else if (REGRET_DIVORCE_DETAIL.equals(scheme)) {
                int reqId = getInt(uri, "request_id");
                RegretDetailActivity.go(mContext, reqId);
            } else if (ACTIVITY_CENTER_PAGE.equals(scheme)) {
                JumpUtil.goEventsActivity(mContext);
            } else if (VOICE_ROOM_RANDOM.equals(scheme)) {
                String idListStr = uri.getQueryParameter("rid_arr");
                if (TextUtils.isEmpty(idListStr)) {
                    ToastUtil.debugShow("Error parameter " + schemeText);
                } else {
                    try {
                        String[] ridStrArr = idListStr.split("_");
                        if (ridStrArr.length > 0) {
                            String ridStr = ridStrArr[(int) (Math.random() * ridStrArr.length)];
                            int gameType = getInt(uri, "game_type");
                            if (gameType <= 0) {
                                EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildSearchRoom(mContext, ridStr).setSource(TrackSource.ROOM_CONFIG);
                                JumpRoomUtil.getInstance().searchRoom(enterRoomInfo, JumpRoomUtil.TMP_ROOM_SCENE);
                            } else {
                                int temprid = (int) IDRegionUtil.INSTANCE.getOriginIdByGameType(ridStr, gameType);
                                EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, temprid, gameType).setSource(TrackSource.ROOM_CONFIG);
                                JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
                            }
                        }
                    } catch (Exception e) {
                        HLog.d(TAG, "jump to voice room error {}!", e);
                    }
                }
            } else if (LANG_MANAGE.equals(scheme)) {
                JumpUtil.gotoChangeLangActivity(mContext);
            } else if (JUMP_MEDAL_PAGE.equals(scheme)) {
                int uid = getInt(uri, "uid");
                if (uid == 0) {
                    uid = LoginHelper.getLoginUid();
                }
                JumpUtil.gotoMedalActivity(mContext, uid);
            } else if (JUMP_HOME_TASK.equals(scheme)) {
                JumpUtil.gotoTaskActivity(mContext);
            } else if (JUMP_SEE_RECORD.equals(scheme)) {
                int gradeUid = getInt(uri, "target");
                JumpUtil.gotoUserGradeActivity(gradeUid, mContext);
            } else if (CHIP_LOTTERY.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                GameLotteryDialog.show(mContext, gameType);
            } else if (JACKAROO_VIP_ROOM_LIST.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                JackarooVipRoomListActivity.jump(mContext, gameType);
            } else if (JACKAROO_CREATE_GAME_ROOM.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                boolean isVip = getBoolean(uri, "is_vip");
                JackarooRoomCreateActivity.Companion.go(mContext, isVip, gameType);
            } else if (COCOS.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                Cocos243Helper.gotoCommonInCocosWithDeeplink(mContext, schemeText, gameType, source);
            } else if (COMPETITION_CREATE.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                ApiService.of(ICompetitionApi.class).gotoCompetitionCreate(mContext, gameType);
            } else if (COMPETITION_LIST.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                ApiService.of(ICompetitionApi.class).gotoCompetitionList(mContext, gameType, source);
            } else if (COMPETITION_DETAIL.equals(scheme)) {
                int gameType = getInt(uri, "game_type");
                int cid = getInt(uri, "cid");
                ApiService.of(ICompetitionApi.class).gotoCompetitionDetail(mContext, gameType, cid);
            } else if (!TextUtils.isEmpty(toast)) {
                ToastUtil.show(toast);
            }
        } catch (Exception e) {
            ToastUtil.debugShow("open deeplink fail" + e);
            HLog.d(TAG, HLog.USR, "open deep link   error {}!", Log.getStackTraceString(e));
        }
        if (callback != null) callback.onJumpFinish();
    }

    private static void jumpSocialGame(int gameType, Activity activity, String scene) {
        JumpQuickStart.simpleQuickStart(gameType, activity, scene);
    }

    private static void jumpToHotVoiceRoom(final Context context, final String source) {
        VoiceRoomPacketSender.getVoiceRoomList(0, 0, "", new LifeSeqCallback(ContextUtil.getLife(context)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.VoiceRoomListRsp voiceRoomListRsp = (TmpRoomPackets.VoiceRoomListRsp) head.message;
                List<TmpRoomPackets.VoiceRoom> voiceRoomList = new ArrayList<>();
                if (voiceRoomListRsp != null) {
                    voiceRoomList = voiceRoomListRsp.getVoiceRoomListList();
                }
                List<VoiceRoomListInfo> voiceRoomListInfos = VoiceRoomListInfo.parseList(voiceRoomList);
                int maxSize = voiceRoomListInfos.size();
                if (maxSize < 1) return;
                if (maxSize > 5) {
                    maxSize = 5;
                }

                int index = (int) (Math.random() * maxSize);
                EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(context, voiceRoomListInfos.get(index).rid, voiceRoomListInfos.get(index).gameType).setSource(source);
                JumpRoomUtil.getInstance().searchRoom(enterRoomInfo, JumpRoomUtil.TMP_ROOM_SCENE);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public static ShowTextInfo parseMsgText(String schemeText) {
        try {
            //去掉消息最后的最后一个/
            if (schemeText.endsWith("/")) {
                schemeText = schemeText.substring(0, schemeText.length() - 1);
            }

            Uri uri = Uri.parse(schemeText);
            String scheme = uri.getAuthority();
            String msgText = uri.getQueryParameter("msg_text");
            int color = getInt(uri, "color");
            boolean bold = getBoolean(uri, "bold");
            int textSize = getInt(uri, "text_size");
            //用户名字的deeplink，替换备注显示
            if (USER_INFO.equals(scheme)) {
                int uid = getInt(uri, "uid");
                boolean showRemark = getBoolean(uri, "show_remark");
                if (showRemark) {
                    FriendInfo friendInfo = FriendInfoCacheManager.getInstance().getFriendInfoByUid(uid);
                    if (friendInfo != null) {
                        msgText = friendInfo.getRemarkName();
                    }
                }
            }
            return new ShowTextInfo(msgText, color, bold, textSize);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ShowTextInfo("");
    }

    public static boolean isWespyDeepLink(String content) {
        return !TextUtils.isEmpty(content) && content.startsWith(DEEP_LINK);
    }

    public static String checkShowDeepLinkSpan(Context context, SpannableStringBuilder ssb, String content, int spanColor) {
        Matcher m = P.matcher(content);
        int msgUsedIndex = 0;
        boolean find = false;
        StringBuilder sb = new StringBuilder();
        while (m.find()) {
            String cur = m.group();
            int start = content.indexOf(cur, msgUsedIndex);
            String s = content.substring(msgUsedIndex, start);
            sb.append(s);
            ssb.append(s);
            msgUsedIndex = start + cur.length();
            find = true;
            ShowTextInfo text = JumpCommon.parseMsgText(cur);
            start = ssb.length();
            ssb.append(text.getText());
            sb.append(text.getText());
            int showColor = text.hasColor() ? text.getColor() : spanColor;//deeplink里面的color优先级更高
            ssb.setSpan(new TextSpanUtil.WespyDeepLinkSpan(cur, showColor), start, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            if (text.getBold()) {
                ssb.setSpan(new StyleSpan(Typeface.BOLD), start, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            if (text.getTexSize() > 0) {
                ssb.setSpan(new AbsoluteSizeSpan(ScreenUtil.dip2px(text.getTexSize())), start, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
        String left;
        if (find) {
            left = content.substring(msgUsedIndex);
        } else {
            left = content;
        }
        ssb.append(left);
        sb.append(left);
        return sb.toString();
    }

    private static int getInt(Uri uri, String key) {
        return getInt(uri, key, 0);
    }

    private static int getInt(Uri uri, String key, int defaultValue) {
        String text = uri.getQueryParameter(key);
        if (TextUtils.isEmpty(text)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(text);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private static boolean getBoolean(Uri uri, String key) {
        return getBoolean(uri, key, false);
    }

    private static boolean getBoolean(Uri uri, String key, boolean defaultValue) {
        String text = uri.getQueryParameter(key);
        if (TextUtils.isEmpty(text)) {
            return defaultValue;
        }
        try {
            return Boolean.parseBoolean(text);
        } catch (Exception e) {
            return defaultValue;
        }

    }
}
