package com.wepie.wespy.module.shop;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.actionbar.AppBarLayoutUtil;
import com.huiwan.widget.banner.BannerViewPager;
import com.huiwan.widget.banner.IBannerPageAdapter;
import com.huiwan.widget.banner.ViewPagerLineIndicator;
import com.three.http.callback.EmptyDataCallback;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotNode;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.makefriend.ShopBanner;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.pay.commonapi.JumpPay;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.HashMap;
import java.util.List;

public class ShopFragment extends Fragment {
    private static final int[] tabRedDots = new int[]{
            RedDotNode.NODE_M_S_RING, RedDotNode.NODE_M_S_DECOR, RedDotNode.NODE_M_S_PROP,
            RedDotNode.NODE_M_S_DECOR, RedDotNode.NODE_M_S_PROP, RedDotNode.NODE_M_S_PROP};
    public static final int[] tabTitleRes = new int[]{R.string.prop_category_ring, R.string.prop_category_decoration,
            R.string.prop_category_item, R.string.prop_category_game, R.string.prop_category_family, R.string.prop_category_jackaroo_skin};
    private AppBarLayout appBarLayout;
    private SecKillItemView seckillHeader;
    private ViewGroup bannerLay;
    private BannerViewPager<ShopBanner> bannerPager;
    private ViewPagerLineIndicator bannerIndicator;
    private TextView coinTv;
    private TextView familyCoinTv;
    private FrameLayout showAllSeckillLay;
    private View bannerPadding;
    private TabLayout goodPagerTabLayout;
    private boolean buyWithClose = false;
    private static final float BANNER_HEIGHT_WIDTH_RATE = 100F / 343;
    private ViewPager2 goodsListViewPager;
    private boolean isFirstShow = true;

    private ShopViewModel viewModel;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(requireActivity()).get(ShopViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.shop_view, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        initData();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!isFirstShow) {
            return;
        }
        isFirstShow = false;
        if (seckillHeader != null) seckillHeader.resumeAnim();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (seckillHeader != null) seckillHeader.pauseAnim();
    }

    private void initView(View view) {
        seckillHeader = view.findViewById(R.id.seckill_header);
        bannerLay = view.findViewById(R.id.banner_lay);
        bannerPager = view.findViewById(R.id.banner_view_lay);
        bannerIndicator = view.findViewById(R.id.banner_pager_indicator);
        appBarLayout = view.findViewById(R.id.appbar);
        goodPagerTabLayout = view.findViewById(R.id.good_pager_tab_layout);
        LinearLayout coinLay = view.findViewById(R.id.coin_lay);
        familyCoinTv = view.findViewById(R.id.family_coin_tv);
        coinTv = view.findViewById(R.id.coin_tv);
        showAllSeckillLay = view.findViewById(R.id.show_all_seckill_lay);
        bannerPadding = view.findViewById(R.id.banner_padding);

        view.post(this::setBannerHeight);

        goodsListViewPager = view.findViewById(R.id.goods_list_container);
        goodPagerTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                Object tag = tab.getTag();
                if (tag instanceof TabViewHolder) {
                    ((TabViewHolder) tag).setSelect(true);
                }

                RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_SHOP, tab.getPosition(), new EmptyDataCallback("shopview"));
                checkShowRedDot();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                Object tag = tab.getTag();
                if (tag instanceof TabViewHolder) {
                    ((TabViewHolder) tag).setSelect(false);
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        goodsListViewPager.setAdapter(new FragmentStateAdapter(this) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                return ShopItemFragment.create(position);
            }

            @Override
            public int getItemCount() {
                return ShopViewModel.getCategorySize();
            }
        });
        new TabLayoutMediator(goodPagerTabLayout, goodsListViewPager, (tab, position) -> {
            View v = LayoutInflater.from(getContext()).inflate(R.layout.item_good_pager_tab, goodPagerTabLayout, false);
            TabViewHolder holder = new TabViewHolder(v);
            int category = ShopViewModel.findCategoryByPos(position);
            holder.bind(tabTitleRes[category], tabRedDots[category]);
            holder.setSelect(position == goodsListViewPager.getCurrentItem());
            tab.setCustomView(v);
            tab.setTag(holder);
        }).attach();

        HashMap<String, Object> trackParam = new HashMap<>();
        trackParam.put("refer_screen_name", TrackScreenName.NATIVE_STORE_PAGE);
        coinLay.setOnClickListener(v -> JumpPay.showGoods(v.getContext(), false, trackParam));

        seckillHeader.setCallback(this::updateAll);
        showAllSeckillLay.setOnClickListener(v -> JumpUtil.gotoSeckillListActivity(getContext()));

        AppBarLayoutUtil.adjustEvaluation(appBarLayout);
    }

    private void initData() {
        viewModel.getShopBannerListLiveData().observe(getViewLifecycleOwner(), list -> {
            if (list != null && !list.isEmpty()) {
                updateBannerPagerInfo(list);
            } else {
                bannerLay.setVisibility(View.GONE);
            }
        });
        viewModel.getFamilySimpleInfoLiveData().observe(getViewLifecycleOwner(), familySimpleInfo -> {
            long familyCoin = familySimpleInfo.getFamilyCoin();
            if (familyCoin > 100_0000) {
                familyCoinTv.setText(String.format(LibBaseUtil.getLocale(), "%sk", familyCoin / 1000));
            } else {
                familyCoinTv.setText(String.valueOf(familyCoin));
            }
        });
        viewModel.getPropSecKillListLiveData().observe(getViewLifecycleOwner(), secKillList -> {
            if (secKillList == null || secKillList.isEmpty()) {
                seckillHeader.setVisibility(View.GONE);
                showAllSeckillLay.setVisibility(View.GONE);
                bannerPadding.setVisibility(View.GONE);
                return;
            }

            if (!secKillList.get(0).isEnd()) {
                seckillHeader.setSeckillInfo(secKillList.get(0), false);
                seckillHeader.setVisibility(View.VISIBLE);
                bannerPadding.setVisibility(View.VISIBLE);
            }
            showAllSeckillLay.setVisibility(secKillList.size() > 1 ? View.VISIBLE : View.GONE);
        });

        viewModel.getTabIndexLiveData().observe(getViewLifecycleOwner(), this::setTabIndex);
        viewModel.getActionLiveData().observe(getViewLifecycleOwner(), shopAction -> {
            if (shopAction instanceof ShopAction.CheckAppBarAction) {
                if (seckillHeader.isShown()) {
                    appBarLayout.setExpanded(false, true);
                }
            } else if (shopAction instanceof ShopAction.RefreshAction) {
                updateAll();
            } else if (shopAction instanceof ShopAction.PurchaseAction) {
                EventDispatcher.postPropBuyEvent(((ShopAction.PurchaseAction) shopAction).getPropId());
                if (buyWithClose) {//求婚，更换戒指，购买成功，关闭购买界面
                    Activity activity = ContextUtil.getActivityFromContext(getContext());
                    if (activity != null) activity.finish();
                }
            }
        });
        refreshCoin();
    }

    private void setBannerHeight() {
        bannerPager.getLayoutParams().height = (int) (bannerPager.getMeasuredWidth() * BANNER_HEIGHT_WIDTH_RATE);
    }

    public void updateBannerPagerInfo(List<ShopBanner> bannerList) {
        if (bannerList == null) {
            bannerLay.setVisibility(View.GONE);
            return;
        }
        if (bannerList.isEmpty()) {
            bannerLay.setVisibility(View.GONE);
        } else if (bannerList.size() == 1) {
            bannerIndicator.setVisibility(View.GONE);
            bannerLay.setVisibility(View.VISIBLE);
        } else {
            bannerLay.setVisibility(View.VISIBLE);
            bannerIndicator.setVisibility(View.VISIBLE);
            bannerIndicator.setItemCount(bannerList.size());
        }
        bannerPager.setData(bannerList, new IBannerPageAdapter<>() {
            @Override
            @NonNull
            public View onCreate(@NonNull ViewGroup container, ShopBanner data) {
                ImageView imageView = new ImageView(container.getContext());
                imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                WpImageLoader.load(data.img, imageView, ImageLoadInfo.newInfo().width(container.getWidth()));
                return imageView;
            }

            @Override
            public void onBind(@NonNull View view, ShopBanner data) {
                view.setOnClickListener(v -> JumpCommon.gotoOtherPager(getContext(), data.deeplink, TrackScreenName.AVATAR_SHOP_BANNER, null));
            }
        });
        bannerPager.enableAutoLoop(5000L);
        bannerIndicator.attachBannerViewPager(bannerPager);
    }

    private void checkShowRedDot() {
        for (int i = 0; i < goodPagerTabLayout.getTabCount(); i++) {
            TabLayout.Tab tab = goodPagerTabLayout.getTabAt(i);
            TabViewHolder holder;
            if (tab != null && tab.getTag() instanceof TabViewHolder) {
                holder = (TabViewHolder) tab.getTag();
            } else {
                continue;
            }
            if (i >= tabRedDots.length) {
                continue;
            }
            holder.refreshRedDot(tabRedDots[i]);
        }
    }

    public void refreshCoin() {
        long coin = LoginHelper.getCoin();
        if (coin > 100_0000) {
            coinTv.setText(String.format(LibBaseUtil.getLocale(), "%sk", coin / 1000));
        } else {
            coinTv.setText(String.valueOf(coin));
        }
    }

    private void updateAll() {
        viewModel.updateAll();
        refreshCoin();
    }

    private void setTabIndex(final int tabIndex) {
        if (tabIndex == 0) {
            RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_SHOP, 0, new EmptyDataCallback("shopview"));
            checkShowRedDot();
        }
        if (goodsListViewPager != null) goodsListViewPager.setCurrentItem(tabIndex, false);
    }

    public void setBuyWithClose(boolean buyWithClose) {
        this.buyWithClose = buyWithClose;
    }

    private static class TabViewHolder {

        private final TextView titleView;
        private final View redView;

        TabViewHolder(View item) {
            titleView = item.findViewById(R.id.shop_tab_tv);
            redView = item.findViewById(R.id.shop_tab_rd_iv);
        }

        public void refreshRedDot(int tabRedDot) {
            int visibility = RedDotUtil.get().hasRedDot(tabRedDot) ? View.VISIBLE : View.GONE;
            redView.setVisibility(visibility);
        }

        public void bind(int tabTitleRes, int tabRedDot) {
            titleView.setText(tabTitleRes);
            refreshRedDot(tabRedDot);
        }

        public void setSelect(boolean b) {
            titleView.setTextColor(ResUtil.getColor(b ? R.color.color_accent_ex : R.color.color_primary_dark));
        }
    }
}
