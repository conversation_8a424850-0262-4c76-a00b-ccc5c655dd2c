package com.wepie.wespy.module.contact.detail

import android.content.Context
import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.user.LifeUserInfoLoadCallback
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserInfoLoadCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.User
import com.huiwan.user.entity.UserTagStatus
import com.three.http.callback.LifeDataCallback
import com.three.http.callback.Result
import com.wejoy.jackaroo.record.GameCareerCache
import com.wejoy.weplay.ex.cancellable.autoCancel
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.weplay.care.api.CareApiResposity
import com.wepie.weplay.care.api.CareApiResposity.getEachOtherCareValue
import com.wepie.weplay.care.guard.entity.CareEachOtherInfo
import com.wepie.weplay.care.guard.entity.CareInfoInUserDetail
import com.wepie.wespy.model.entity.AvatarImageInfo
import com.wepie.wespy.model.entity.BeBlockedState
import com.wepie.wespy.model.entity.family.FamilyBasicInfoRsp
import com.wepie.wespy.model.entity.marry.MarryInfo
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import com.wepie.wespy.net.http.api.BeBlockApi
import com.wepie.wespy.net.http.api.FamilyApi
import com.wepie.wespy.net.http.api.PropApi
import com.wepie.wespy.net.http.api.VisitorApi
import com.wepie.wespy.net.tcp.callback.marry.MarryInfo2Callback
import com.wepie.wespy.net.tcp.sender.MarryPacketSender
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicInteger

class UserInfoDetailViewModel : LifeViewModel() {
    var targetUid: Int = 0
        private set

    private val _userLiveData = MutableLiveData<User>()
    val userLiveData: LiveData<User> = _userLiveData

    private val _avatarImageInfoLiveData = MutableLiveData<AvatarImageInfo>()
    val avatarImageInfoLiveData: LiveData<AvatarImageInfo> = _avatarImageInfoLiveData

    private val _careInfoInUserDetailLiveData: MutableLiveData<CareInfoInUserDetail> =
        MutableLiveData()
    val careInfoInUserDetailLiveData: LiveData<CareInfoInUserDetail> = _careInfoInUserDetailLiveData

    private val _familyLiveData = MutableLiveData<FamilyBasicInfoRsp>()
    val familyLiveData: LiveData<FamilyBasicInfoRsp> = _familyLiveData

    private val _marryInfoLiveData = MutableLiveData<MarryInfo>()
    val marryInfoLiveData: LiveData<MarryInfo> = _marryInfoLiveData

    private val eventFlow = MutableSharedFlow<UserInfoDetailEvent>()

    var isSelfBlockedState = -1 // 自己是否被拉黑
        private set

    var hasCared = false
        private set

    fun init(uid: Int) {
        targetUid = uid
        if (targetUid == LoginHelper.getLoginUid()) {
            isSelfBlockedState = UserInfoDetailActivity.NOT_BE_BLOCKED
        } else {
            getBeBlockedState()
        }
        getFamily()
        visit()
        checkIsCared()
    }

    fun observeEvent(owner: LifecycleOwner, observer: Observer<UserInfoDetailEvent>) {
        owner.lifecycleScope.launch {
            eventFlow.collect {
                observer.onChanged(it)
            }
        }
    }

    fun notifyEvent(event: UserInfoDetailEvent) {
        viewModelScope.launch {
            eventFlow.emit(event)
        }
    }

    fun getUserInfo(): User? {
        return userLiveData.value
    }

    fun requestUserInfo(callback: UserInfoLoadCallback) {
        val isLoading = AtomicInteger(0)
        UserService.get().getCacheUserLocal(targetUid, object : LifeUserInfoLoadCallback(toLife()) {
            override fun onUserInfoSuccess(userInfo: User) {
                val flag = isLoading.getAndIncrement()
                if (flag == 0) {
                    _userLiveData.value = userInfo
                    callback.onUserInfoSuccess(userInfo)
                }
            }

            override fun onUserInfoFailed(description: String?) {
                super.onUserInfoFailed(description)
            }
        })

        UserService.get()
            .getCacheUserFromServer(targetUid, object : LifeUserInfoLoadCallback(toLife()) {
                override fun onUserInfoSuccess(userInfo: User) {
                    _userLiveData.value = userInfo
                    EventDispatcher.postUserInfoUpdate(userInfo)
                    EventDispatcher.postUserInfoChangeEvent(
                        userInfo.uid, userInfo.remarkName, userInfo.headimgurl
                    )
                    val flag = isLoading.getAndIncrement()
                    if (flag == 0) {
                        callback.onUserInfoSuccess(userInfo)
                    }
                    requestAvatarImage()
                }

                override fun onUserInfoFailed(description: String?) {
                    super.onUserInfoFailed(description)
                    val flag = isLoading.get()
                    if (flag == 0) {
                        callback.onUserInfoFailed(description)
                    }
                }
            })
    }

    private fun requestAvatarImage() {
        PropApi.getAvatarImage(
            targetUid, true,
            object : LifeDataCallback<AvatarImageInfo>(toLife()) {
                override fun onSuccess(result: Result<AvatarImageInfo>) {
                    _avatarImageInfoLiveData.value = result.data
                }

                override fun onFail(code: Int, msg: String) {
                    ToastUtil.show(msg)
                }
            })
    }

    fun getCareInfoListV2() {
        CareApiResposity.getCareInfoListV2(
            targetUid,
            object : LifeDataCallback<CareInfoInUserDetail>(this) {

                override fun onSuccess(result: Result<CareInfoInUserDetail>?) {
                    result?.let {
                        _careInfoInUserDetailLiveData.value = result.data
                    }
                }

                override fun onFail(code: Int, msg: String?) {
                    ToastUtil.show(msg)
                }

            })
    }

    private fun getBeBlockedState() {
        BeBlockApi.getBeBlockedState(targetUid, object : LifeDataCallback<BeBlockedState>(this) {
            override fun onSuccess(result: Result<BeBlockedState>) {
                isSelfBlockedState = if (result.data.be_blocked) {
                    UserInfoDetailActivity.BE_BLOCKED
                } else {
                    UserInfoDetailActivity.NOT_BE_BLOCKED
                }
                notifyEvent(UserInfoDetailEvent.BlockedState(result.data.be_blocked))
            }

            override fun onFail(code: Int, msg: String) {
                ToastUtil.show(msg)
            }

        })
    }

    private fun getFamily() {
        FamilyApi.memberBasicInfo(
            targetUid, 1,
            object : LifeDataCallback<FamilyBasicInfoRsp>(this) {
                override fun onSuccess(result: Result<FamilyBasicInfoRsp>) {
                    _familyLiveData.value = result.data
                }

                override fun onFail(i: Int, s: String) {
                    ToastUtil.show(s)
                }
            })
    }

    private fun visit() {
        VisitorApi.visit(targetUid, null)
    }

    /**
     * 是否有守护值
     */
    private fun checkIsCared() {
        getEachOtherCareValue(targetUid, object : LifeDataCallback<CareEachOtherInfo>(this) {
            override fun onSuccess(result: Result<CareEachOtherInfo>) {
                hasCared = result.data.hasCared()
            }

            override fun onFail(code: Int, msg: String) {
                hasCared = false
            }
        })
    }

    fun updateRing() {
        MarryPacketSender.getMarryInfoReq(targetUid, toLife(), object : MarryInfo2Callback {
            override fun onSuccess(marryInfo: MarryInfo) {
                _marryInfoLiveData.value = marryInfo
            }

            override fun onFail(msg: String) = Unit
        })
    }

    companion object {
        fun get(view: View): UserInfoDetailViewModel? {
            return get(view.context)
        }

        fun get(context: Context): UserInfoDetailViewModel? {
            if (context is ViewModelStoreOwner) {
                return ViewModelProvider(context).get(UserInfoDetailViewModel::class.java)
            }
            return null
        }
    }
}

sealed class UserInfoDetailEvent {
    class AddFriend(val uid: Int) : UserInfoDetailEvent()
    object ShowGiftDialog : UserInfoDetailEvent()
    class BlockedState(val isBlock: Boolean) : UserInfoDetailEvent()
    class FamilyLightNameUpdate(val name: String) : UserInfoDetailEvent()
    class RefreshSelfTagEvent(val userTagStatuses: List<UserTagStatus>) : UserInfoDetailEvent()
}