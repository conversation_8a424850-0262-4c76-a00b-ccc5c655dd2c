package com.wepie.wespy.module.pay.commonapi

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.IapApi
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.banner.BannerViewPager
import com.huiwan.widget.banner.IBannerPageAdapter
import com.huiwan.widget.banner.ViewPagerLineIndicator
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.model.entity.other.WejoyDiscountInfo
import com.wepie.wespy.module.common.jump.JumpCommon
import com.wepie.wespy.net.http.api.OrderApi
import com.wepie.wespy.net.http.api.OrderApi.GoodsType
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Collections

class GoodsFragment : Fragment() {

    private var type: String = OrderApi.GoodsType.GOLD
    private lateinit var viewModel: GoodsViewModel

    private lateinit var payHelpTx: TextView
    private lateinit var diamondNumTv: TextView
    private lateinit var coinNumTv: TextView
    private lateinit var bannerIndicator: ViewPagerLineIndicator
    private lateinit var bannerLay: View
    private lateinit var bannerPager: BannerViewPager<WejoyDiscountInfo.PayBannerInfo>
    private val adapter by lazy {
        GoodsListRvHelper.Adapter()
    }
    private lateinit var rv: RecyclerView
    private lateinit var bgIv: ImageView
    private lateinit var rootLay: ViewGroup

    private val trackParamMap = HashMap<String, Any>()

    private var updateCallback: GoodsListRvHelper.UpdateCallback? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(requireActivity())[GoodsViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        arguments?.let {
            type = it.getString("type", OrderApi.GoodsType.GOLD)
            trackParamMap.clear()
            trackParamMap.putAll(if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(JumpPay.KEY_TRACK_PARAM, trackParamMap.javaClass)
            } else {
                it.getParcelable(JumpPay.KEY_TRACK_PARAM) as HashMap<String, Any>?
            } ?: emptyMap())
            // 额外追加screenName，在adapter里上报时要用
            trackParamMap.put("screen_name", TrackScreenName.PAY_PAGE);
        }
        return inflater.inflate(R.layout.view_gold_new, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserver()
        updateData()
        ApiService.of(IapApi::class.java).checkUnHandledPurchase(false)
    }

    override fun onResume() {
        super.onResume()
        val user = LoginHelper.getLoginUser() ?: return
        val map = HashMap<String, Any>()
        if (type == GoodsType.GOLD) {
            map["sub_screen_name"] = ResUtil.getStr(R.string.jackaroo_track_coin_sub)
            map["account_coin"] = user.chipCoin.toString()
        } else {
            map["sub_screen_name"] = ResUtil.getStr(R.string.jackaroo_track_coin_main)
            map["account_coin"] = user.coin.toString()
        }
        map["is_vip"] = user.isVip
        map.putAll(trackParamMap)
        ShenceEvent.appViewScreen(TrackScreenName.PAY_PAGE, map)
    }

    private fun initObserver() {
        if (type == OrderApi.GoodsType.GOLD) {
            viewModel.goodGoldLivaData.observe(viewLifecycleOwner) {
                adapter.update(it, trackParamMap, updateCallback, type)
            }
        } else {
            viewModel.diamondLiveData.observe(viewLifecycleOwner) {
                adapter.update(it, trackParamMap, updateCallback, type)
            }
            viewModel.bannerList.observe(viewLifecycleOwner) {
                updateBannerPagerInfo(it)
            }
        }

        UserService.get().selfUser.observe(viewLifecycleOwner) {
            refreshUserData(it.getCoin())
        }
        lifecycleScope.launch {
            val flow = ApiService.of(ChipApi::class.java).gameChipFlow() ?: return@launch
            flow.collectLatest {
                coinNumTv.text = it.toString()
            }
        }
    }


    private fun initView() {
        view?.apply {
            bgIv = findViewById(R.id.bg_iv)
            ViewUtil.setViewHeight(bgIv, (ScreenUtil.getScreenWidth() / 1125F * 966).toInt())
            rv = findViewById(R.id.pay_goods_list)
            rv.setLayoutManager(GridLayoutManager(rv.context, 3))
            if (!ScreenUtil.isLandScape(rv.context)) {
                rv.addItemDecoration(GoodsItemDecoration())
            }
            rv.adapter = adapter
            diamondNumTv = findViewById(R.id.pay_diamond_tv)
            coinNumTv = findViewById(R.id.coin_tv)
            payHelpTx = findViewById(R.id.pay_help_tv)
            bannerLay = findViewById(R.id.banner_lay)
            bannerPager = findViewById(R.id.banner_view_lay)
            bannerIndicator = findViewById(R.id.banner_pager_indicator)
            rootLay = findViewById(R.id.root_lay)
            ViewUtil.setTopMargins(rootLay, ScreenUtil.getStatusBarHeight())
        }


        bannerPager.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(16f).toFloat())
        bannerPager.setClipToOutline(true)
        bannerIndicator.attachBannerViewPager(bannerPager)
    }

    private fun updateData() {
        when (type) {
            OrderApi.GoodsType.GOLD -> {
                payHelpTx.text = ConfigHelper.getInstance().chargeTip
                bgIv.setImageResource(R.drawable.pay_coin_bg_pic)
            }

            OrderApi.GoodsType.DIAMOND -> {
                payHelpTx.text = ConfigHelper.getInstance().chipCoinChargeTip
                bgIv.setImageResource(R.drawable.pay_diamond_bg_pic)
            }
        }
    }

    private fun refreshUserData(coin: Long) {
        diamondNumTv.text = coin.toString()
    }

    private fun updateBannerPagerInfo(bannerList: List<WejoyDiscountInfo.PayBannerInfo>?) {
        if (bannerList.isNullOrEmpty()) {
            bannerLay.visibility = View.GONE
            return
        }
        if (bannerList.size == 1) {
            bannerIndicator.visibility = View.GONE
            bannerLay.visibility = View.VISIBLE
        } else {
            bannerLay.visibility = View.VISIBLE
            bannerIndicator.visibility = View.VISIBLE
            bannerIndicator.setItemCount(bannerList.size)
        }
        bannerPager.setData(
            bannerList,
            object : IBannerPageAdapter<WejoyDiscountInfo.PayBannerInfo> {
                override fun onCreate(
                    container: ViewGroup,
                    data: WejoyDiscountInfo.PayBannerInfo
                ): View {
                    return ImageView(container.context).apply {
                        setScaleType(ImageView.ScaleType.CENTER_CROP)
                    }
                }

                override fun onBind(
                    view: View,
                    data: WejoyDiscountInfo.PayBannerInfo
                ) {
                    if (view is ImageView) {
                        WpImageLoader.load(data.pic, view)
                    }
                    view.setOnClickListener {
                        HLog.d(TAG, HLog.USR, "updateBannerPagerInfo! onClick, banner=$data")
                        ShenceEvent.appClick(
                            TrackScreenName.GOODS_LIST_BANNER,
                            Collections.singletonMap<String, Any>(
                                "banner_id",
                                data.bannerId.toString()
                            )
                        )
                        JumpCommon.gotoOtherPager(
                            context,
                            data.url,
                            TrackScreenName.GOODS_LIST_BANNER,
                            null
                        )
                    }
                }

            })
        bannerIndicator.setItemCount(bannerList.size)
        bannerPager.enableAutoLoop(5000L)
    }


    companion object {
        private const val TAG = "GoodsFragment"

        @JvmStatic
        fun create(
            i: Int,
            trackParam: Map<String, Any>,
            updateCallback: GoodsListRvHelper.UpdateCallback
        ): Fragment {
            val type = GoodsViewModel.findCategoryByPos(i)
            val fragment = GoodsFragment()
            val bundle = Bundle()
            bundle.putString("type", type)
            bundle.putSerializable(JumpPay.KEY_TRACK_PARAM, if (trackParam is HashMap) trackParam else HashMap(trackParam))
            fragment.arguments = bundle
            fragment.updateCallback = updateCallback
            return fragment
        }
    }
}