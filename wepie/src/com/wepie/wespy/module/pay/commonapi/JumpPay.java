package com.wepie.wespy.module.pay.commonapi;

import android.content.Context;
import android.content.Intent;

import com.wepie.wespy.net.http.api.OrderApi;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by three on 15/4/25.
 */
public class JumpPay {
    final static String KEY_GOODS_TYPE = "_goods_type";
    final static String KEY_TRACK_PARAM = "track_param";

    public static void showGoods(Context context) {
        showGoods(context, false);
    }

    public static void showGoods(Context context, boolean subCoinFirst) {
        showGoods(context, subCoinFirst, new HashMap<>());
    }

    /**
     * @param trackParam GoodsListActivity上传埋点后还会原封不动地传给点击金额场景、创建订单场景
     *                        之前只传了referScreenName，后续可能会增加新的埋点数据，考虑到通用性，直接加一个map
     */
    public static void showGoods(Context context, boolean subCoinFirst, Map<String, Object> trackParam) {
        Intent intent = new Intent(context, GoodsListActivity.class);
        intent.putExtra(KEY_GOODS_TYPE, subCoinFirst ? OrderApi.GoodsType.GOLD : OrderApi.GoodsType.DIAMOND);
        if (trackParam instanceof HashMap<String, Object>) {
            intent.putExtra(KEY_TRACK_PARAM, (HashMap<String, Object>) trackParam);
        } else {
            intent.putExtra(KEY_TRACK_PARAM, new HashMap<>(trackParam));
        }
        context.startActivity(intent);
    }

}
