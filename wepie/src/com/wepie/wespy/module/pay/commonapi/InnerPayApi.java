package com.wepie.wespy.module.pay.commonapi;


import android.app.Activity;
import android.text.TextUtils;

import androidx.collection.ArrayMap;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IapApi;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.base.appfyers.AppsFlyerUtil;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;

import java.lang.ref.SoftReference;
import java.util.Collections;
import java.util.Map;

/**
 * Created by three on 15/4/25.
 */
public class InnerPayApi {
    private static final String TAG = "InnerPayApi";
    public static PayResultCallback mPayResultCallback;

    public static void showPay(final WespyGoods wespyGoods,
                               final Activity activity,
                               PayResultCallback callback) {
        showPay(wespyGoods, Collections.emptyMap(), activity, callback, Collections.emptyMap());
    }

    public static void showPay(final WespyGoods wespyGoods,
                               Map<String, String> extParam,
                               final Activity activity,
                               PayResultCallback callback) {
        showPay(wespyGoods, extParam, activity, callback, Collections.emptyMap());
    }

    public static void showPay(final WespyGoods wespyGoods,
                               final Activity activity,
                               PayResultCallback callback,
                               Map<String, Object> trackEventParam) {
        showPay(wespyGoods, Collections.emptyMap(), activity, callback, trackEventParam);
    }

    /**
     * 下面两个参数作用不一样，别用混了
     * @param extParam 参与http请求
     * @param trackEventParam 参与埋点统计，创建订单后上
     * 但并非所有订单都需要上报，目前仅要求“正常拉起商品页面或商品弹窗，点击某个金额后创建的订单”才需要埋点
     */
    public static void showPay(final WespyGoods wespyGoods,
                               Map<String, String> extParam,
                               final Activity activity,
                               PayResultCallback callback,
                               Map<String, Object> trackEventParam) {
        if (callback == null) {
            mPayResultCallback = null;
        } else {
            final SoftReference<PayResultCallback> softReference = new SoftReference<>(callback);
            mPayResultCallback = new PayResultCallback() {
                @Override
                public void onSuccess(WpPayResult payResult) {
                    PayResultCallback callback = softReference.get();
                    if (callback != null) {
                        callback.onSuccess(payResult);
                    }
                }

                @Override
                public void onFail() {
                    PayResultCallback callback = softReference.get();
                    if (callback != null) {
                        callback.onFail();
                    }
                }
            };
        }
        Map<String, String> ext = new ArrayMap<>();
        ext.put("afid", AppsFlyerUtil.getAfId());
        if (extParam != null) {
            ext.putAll(extParam);
        }

        showPayDialog(wespyGoods, ext, activity, trackEventParam);
    }

    private static void showPayDialog(final WespyGoods wespyGoods, Map<String, String> extParam, final Activity activity, Map<String, Object> trackEventParam) {
        doGooglePay(wespyGoods, extParam, activity, trackEventParam);
    }

    private static void doGooglePay(final WespyGoods wespyGoods, Map<String, String> extParam, final Activity activity, Map<String, Object> trackEventParam) {
        if (TextUtils.isEmpty(wespyGoods.gpProductId)) {
            ToastUtil.show(R.string.goods_info_error);
            return;
        }
        ProgressDialogUtil dialogUtil = new ProgressDialogUtil();
        dialogUtil.showLoading(activity, "", true);
        IapApi iapApi = ApiService.of(IapApi.class);
        iapApi.updateTrackEventParam(trackEventParam);
        iapApi.doPay(activity, wespyGoods, extParam, new DataCallback<>() {
            @Override
            public void onCall(IapApi.PayResult data) {
                HLog.d(TAG, HLog.USR, "pay success callback: {}, outCb:{}", data.msg, mPayResultCallback);
                ToastUtil.show(data.msg);
                dialogUtil.hideLoading();
                if (mPayResultCallback != null) {
                    WpPayResult result = new WpPayResult();
                    result.orderId = data.wpOrderId;
                    mPayResultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailed(int code, String msg) {
                HLog.d(TAG, HLog.USR, "pay failed callback: {}, outCb:{}", msg, mPayResultCallback);
                ToastUtil.show(msg);
                if (mPayResultCallback != null) {
                    mPayResultCallback.onFail();
                }
                dialogUtil.hideLoading();
            }
        });
    }

    public interface PayResultCallback {
        void onSuccess(WpPayResult payResult);

        void onFail();
    }

}
