package com.wepie.wespy.model.entity;

import android.content.ContentValues;
import android.database.Cursor;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.CursorUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.constants.BaseConstants;
import com.huiwan.store.database.WPModel;
import com.huiwan.user.LoginHelper;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.config.BaseConfig;
import com.wepie.wespy.model.entity.match.GameInviteInfo;
import com.wepie.wespy.module.chat.invitegame.GameInviteMsg;
import com.wepie.wespy.module.game.game.activity.JudgeCardItem;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_pu_msg_chat;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_pu_msg_chatBatch;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_pu_msg_newFriend;
import com.wepie.wespy.net.tcp.packet.ChatPackets.msg_chat_info;
import com.wepie.wespy.net.tcp.packet.ChatPackets.userInfoChatServer;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class WPMessage extends MessageModel implements GameInviteMsg {

    private static final long serialVersionUID = -5866781686666476088L;
    public static final int EXTENSION_TYPE_FLOWER = 1;
    public static final int EXTENSION_TYPE_GIFT = 3;
    public static final int EXTENSION_TYPE_PASSWORD_PACKET = 5;
    public static final int EXTENSION_TYPE_JK_SKIN = 9;

    private static final String TABLE_NAME = "message";

    private static final String MID = "mid";
    private static final String CONTENT = "content";
    private static final String SEND_UID = "send_uid";
    private static final String RECV_UID = "recv_uid";
    private static final String TIME = "time";
    public static final String STATUS = "status";
    private static final String MEDIA_TYPE = "media_type";
    private static final String EXTENSION = "extension";
    public static final String BUBBLE_ID = "bubble_id";
    public static final String INVITE_STATUS = "invite_status";
    public static final String SUB_TYPE = "sub_type";// 0-8 以前只有群聊有，9和10是新版本兼容合并了群聊私聊的subType
    public static final String REF_MID = "ref_mid";

    public static final int STATUS_OK = 0;
    public static final int STATUS_SENDING = 1;
    public static final int STATUS_FAIL = 2;
    public static final int STATUS_VIEWED = 3;
    public static final int STATUS_SCREENSHOT = 4;
    public static final int STATUS_VIDEO_BLOCKED = 5; //视频封禁

    public static final int MEDIA_TYPE_TEXT = 1;//文字、礼物消息等
    public static final int MEDIA_TYPE_PHOTO = 2;//私密图片
    public static final int MEDIA_TYPE_AUDIO = 3;//语音
    public static final int MEDIA_TYPE_DICE = 6;//骰子
    public static final int MEDIA_TYPE_RED_PACKET = 8;//红包
    public static final int MEDIA_TYPE_RED_PACKET_TIP = 9;//红包提示，xxx领取
    public static final int MEDIA_TYPE_SEND_CARD = 10;//个人名片
    public static final int MEDIA_TYPE_INVITE = 11;//邀请消息
    public static final int MEDIA_TYPE_NORMAL_PHOTO = 12;//普通图片
    public static final int MEDIA_TYPE_COCOS_GAME_INVITE = 13;//游戏邀请
    public static final int MEDIA_TYPE_GAME_INVITE_NEW = 14;//新的卡片式小游戏邀请
    public static final int MEDIA_TYPE_RECALL = 15; //撤回
    public static final int MEDIA_TYPE_SYSTEM = 16; //系统消息
    public static final int MEDIA_TYPE_VIP_DONATE = 17;//赠送vip
    public static final int MEDIA_TYPE_GAME_INVITE = 18;//游戏邀请【小游戏及桌游】
    public static final int MEDIA_TYPE_XROOM_INVITE = 19;//密室杀邀请
    public static final int MEDIA_TYPE_H5_SHARE = 20;//分享H5
    public static final int MEDIA_TYPE_CHAT_LIMIT = 23;//单向私聊限制
    public static final int MEDIA_TYPE_GROUP_SHARE = 24;//群组分享
    public static final int MEDIA_TYPE_CHAT_VIDEO = 25;//单聊视频消息

    public static final int EXTENSION_TYPE_INVITE_FRIEND = 1;
    public static final int EXTENSION_TYPE_CONVENE_PLAYER = 2;
    public static final int EXTENSION_TYPE_CARD_MSG = 3;
    public static final int EXTENSION_TYPE_ASSEMBLE_MSG = 4;

    public static final int SUB_TYPE_EMOTICON = ChatMsg.SUBTYPE_EMOTICON;

    private static final String TAG = "WPMessage";

    String mid;
    String content;
    Integer send_uid;
    Integer recv_uid;
    Long time;
    Integer status;
    Integer media_type;
    String extension;
    Integer subType = 0;

    private int bubbleId;
    private int inviteStatus = STATUS_INIT;

    private String ref_mid = "";

    private String quoteType;

    // whether original photo upload
    private boolean useOrigin = false;

    @Override
    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }


    public void setSend_uid(Integer send_uid) {
        this.send_uid = send_uid;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getRefMid() {
        return ref_mid;
    }

    public void setRefMid(String refMid) {
        this.ref_mid = refMid;
    }

    public String getQuoteType() {
        return quoteType;
    }

    public void setQuoteType(String quoteType) {
        this.quoteType = quoteType;
    }

    public Integer getSend_uid() {
        return send_uid;
    }

    public void setRecv_uid(Integer recv_uid) {
        this.recv_uid = recv_uid;
    }

    public Integer getRecv_uid() {
        return recv_uid;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getMedia_type() {
        return media_type;
    }

    public void setMedia_type(Integer media_type) {
        this.media_type = media_type;
    }

    public String getExtension() {
        return extension == null ? "" : extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setExtensionWithGiftNum(int gift_id, int gift_num, boolean is_private, String comboId, int comboTimes) {
        try {
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gift_id);
            JSONObject obj = new JSONObject();
            obj.put("type", EXTENSION_TYPE_GIFT);
            obj.put("gift_id", gift_id);
            obj.put("num", gift_num);
            obj.put("is_public", is_private ? BaseConfig.SERVER_NO : BaseConfig.SERVER_YES);
            if (gift != null) {
                obj.put("gift_type", gift.getGiftType());
            }
            obj.put("combo_id", comboId);
            obj.put("combo_times", comboTimes);
            setExtension(obj.toString());
        } catch (Exception e) {
            HLog.d(this.getClass().getSimpleName(), e.toString());
        }
    }

    public void setExtensionWithSendInfo(GiftSendInfo sendInfo) {
        try {
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(sendInfo.giftId);
            JSONObject obj = new JSONObject();
            obj.put("type", EXTENSION_TYPE_GIFT);
            obj.put("gift_id", sendInfo.giftId);
            obj.put("num", sendInfo.giftNum);
            obj.put("is_public", sendInfo.isPrivate ? BaseConfig.SERVER_NO : BaseConfig.SERVER_YES);
            if (gift != null) {
                obj.put("gift_type", gift.getGiftType());
            }
            obj.put("combo_id", sendInfo.comboId);
            obj.put("combo_times", sendInfo.comboTimes);
            obj.put("is_gift_card", sendInfo.isGiftCard ? 1 : 0);
            obj.put("broad_content", sendInfo.extMsg);
            setExtension(obj.toString());
        } catch (Exception e) {
            HLog.d(this.getClass().getSimpleName(), e.toString());
        }
    }

    public void addInfoFromExtraInfo(String extraInfo) {
        if (TextUtils.isEmpty(extraInfo)) {
            return;
        }
        try {
            JSONObject newExtJo = new JSONObject(extension);
            JSONObject jsonObject = new JSONObject(extraInfo);
            if (jsonObject.has("give_away_desc")) {
                String desc = jsonObject.optString("give_away_desc", "");
                newExtJo.put("give_away_desc", desc);
            }
            if (jsonObject.has("give_away_coin")) {
                int coin = jsonObject.optInt("give_away_coin", 0);
                newExtJo.put("give_away_coin", coin);
            }
            if (jsonObject.has("explode_level")) {
                int level = jsonObject.optInt("explode_level", 1);
                newExtJo.put("explode_level", level);
            }
            if (jsonObject.has("charm_type")) {
                newExtJo.put("charm_type", jsonObject.optString("charm_type", ""));
            }
            if (jsonObject.has("animation_index")) {
                newExtJo.put("animation_index", jsonObject.optString("animation_index", ""));
            }
            if (jsonObject.has("show_img_uid_list")) {
                JSONArray jsonArray = jsonObject.optJSONArray("show_img_uid_list");
                if (null != jsonArray) {
                    newExtJo.put("show_img_uid_list", jsonArray);
                }
            }
            if (jsonObject.has("extra_return_coin")) {
                newExtJo.put("extra_return_coin", jsonObject.optInt("extra_return_coin", 0));
            }
            if (jsonObject.has("combo_desc")) {
                newExtJo.put("combo_desc", jsonObject.getString("combo_desc"));
            }
            setExtension(newExtJo.toString());
        } catch (Exception e) {
            TimeLogger.err("error parse extra info");
        }
    }

    public int getBubbleId() {
        return bubbleId;
    }

    public void setBubbleId(int bubbleId) {
        this.bubbleId = bubbleId;
    }

    public void setExtensionWithRecall(String mid, long timestamp) {
        try {
            JSONObject obj = new JSONObject();
            obj.put("mid", mid);
            obj.put("recalled_msg_timestamp", timestamp);
            setExtension(obj.toString());
        } catch (Exception e) {
            HLog.d(this.getClass().getSimpleName(), e.toString());
        }
    }

    public int getFlowerNumFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("type") && obj.has("num")) {
                    int type = obj.getInt("type");
                    int flowerNum = obj.getInt("num");
                    if (isExtensionTypeGift(type)) return flowerNum;
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public boolean getIsPrivateFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                int is_public = obj.getInt("is_public");
                return is_public == BaseConfig.SERVER_NO;
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return false;
    }

    public int getGiftNumFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                int type = obj.getInt("type");
                int gift_num = obj.getInt("num");
                if (isExtensionTypeGift(type)) return gift_num;
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    @Nullable
    public GiftSendInfo getGiftSendInfoFromExtension() {
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (!obj.has("type")) {
                    return null;
                }
                int type = obj.getInt("type");
                if (isExtensionTypeGift(type)) {
                    int giftId = obj.getInt("gift_id");
                    int giftNum = obj.optInt("num");
                    int isPublic = obj.optInt("is_public");
                    String comboId = obj.optString("combo_id");
                    int comboTimes = obj.optInt("combo_times");
                    int isGiftCard = obj.optInt("is_gift_card");
                    GiftSendInfo sendInfo = new GiftSendInfo();
                    sendInfo.isPrivate = isPublic != BaseConfig.SERVER_YES;
                    sendInfo.isGiftCard = isGiftCard == 1;
                    sendInfo.giftNum = giftNum;
                    sendInfo.giftId = giftId;
                    sendInfo.comboId = comboId;
                    sendInfo.comboTimes = comboTimes;
                    sendInfo.recUid = recv_uid;
                    sendInfo.scene = Gift.GIFT_SCENE_CHAT;
                    return sendInfo;
                }
            } catch (Exception e) {
                TimeLogger.err("error parse extension");
            }
        }
        return null;
    }

    @Nullable
    public JudgeCardItem getExtJudgeCardItem() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                String imgUrl = obj.getString("card_img_url");
                String title = obj.getString("card_title");
                String content = obj.getString("card_content");
                String jumpUrl = obj.getString("card_jump_url");
                return new JudgeCardItem(imgUrl, title, content, jumpUrl);
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return null;
    }

    public int getGiftIdFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getInt("gift_id");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public int getPropIdFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getInt("prop_id");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public String getPwdFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getString("passwd");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return "";
    }

    public String getGiftRecCoinFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("give_away_desc")) {
                    return obj.getString("give_away_desc");
                }

            } catch (Exception e) {
            }
        }
        return "";
    }

    public int getGiftExtraReturnCoinFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.optInt("extra_return_coin", 0);
            } catch (Exception e) {
            }
        }
        return 0;
    }

    public String getPropInfoDescFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.optString("prop_info_desc", "");
            } catch (Exception e) {
            }
        }
        return "";
    }

    public String getComboIdFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("combo_id")) {
                    return obj.getString("combo_id");
                }

            } catch (Exception e) {
            }
        }
        return "";
    }

    public int getComboTimesFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("combo_times")) {
                    return obj.getInt("combo_times");
                }
            } catch (Exception e) {
            }
        }
        return 0;
    }

    public int getExtensionType() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("type")) return obj.getInt("type");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return EXTENSION_TYPE_JK_SKIN;
    }

    public int getExtensionSubType() {
        return getExtensionSubType(getExtension());
    }

    public static int getExtensionSubType(String extension) {
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("sub_type")) return obj.getInt("sub_type");
            } catch (Exception e) {
                //nothing
            }
        }
        return 0;
    }

    public int getExtensionCocosGameTid() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("tid")) {
                    return obj.getInt("tid");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public int getExtensionGameType() {
        int gameType = 0;
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                Iterator<String> iterator = obj.keys();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    if (BaseConstants.GAME_TYPE.equalsIgnoreCase(key) || "gameType".equalsIgnoreCase(key)) {
                        gameType = obj.getInt(key);
                        break;
                    }
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return gameType;
    }

    public int getExtensionXRoomGid() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("xgid")) {
                    return obj.getInt("xgid");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public int getExtensionCocosGameType() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("game_type")) {
                    return obj.getInt("game_type");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public int getExtensionSubTypeRid() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("rid")) return obj.getInt("rid");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public int getExtensionDonateId() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("donate_id")) {
                    return obj.getInt("donate_id");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return 0;
    }

    public String getExtensionComment() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("comment")) {
                    return obj.getString("comment");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return "";
    }

    public int getPacketSkinId() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("rp_skin_id")) {
                    return obj.getInt("rp_skin_id");
                }
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return -1;
    }

    public String getRedPacketIdFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getString("rp_id");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return "";
    }

    public int getRedPacketGrapedFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getInt("graped");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return RedPacket.STATE_UNGRAPED;
    }

    public int getRedPacketStateFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getInt("state");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return RedPacket.STATE_INIT;
    }

    public String getRedPacketDescFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                return obj.getString("msg");
            } catch (Exception e) {
                HLog.d(this.getClass().getSimpleName(), e.toString());
            }
        }
        return ResUtil.getStr(R.string.red_packet_default_desc);
    }

    public String getRedPacketPasswordFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension)) {
            try {
                JSONObject obj = new JSONObject(extension);
                if (obj.has("password")) return obj.getString("password");
                return "";
            } catch (Exception e) {
                HLog.e(this.getClass().getSimpleName(), e.toString());
            }
        }
        return "";
    }

    public boolean isPasswordPacket() {
        return getExtensionType() == EXTENSION_TYPE_PASSWORD_PACKET;
    }

    public boolean isMediaTypeRedPacket() {
        return media_type == WPMessage.MEDIA_TYPE_RED_PACKET;
    }

    public boolean isMediaTypeRedPacketTip() {
        return media_type == WPMessage.MEDIA_TYPE_RED_PACKET_TIP;
    }

    public UserIdCardInfo getSendCardFromExtension() {
        String extansion = getExtension();
        if (!TextUtils.isEmpty(extansion)) {
            try {
                return JsonUtil.fromJson(extansion, UserIdCardInfo.class);
            } catch (Exception e) {
                HLog.e(this.getClass().getSimpleName(), e.toString());
            }
        }
        return null;
    }

    private GameInviteInfo cacheInviteInfo = null;

    public GameInviteInfo getGameInviteFromExtension() {
        String extension = getExtension();
        if (!TextUtils.isEmpty(extension) && cacheInviteInfo == null) {
            try {
                cacheInviteInfo = JsonUtil.getGson().fromJson(extension, GameInviteInfo.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cacheInviteInfo;
    }

    public WPMessage() {
        this.status = 0;
        this.media_type = 1;
        this.time = System.currentTimeMillis();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    @Override
    public String getPrimaryKeyName() {
        return MID;
    }

    @Override
    public String getPrimaryKey() {
        return mid;
    }


    @Override
    public WPModel fromCursor(Cursor cursor) {
        WPMessage message = null;
        if (cursor.moveToNext()) {
            message = new WPMessage();
            message.setMid(CursorUtil.getString(cursor, MID));
            message.setMedia_type(CursorUtil.getInt(cursor, MEDIA_TYPE));
            message.setContent(CursorUtil.getString(cursor, CONTENT));
            message.setRecv_uid(CursorUtil.getInt(cursor, RECV_UID));
            message.setSend_uid(CursorUtil.getInt(cursor, SEND_UID));
            message.setStatus(CursorUtil.getInt(cursor, STATUS));
            message.setTime(CursorUtil.getLong(cursor, TIME));
            message.setExtension(CursorUtil.getString(cursor, EXTENSION));
            message.bubbleId = CursorUtil.getInt(cursor, BUBBLE_ID);
            message.inviteStatus = CursorUtil.getInt(cursor, INVITE_STATUS);
            message.subType = CursorUtil.getInt(cursor, SUB_TYPE);
            int refMidIndex = cursor.getColumnIndex(REF_MID);
            if (refMidIndex > 0 && refMidIndex < cursor.getColumnCount()) {
                message.ref_mid = cursor.getString(refMidIndex);
            }
        }
        return message;
    }

    @Override
    public ContentValues toContentValues() {
        ContentValues values = new ContentValues();
        values.put(CONTENT, content);
        values.put(MEDIA_TYPE, media_type);
        values.put(MID, mid);
        values.put(RECV_UID, recv_uid);
        values.put(SEND_UID, send_uid);
        values.put(STATUS, status);
        if (time != null) {
            values.put(TIME, time);
        }
        values.put(EXTENSION, extension);
        values.put(BUBBLE_ID, bubbleId);
        values.put(INVITE_STATUS, inviteStatus);
        values.put(SUB_TYPE, subType);
        values.put(REF_MID, ref_mid);
        return values;
    }

    public static WPMessage fromProto(GeneratedMessageLite<?, ?> message) {
        WPMessage msg = null;
        if (message != null) {
            if (message instanceof chat_pu_msg_chat) {
                chat_pu_msg_chat protMsg = (chat_pu_msg_chat) message;
                msg = new WPMessage();
                msg.send_uid = protMsg.getSendUid();
                msg.recv_uid = protMsg.getRecvUid();
                msg.mid = protMsg.getMid();
                msg.content = protMsg.getContent();
                msg.media_type = protMsg.getMediaType();
                msg.time = protMsg.getTime();
                msg.extension = protMsg.getExtension();
                msg.status = WPMessage.STATUS_OK;
                msg.bubbleId = protMsg.getBubbleId();
                msg.subType = protMsg.getSubType();
                msg.ref_mid = protMsg.getRefMid();
            } else if (message instanceof chat_pu_msg_newFriend) {
                chat_pu_msg_newFriend protMsg = (chat_pu_msg_newFriend) message;
                userInfoChatServer userInfo = protMsg.getExtraUserInfo();
                msg = new WPMessage();
                msg.send_uid = userInfo.getUid();
                msg.recv_uid = protMsg.getRecvUid();
                msg.mid = protMsg.getMid();
                msg.content = protMsg.getContent();
                msg.media_type = MEDIA_TYPE_TEXT;
                msg.time = protMsg.getTime();
                msg.status = WPMessage.STATUS_OK;
            }
        }
        return msg;
    }

    public static List<WPMessage> parseBatchMessageFromProto(GeneratedMessageLite<?, ?> message) {
        List<WPMessage> messages = new ArrayList<>();
        if (message instanceof chat_pu_msg_chatBatch) {
            chat_pu_msg_chatBatch batchMsg = (chat_pu_msg_chatBatch) message;
            int receiveUid = batchMsg.getRecvUid();
            List<msg_chat_info> msg_chat_infoList = batchMsg.getMsgChatListList();
            for (msg_chat_info chat_info : msg_chat_infoList) {
                messages.add(buildWPMessage(receiveUid, chat_info));
            }
        } else {
            HLog.d(TAG, HLog.USR, "parseBatchMessageFromProto error! msg=" + message);
        }
        return messages;
    }

    private static WPMessage buildWPMessage(int receiveUid, msg_chat_info protMsg) {
        WPMessage msg = new WPMessage();
        msg.send_uid = protMsg.getSendUid();
        msg.recv_uid = receiveUid;
        msg.mid = protMsg.getMid();
        msg.content = protMsg.getContent();
        msg.media_type = protMsg.getMediaType();
        msg.time = protMsg.getTime();
        msg.extension = protMsg.getExtension();
        msg.status = WPMessage.STATUS_OK;
        msg.bubbleId = protMsg.getBubbleId();
        msg.subType = protMsg.getSubType();
        msg.ref_mid = protMsg.getRefMid();
        return msg;
    }

    public boolean isExtensionTypeGift(int type) {
        return type == EXTENSION_TYPE_FLOWER || type == EXTENSION_TYPE_GIFT;
    }

    public boolean isExtensionTypeGameSkin(int type) {
        return type == EXTENSION_TYPE_JK_SKIN;
    }

    public void setExtensionWithGameInfo(GameInviteInfo gameInviteInfo) {
        if (gameInviteInfo == null) return;
        try {
            setExtension(JsonUtil.toJson(gameInviteInfo));
        } catch (Exception e) {
            HLog.e(TAG, e, e.toString());
        }
    }

    public boolean canRecall() {
        if (ConfigHelper.getInstance().msgRecallClose()) return false;
        boolean disableRecall = media_type == MEDIA_TYPE_DICE || media_type == MEDIA_TYPE_PHOTO ||
                media_type == MEDIA_TYPE_RED_PACKET || media_type == MEDIA_TYPE_RED_PACKET_TIP ||
                media_type == MEDIA_TYPE_SEND_CARD || media_type == MEDIA_TYPE_RECALL ||
                media_type == MEDIA_TYPE_SYSTEM || media_type == MEDIA_TYPE_VIP_DONATE ||
                media_type == MEDIA_TYPE_GAME_INVITE || media_type == MEDIA_TYPE_XROOM_INVITE ||
                (media_type == MEDIA_TYPE_TEXT && isExtensionTypeGift(getExtensionType()));
        if (disableRecall) {
            return false;
        }
        if (status == STATUS_OK || status == STATUS_VIEWED) {
            if (getSend_uid() == LoginHelper.getLoginUid()) {
                return TimeUtil.getElapsedServerTime() - getTime() < 2 * 60 * 1000;
            }
        }
        return false;
    }

    public boolean isEmoticon() {
        if (media_type == MEDIA_TYPE_NORMAL_PHOTO) {
            return subType != null && subType == WPMessage.SUB_TYPE_EMOTICON;
        }
        return false;
    }

    public String getRecallMid() {
        try {
            JSONObject jsonObject = new JSONObject(getExtension());
            return jsonObject.getString("mid");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return "";
    }

    public long getRecallTimeStamp() {
        try {
            JSONObject jsonObject = new JSONObject(getExtension());
            return jsonObject.getLong("recalled_msg_timestamp");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return 0;
    }

    @Override
    public String getGameInviteMid() {
        return mid;
    }

    @Override
    public int getGameInviteStatus() {
        return inviteStatus;
    }

    @Override
    public void setGameInviteStatus(int status) {
        this.inviteStatus = status;
    }

    public InviteCardInfo getWithInviteCardWithExtension() {
        return getWithInviteCardWithExtension(getExtension());
    }

    @NonNull
    @Override
    public String toString() {
        return "WPMessage{" +
                "mid='" + mid + '\'' +
                ", content='" + content + '\'' +
                ", send_uid=" + send_uid +
                ", recv_uid=" + recv_uid +
                ", time=" + time +
                ", status=" + status +
                ", media_type=" + media_type +
                ", extension='" + extension + '\'' +
                ", subType=" + subType +
                ", bubbleId=" + bubbleId +
                ", inviteStatus=" + inviteStatus +
                ", cacheInviteInfo=" + cacheInviteInfo +
                '}';
    }

    public static InviteCardInfo getWithInviteCardWithExtension(String extension) {
        return InviteCardInfo.build(extension);
    }

    public String getAvatarInviteMsgTypeFromExtension() {
        try {
            JSONObject jsonObject = new JSONObject(getExtension());
            return jsonObject.getString("msg_type");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return "";
    }

    public boolean getIsServerSendGiftFromExtension() {
        boolean isServerSendGift = false;
        try {
            JSONObject jsonObject = new JSONObject(getExtension());
            isServerSendGift = jsonObject.getBoolean("server_sent_gift");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return isServerSendGift;
    }

    public boolean isCanQuote() {
        boolean isForbidQuoteInviteType = isForbidQuoteInviteType();
        boolean isTips = media_type == MEDIA_TYPE_RED_PACKET_TIP || media_type == MEDIA_TYPE_RECALL ||
                media_type == MEDIA_TYPE_SYSTEM;
        boolean statusOK = status == ChatMsg.STATUS_OK || status == ChatMsg.STATUS_VIEWED || status == STATUS_VIDEO_BLOCKED;
        return (!isForbidQuoteInviteType && !isTips) && statusOK;
    }

    /**
     * 是否是禁止回复的邀请消息类型
     *
     */
    public boolean isForbidQuoteInviteType() {
        try {
            JSONObject jsonObject = new JSONObject(getExtension());
            int inviteType = jsonObject.optInt("invite_type");
            List<Integer> forbidReplyInviteType = ConfigHelper.getInstance().getConstConfig().getForbidReplyInviteType();
            return forbidReplyInviteType.contains(inviteType);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isVideoMsg() {
        return media_type == MEDIA_TYPE_CHAT_VIDEO;
    }

    public void updateContent(String content) {
        if (isVideoMsg()) {
            extension = content;
            this.content = ResUtil.getStr(R.string.common_room_not_support_msg);
        } else {
            this.content = content;
        }
    }

    public boolean isUseOrigin() {
        return useOrigin;
    }

    public void setUseOrigin(boolean useOrigin) {
        this.useOrigin = useOrigin;
    }
}
