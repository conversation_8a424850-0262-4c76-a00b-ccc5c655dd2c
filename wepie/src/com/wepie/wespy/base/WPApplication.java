package com.wepie.wespy.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.google.android.play.core.splitcompat.SplitCompat;
import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.BaseConfig;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.DrawableRecycleUtil;
import com.huiwan.base.util.LeakRefWatcher;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.ProcessUtil;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.SdkBaseStore;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.BaseUiUtil;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.weplay.login.helper.LoginStatisticsHelper;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;
import com.wepie.startup.Initializer;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.base.apm.ApmDepImpl;
import com.wepie.wespy.base.startup.InitializerUtils;
import com.wepie.wespy.base.startup.ProcessInitializerFactory;
import com.wepie.wespy.helper.WPHelper;
import com.wepie.wespy.module.match.util.AudioMatchFloatingPresenter;

import java.util.Map;

/**
 * 防止没有权限提前初始化的Did导致一些异常
 * Application不要有网络请求，不要调用，或者间接调用DeviceIdHelp.getDid
 */
public class WPApplication extends Application {

    private static final String TAG = "WPApplication";

    private static final String LANG_PREF = "pf_lang";
    private boolean isIsolatedProcess = false;
    private InitializerManager manager;

    @Override
    protected void attachBaseContext(Context base) {
        Apm.initDep(ApmDepImpl.INSTANCE);
        Apm.recordPeriod("app_launch", true);
        Apm.recordPeriod("WPApplication#attachBaseContext", true);
        manager = InitializerManager.create(this, BuildConfig.DEBUG, new InitializerManager.IErrorCallback() {
            @Override
            public void onLog(@NonNull String msg) {
                HLog.d("InitializerManager", HLog.USR, msg);
                FLog.d(msg);
            }

            @Override
            public void onCrash(@NonNull Exception e) {
                FLog.e(e);
            }

            @Override
            public void onTimeOut(@NonNull Initializer task, boolean isDone, long duration, @NonNull String msg) {
                Map<String, String> map = new ArrayMap<>();
                map.put("msg", msg);
                HLog.aliPerformance("startup", task.tag() + "(" + task.flags() + ")", isDone, duration, "", map);
            }
        });
        super.attachBaseContext(base);
        isIsolatedProcess = isIsolated(Process.myUid());
        if (isIsolatedProcess) {
            Log.e(TAG, "isolated process");
            return;
        }
        SplitCompat.install(this);
        Apm.recordPeriod("WPApplication#attachBaseContext", false);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Apm.recordPeriod("WPApplication#onCreate", true);
        String processName = ProcessUtil.getCurrentProcessName(this);
        if (isIsolatedProcess || isRestart(processName)) {
            Log.e(TAG, "isolated process");
            return;
        }
        boolean isMainProcess = ProcessUtil.isMainProcess(this);
        init(processName, isMainProcess);
        Apm.recordPeriod("WPApplication#onCreate:" + processName, false);
        if (!ProcessUtil.isMainProcess(this)) {
            Apm.disableTimeRecord();
        }
    }

    private void init(String processName, boolean isMainProcess) {
        Apm.recordPeriod("WPApplication#init", true);
        if (isMainProcess) {
            LoginHelper.setLoginTime(System.currentTimeMillis());
        }
        SdkBaseStore.init(this, BuildConfig.DEBUG);

        BaseConfig config = SDKInitUtil.initBaseConfig(this, processName, isMainProcess);
        LibBaseUtil.setLangFromPref(true);
        PrefUtil prefUtil = PrefUtil.getInstance();
        prefUtil.importFromSharedPreferences(LANG_PREF);
        LibBaseUtil.init(this, config, prefUtil.getPref(), new SDKInitUtil.BaseLogger());
        ResUtil.setListener(SplitCompat::install);
        manager.executorService(ThreadUtil.getCacheThreadPool());
        manager.addParam(InitializerUtils.KEY_IS_MAIN_PROCESS, isMainProcess);
        manager.addParam(InitializerUtils.KEY_PROCESS_NAME, processName);
        int uid = LoginHelper.getLoginUid();
        if (uid > 0) {
            manager.addParam(InitializerUtils.KEY_UID, uid);
        }
        manager.start();
        registerLifecycle();
        SDKInitUtil.initInAllProcesses(manager);

        ProcessInitializerFactory factory = ProcessInitializerFactory.get(this);
        if (factory != null) {
            factory.createInitializers(manager);
        }
        manager.initFromMetaData(this, processName);
        InitializerManagerUtils.init(manager);
        InitializerManagerUtils.finish();
        BaseUiUtil.INSTANCE.init(this);
        Apm.recordPeriod("WPApplication#init", false);
    }

    private void registerLifecycle() {
        ActivityTaskManager activityTaskManager = ActivityTaskManager.getInstance();
        activityTaskManager.registerActivityTaskListener(new ActivityTaskManager.ActivityTaskListener() {

            @Override
            public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
                WPHelper.updateScreen(activity);
                LoginStatisticsHelper.updateFBWebTag(false, activity);
                LittleGame.onRestoreInstance(savedInstanceState);
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                AudioMatchFloatingPresenter.checkJumpPendingActivity(activity);
                LibBaseUtil.checkUpdateLang();
                WPHelper.updateScreen(activity);
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
                super.onActivitySaveInstanceState(activity, outState);
                LittleGame.onSaveInstance(activity, outState);
            }
        });
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        if (ProcessUtil.isMainProcess(this)) {
            SVGAUtil.clearMemCache();
            WpImageLoader.trimMemory(level);
        }
        LeakRefWatcher.trimMemory(level);
        DrawableRecycleUtil.trimMemory(level);
    }

    public static boolean isIsolated(int uid) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            return Process.isIsolated();
        }
        return uid % 100000 >= 90000;
    }

    public static boolean isRestart(String processName) {
        return processName.endsWith(":restart");
    }
}
