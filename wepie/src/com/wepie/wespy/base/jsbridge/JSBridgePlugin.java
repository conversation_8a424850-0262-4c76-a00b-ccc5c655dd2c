package com.wepie.wespy.base.jsbridge;

import static com.huiwan.module.webview.WebViewBridgeInterface.generateErrorJson;
import static com.huiwan.module.webview.WebViewBridgeInterface.generateSuccessJson;
import static com.huiwan.module.webview.WebViewBridgeInterface.generateSuccessJson2;
import static com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt.SCENE_H5;

import android.Manifest;
import android.app.Activity;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.PersistableBundle;
import android.text.TextUtils;

import androidx.collection.ArrayMap;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.interfaces.IAsyncCallback;
import com.huiwan.base.interfaces.SingleCallback;
import com.huiwan.base.ktx.IntentExtKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ApplicationUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.GiftDialogCallback;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.send.GiftShowConfig;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.configservice.international.regoin.RegionUtil;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.constants.Common;
import com.huiwan.constants.IntentConfig;
import com.huiwan.decorate.UserDecorManager;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.module.webview.util.WebUtil;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.file.FileConfig;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.uploader.BucketType;
import com.wepie.lib.api.uploader.IUploadCallback;
import com.wepie.lib.api.uploader.SimpleFileUploader;
import com.wepie.lib.share.ShareHelper;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.libpermission.WPPermission;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.module.chat.ChatUtil;
import com.wepie.wespy.module.chat.send.ChatRecorder;
import com.wepie.wespy.module.chat.ui.group.GroupChatActivity;
import com.wepie.wespy.module.chat.ui.single.ChatActivity;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.common.jump.UserGameStatusUtil;
import com.wepie.wespy.module.contact.manager.FriendListUtil;
import com.wepie.wespy.module.family.FamilyLightNameUpdateEvent;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.shop.dialogs.PropDialogCallback;
import com.wepie.wespy.module.shop.dialogs.PropInfoConfig;
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.main.BaseVoiceRoomActivity;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomH5;
import com.wepie.wespy.net.risk.RiskCaptchaHelper;
import com.wepie.wespy.utils.CalendarUtil;
import com.wepie.wespy.utils.PackageUtil;
import com.wepie.wpdd.DeviceIdHelper;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JSBridgePlugin {
    private static final String TAG = "JSBridgePlugin";
    private static WebViewRecorder webViewRecorder;

    public static void registerWebApiExtHandler(WebApi api) {
        api.setUriInterceptor(JSBridgePlugin::interceptUrl);
        api.registerWebViewHandler("getUserInfo", (arg, callback) -> {
            String jsonString = "";
            try {
                HeadDecorExtra extra = UserDecorManager.getInstance().getUserDecor();
                String decorUrl = "";
                int decorType = 0;
                if (extra != null) {
                    decorUrl = extra.getAnimationUrl();
                    decorType = extra.getAnimationType();
                }

                JSONObject json = new JSONObject();
                User user = UserService.get().getLoginUser();
                json.put("uid", String.valueOf(user.getUid()));
                json.put("nickname", user.getNickname());
                json.put("avatar", user.getHeadimgurl() + "");
                json.put("sid", user.getSid());
                json.put("deviceId", DeviceIdHelper.getDid());
                json.put("region", GlobalConfigManager.getInstance().getRegion());
                json.put("gender", user.getGender());
                json.put("userDecorUrl", decorUrl);
                json.put("userDecorType", decorType);
                jsonString = json.toString();
            } catch (Exception e) {
                HLog.d(TAG, HLog.USR, "getUserInfo Exception={}", e);
            }
            callback.onCall(jsonString);
        });

        api.registerWebViewHandler("getRecentContact", (arg, callback) -> {
            String jsonString = "[]";
            try {
                List<FriendInfo> users = FriendListUtil.getRecentContactUser();
                JSONArray array = new JSONArray();
                int total = users.size();
                if (total > 200) {
                    total = 200;
                }
                for (int i = 0; i < total; i++) {
                    FriendInfo info = users.get(i);
                    UserSimpleInfo user = UserService.get().getCacheSimpleLocalUser(info.getUid());
                    JSONObject json = new JSONObject();
                    json.put("uid", info.getUid());
                    json.put("remark_name", info.getRealRemarkName());
                    if (user != null) {
                        json.put("nickname", user.nickname);
                        json.put("avatar", user.headimgurl);
                        json.put("gender", user.gender);
                    }
                    array.put(json);
                }
                jsonString = array.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
            callback.onCall(jsonString);
        });

        api.registerWebViewHandler("isAndroid", (arg, callback) -> callback.onCall("true"));

        api.registerWebViewHandler("jumpGame", (arg, callback) -> {
            int gameType = 0;
            try {
                gameType = Integer.parseInt(arg.argStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            JumpRoomUtil.jumpToLittleGame(gameType, arg.context, SCENE_H5);
            callback.onCall(generateSuccessJson());
        });

        api.registerWebViewHandler("jumpUserInfo", (handlerArg, callback) -> {
            int uid = 0;
            try {
                uid = Integer.parseInt(handlerArg.argStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            JumpUtil.enterUserInfoDetailActivity(handlerArg.context, uid, TrackSource.WEB);
            callback.onCall(generateSuccessJson());

        });


        api.registerWebViewHandler("jumpTopic", (handlerArg, callback) -> {
            int topicId = 0;
            try {
                topicId = Integer.parseInt(handlerArg.argStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            JumpUtil.gotoSquareTopicActivity(handlerArg.context, topicId);
            callback.onCall(generateSuccessJson());
        });

        api.registerWebViewHandler("jumpDeepLink", (handlerArg, callback) -> {
            try {
                HLog.d(TAG, HLog.USR, "jumpDeepLink!  arg=" + handlerArg.argStr);
                String url = URLDecoder.decode(handlerArg.argStr, "UTF-8");
                Uri uri = Uri.parse(url);
                String source = getSource(uri);
                String scene = getSubSource(uri);
                // 不确定其它Schema是否会有影响，这里其它的Schema保持原来逻辑
                if (handlerArg.argStr.contains("http?url=")) {
                    url = handlerArg.argStr;
                }
                JumpCommon.gotoOtherPager(handlerArg.context, url, source, scene, null);
                callback.onCall(generateSuccessJson());
            } catch (Exception e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("closeWindow", (handlerArg, callback) -> {
            Activity activity = ContextUtil.getActivityFromContext(handlerArg.context);
            try {
                if (activity != null && !activity.isFinishing()) {
                    activity.finish();
                }
                callback.onCall(generateSuccessJson());
            } catch (Exception e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("getAppInfo", (handlerArg, callback) -> {
            String jsonString = "{}";
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("versionName", PackageUtil.getCommonVersionName());
                jsonObject.put("packageName", PackageUtil.getPackageName());
                jsonObject.put("channel", PackageUtil.getChannel());
                jsonString = jsonObject.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
            callback.onCall(jsonString);
        });

        api.registerWebViewHandler("previewProp", new IAsyncCallback<WebApi.HandlerArg, String>() {
            @Override
            public void onCall(WebApi.HandlerArg handlerArg, SingleCallback<String> callback) {
                int propId = 0;
                try {
                    propId = Integer.parseInt(handlerArg.argStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                Activity activity = ActivityTaskManager.getInstance().getTopActivity();
                if (activity != null) {
                    PropInfoConfig config = PropInfoConfig.fromOutOfShop(propId);
                    config.setPreview(true);
                    config.setBpPreview(true);
                    PropInfoDialog.show(activity, config, new PropDialogCallback() {
                        @Override
                        public void onDismiss() {
                            super.onDismiss();
                        }
                    });
                    callback.onCall(generateSuccessJson());
                } else {
                    callback.onCall(generateErrorJson("activity error"));
                }
            }
        });

        //音频录制+播放
        api.registerWebViewHandler("startRecords", new IAsyncCallback<WebApi.HandlerArg, String>() {
            @Override
            public void onCall(WebApi.HandlerArg handlerArg, final SingleCallback<String> callback) {
                Activity activity = ContextUtil.getActivityFromContext(handlerArg.context);
                if (activity == null) {
                    callback.onCall(generateErrorJson("context error"));
                    return;
                }

                if (UserGameStatusUtil.checkUserStatus(activity, UserGameStatusUtil.TYPE_CHECK_VOICE)) {
                    callback.onCall(generateErrorJson(R.string.js_record_state_err));
                    return;
                }

                if (PermissionUtil.hasPermission(activity, Manifest.permission.RECORD_AUDIO)) {
                    handlerRecords(callback);
                } else {
                    WPPermission.with(activity)
                            .permission(Manifest.permission.RECORD_AUDIO)
                            .requestDialogTip(ResUtil.getResource().getString(R.string.js_req_permission_record))
                            .request(new PermissionCallback() {
                                @Override
                                public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                                    handlerRecords(callback);
                                }

                                @Override
                                public void noPermission(List<String> denied, boolean quick) {
                                    callback.onCall(generateErrorJson(R.string.permission_denied_no_record));
                                }
                            });
                }
            }

            public void handlerRecords(final SingleCallback<String> callback) {
                if (webViewRecorder == null) webViewRecorder = new WebViewRecorder();
                int code = webViewRecorder.startRecord();
                if (code == 1) {
                    callback.onCall(generateSuccessJson("path", webViewRecorder.getRecordPath()));
                } else {
                    if (code == 0) {
                        callback.onCall(generateErrorJson(R.string.record_failed_check_permission));
                    } else if (code == -1) {
                        callback.onCall(generateErrorJson(R.string.js_recording));
                    } else {
                        callback.onCall(generateErrorJson(R.string.record_failed));
                    }
                }
            }

            @Override
            public void onCancel() {
                release();
            }
        });

        api.registerWebViewHandler("stopRecords", (handlerArg, callback) -> {
            if (webViewRecorder != null) {
                String path = webViewRecorder.stopRecord();
                long duration = ChatRecorder.getAudioTime(handlerArg.context, path);
                callback.onCall(generateSuccessJson2("path", path, "duration", String.valueOf(duration)));
            } else {
                callback.onCall(generateErrorJson("webViewRecorder = null"));
            }
        });

        api.registerWebViewHandler("playMusic", (handlerArg, callback) -> {
            String path = "";
            try {
                JSONObject jsonObject = new JSONObject(handlerArg.argStr);
                path = jsonObject.getString("path");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (TextUtils.isEmpty(path)) {
                callback.onCall(generateErrorJson("path empty"));
                return;
            }
            if (!FileUtil.fileExists(path)) {
                callback.onCall(generateErrorJson(R.string.file_not_exist));
                return;
            }
            BgMusicManager.getInstance().playLocal(MusicSource.SOURCE_WEB_VIEW_AUDIO, path, false);
            callback.onCall(generateSuccessJson());
        });

        api.registerWebViewHandler("stopMusic", (handlerArg, callback) -> {
            String path = "";
            try {
                JSONObject jsonObject = new JSONObject(handlerArg.argStr);
                path = jsonObject.getString("path");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            BgMusicManager.getInstance().stop(path);
            callback.onCall(generateSuccessJson());
        });

        //上传
        api.registerWebViewHandler("uploadFile", (handlerArg, callback) -> {
            String path = "";
            String qn_key = null;
            try {
                JSONObject jsonObject = new JSONObject(handlerArg.argStr);
                path = jsonObject.getString("path");
                qn_key = jsonObject.getString("qn_key");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (TextUtils.isEmpty(path)) {
                callback.onCall(generateErrorJson("path empty"));
                return;
            }
            if (!FileUtil.fileExists(path)) {
                callback.onCall(generateErrorJson(R.string.file_not_exist));
                return;
            }
            SimpleFileUploader.upload(BucketType.webView, path, qn_key, new IUploadCallback() {
                @Override
                public void onSuccess(String localPath, String url) {
                    callback.onCall(generateSuccessJson("url", url));
                }

                @Override
                public void onFailed(int code, String msg) {
                    callback.onCall(generateErrorJson(msg));
                }
            });
        });

        api.registerWebViewHandler("addReminder", (handlerArg, callback) -> {
            try {
                JSONObject jsonObject = new JSONObject(handlerArg.argStr);
                String title = jsonObject.getString("title");
                long start = jsonObject.getLong("start");
                long end = jsonObject.getLong("end");
                long remind = jsonObject.getLong("remind");
                Activity activity = ContextUtil.getActivityFromContext(handlerArg.context);
                if (activity == null) {
                    callback.onCall(generateErrorJson("context error"));
                    return;
                }
                CalendarUtil.addCalendarNotice(activity, start, end, (remind - start) / 1000 / 60, title);
            } catch (JSONException e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson("json parse error"));
            }
        });

        api.registerWebViewHandler("launchWXMiniProgram", (handlerArg, callback) -> {
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject(handlerArg.argStr);
                String miniProgramId = jsonObject.getString("userName");
                String path = jsonObject.getString("path");
                int miniprogramType = jsonObject.getInt("miniprogramType");
                int result = -1;
                if (result == 0) {
                    callback.onCall(generateSuccessJson());
                } else if (result == -1) {
                    callback.onCall(generateErrorJson(R.string.app_id_not_init));
                } else if (result == -2) {
                    callback.onCall(generateErrorJson("WeChat not install"));
                } else if (result == -3) {
                    callback.onCall(generateErrorJson(R.string.open_failed));
                } else {
                    callback.onCall(generateErrorJson(R.string.open_failed));
                }
            } catch (Exception e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("getStatusBarHeight", (handlerArg, callback) -> {
            float height = ScreenUtil.px2dp(ScreenUtil.getStatusBarHeight());
            String json = generateSuccessJson("statusBarHeight", String.valueOf(height));
            callback.onCall(json);
        });
        api.registerWebViewHandler("restoreIap", ((handlerArg, callback) -> {
            ApiService.of(IapApi.class).tryRestore();
            String json = generateSuccessJson();
            callback.onCall(json);
        }));
        api.registerWebViewHandler("GetServerHost", (handlerArg, callback) -> {
            String json = generateSuccessJson("result", GlobalConfigManager.getInstance().getConfigs());
            callback.onCall(json);
        });
        api.registerWebViewHandler("setLuckyNumber", (handlerArg, callback) -> {
            UserService service = UserService.get();
            User user = service.getLoginUser();
            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(handlerArg.argStr);
                if (jsonObject.has("luckyNumber")) {
                    String wdId = jsonObject.getString("luckyNumber");
                    user.setWdid(wdId);
                }
                if (jsonObject.has("luckyLevel")) {
                    user.luckyIdLevel = jsonObject.getInt("luckyLevel");
                }
                service.saveSelfUser(user);
                callback.onCall(generateSuccessJson());
                service.getCacheSimpleUserFromServer(user.uid, null);
            } catch (JSONException e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });
        api.registerWebViewHandler("getDeviceInfo", (arg, callback) -> {
            String jsonString = "";
            try {
                JSONObject json = new JSONObject();
                json.put("android_version", Build.VERSION.SDK_INT);
                json.put("mobileModel", Build.MODEL);
                json.put("totalMemory", ApplicationUtil.getTotalMemory());
                json.put("availableMemory", ApplicationUtil.getAvailMemory());
                json.put("deviceId", DeviceIdHelper.getDid());
                jsonString = json.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
            callback.onCall(jsonString);
        });
        api.registerWebViewHandler("updateFamilyLightBoardContent", (arg, callback) -> {
            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(arg.argStr);
                if (jsonObject.has("title")) {
                    String familyLightName = jsonObject.getString("title");
                    notifyFamilyFlightUpdate(familyLightName);
                }
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });
        api.registerWebViewHandler("report", (arg, callback) -> {
            JSONObject jsonObject;
            ReportBuilder reportBuilder = ReportBuilder.newBuilder();
            try {
                jsonObject = new JSONObject(arg.argStr);
                if (jsonObject.has("targetUid")) {
                    int targetUid = jsonObject.getInt("targetUid");
                    reportBuilder.setTargetUid(targetUid);
                }
                if (jsonObject.has("reportType")) {
                    int reportType = jsonObject.getInt("reportType");
                    reportBuilder.setReportType(reportType);
                }
                if (jsonObject.has("source")) {
                    String source = jsonObject.getString("source");
                    reportBuilder.setSource(source);
                }
                if (jsonObject.has("activityId")) {
                    int activityId = jsonObject.getInt("activityId");
                    reportBuilder.setActivityId(activityId);
                }
                if (jsonObject.has("extra_content")) {
                    String extraContent = jsonObject.getString("extra_content");
                    reportBuilder.setExtContent(extraContent);
                }
                JumpUtil.gotoReportMainActivity(arg.context, reportBuilder);
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                e.printStackTrace();
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("copyClipboard", (arg, callback) -> {
            try {
                JSONObject jsonObject = new JSONObject(arg.argStr);
                String content = jsonObject.optString("content", "");
                copyToClipboard(arg.context, content);
                callback.onCall(generateSuccessJson());
            } catch (Exception e) {
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("uploadImage", (arg, callback) -> {
            try {
                JSONObject args = new JSONObject(arg.argStr);
                ThreadUtil.runInOtherThread(() -> {
                    String base64 = args.optString("base64", "");
                    Bitmap bitmap = ShareHelper.getShareBitmap(base64);
                    if (bitmap == null) {
                        ThreadUtil.runOnUiThread(() -> callback.onCall(generateErrorJson("save image err")));
                        return;
                    }
                    String path = FileUtil.saveImageToLocal(bitmap, FileConfig.getImageCacheDirPath());
                    if (TextUtils.isEmpty(path)) {
                        ThreadUtil.runOnUiThread(() -> callback.onCall(generateErrorJson("save image err")));
                        return;
                    }
                    SimpleFileUploader.upload(BucketType.webView, path, new IUploadCallback() {
                        @Override
                        public void onSuccess(String localPath, String url) {
                            callback.onCall(generateSuccessJson("url", url));
                            FileUtil.safeDeleteFile(path);
                        }

                        @Override
                        public void onFailed(int code, String msg) {
                            callback.onCall(generateErrorJson(msg));
                            FileUtil.safeDeleteFile(path);
                        }
                    });
                });
            } catch (JSONException e) {
                callback.onCall(generateErrorJson(e.getMessage()));
            }
        });

        api.registerWebViewHandler("goVoiceRoomGift", (handlerArg, callback) -> {
            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(handlerArg.argStr);
                String rid = "";
                if (jsonObject.has("rid")) {
                    rid = jsonObject.getString("rid");
                }
                int giftId = 0;
                if (jsonObject.has("gift_id")) {
                    giftId = jsonObject.getInt("gift_id");
                }
                String enterRoomSource = "";
                if (jsonObject.has("enter_room_source")) {
                    enterRoomSource = jsonObject.getString("enter_room_source");
                }
                if (TextUtils.isEmpty(rid)) {
                    //开通高级语音房
                    JumpUtil.gotoSuperiorRoomDetailActivity(handlerArg.context);
                } else {
                    //进入高级语音房并打开礼物面板
                    gotoAdvanceRoom(handlerArg.context, rid, giftId, enterRoomSource);
                }
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("goSingleChatGift", (handlerArg, callback) -> {
            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(handlerArg.argStr);
                int recvUid = jsonObject.getInt("recv_uid");
                int giftId = jsonObject.getInt("gift_id");
                boolean isFriend = FriendInfoCacheManager.getInstance().isFriend(recvUid);
                if (isFriend) {
                    goSingleChatAndShowGiftBroad(recvUid, giftId);
                    callback.onCall(generateSuccessJson());
                } else {
                    callback.onCall(generateErrorJson("user is not friend yet!"));
                }
            } catch (JSONException e) {
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("openInBrowser", (handlerArg, callback) -> {
            HLog.d(TAG, HLog.USR, "openInBrowser, argStr=" + handlerArg.argStr);
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                String url = data.optString("url");
                WebUtil.openInBrowser(handlerArg.context, url);
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "openInBrowser error! msg=" + e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        //根据道具id获取道具详情
        api.registerWebViewHandler("getPropsByIds", (handlerArg, callback) -> {
            HLog.e(TAG, HLog.USR, "getPropsByIds, argStr=" + handlerArg.argStr);
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                JSONArray propIds = data.optJSONArray("propIds");
                if (propIds == null) {
                    callback.onCall(generateSuccessJson());
                    return;
                }

                List<PropItem> propList = new ArrayList<>();
                for (int index = 0; index < propIds.length(); index++) {
                    int propId = propIds.optInt(index, 0);
                    PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(propId);
                    if (propItem != null) {
                        propList.add(propItem);
                    }
                }
                callback.onCall(generateSuccessJson("item_list", propList.toString()));
            } catch (JSONException e) {
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        //h5全屏弹窗
        api.registerWebViewHandler("openNewWebView", (handlerArg, callback) -> {
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                String url = data.getString("url");
                //还有一个isExternalLink字段，Android暂时没用到，后续有需求可以加上
                if (TextUtils.isEmpty(url)) return;
                VoicePluginService.getPlugin(IRoomH5.class).show(url, ScreenUtil.getScreenHeight());
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "openNewWebView error! msg=" + e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("openNewWebViewPage", (handlerArg, callback) -> {
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                String url = data.getString("url");
                HLog.d(TAG, HLog.USR, "openNewWebViewPage, url=" + url);
                if (TextUtils.isEmpty(url)) return;
                ApiService.of(WebApi.class).gotoWebActivity(handlerArg.context, url);
                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "openNewWebView error! msg=" + e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        //风控拦截--二次验证
        api.registerWebViewHandler("sendCaptchaCode", (handlerArg, callback) -> {
            HLog.d(TAG, HLog.USR, "sendCaptchaCode, argStr={}", handlerArg.argStr);
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                String captchaCode = data.getString("captchaCode");
                int captchaType = data.getInt("captchaType");
                String receiveAccount = data.getString("receiveAccount");
                if (captchaType == Common.RISK_VERIFICATION_TYPE_EMAIL) {
                    // 2024/10/8 跳转系统邮箱
                    openEmail(handlerArg.context, receiveAccount, captchaCode);
                } else if (captchaType == Common.RISK_VERIFICATION_TYPE_PHONE_MSG) {
                    // 2024/10/8 跳转系统短信
                    sendSms(handlerArg.context, receiveAccount, captchaCode);
                } else if (captchaType == Common.RISK_VERIFICATION_TYPE_WHATSAPP) {
                    // 2024/12/23 跳转WhatsApp
                    sendWhatsAppSms(handlerArg.context, receiveAccount, captchaCode);
                } else {
                    ToastUtil.show(ResUtil.getStr(R.string.captcha_code_not_support));
                }

                callback.onCall(generateSuccessJson());
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "sendCaptchaCode error! msg={}", e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("getAreaCode", (handlerArg, callback) -> {
            HLog.d(TAG, HLog.USR, "sendCaptchaCode, argStr={}", handlerArg.argStr);
            String areaCode = ApiService.of(HwApi.class).getSimAreaCode();
            AreaConfig.Area areaConfig = RegionUtil.getAreaConfigByAreaCode(areaCode);
            String iconUrl = areaConfig.getAreaUrl();
            callback.onCall(generateSuccessJson2("areaCode", areaCode, "iconUrl", iconUrl));
        });

        api.registerWebViewHandler("chooseAreaCode", (handlerArg, callback) -> {
            Activity curActivity = ContextUtil.getFragmentActivityFromContext(handlerArg.context);
            if (curActivity == null) {
                callback.onCall(generateErrorJson("handlerArg context is null"));
                return;
            }
            ApiService.of(HwApi.class).selectLocal(curActivity, ActivityResultCode.REQUEST_H5_SELECT_AREA);

            ActivityTaskBuilderHolder.get().register(activity -> new ActivityTask() {
                @Override
                public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                    if (ActivityResultCode.REQUEST_H5_SELECT_AREA == requestCode
                            && Activity.RESULT_OK == resultCode) {
                        String areaCode = data.getStringExtra(IntentConfig.AREA_CODE);
                        String iconUrl = data.getStringExtra(IntentConfig.AREA_URL);
                        callback.onCall(generateSuccessJson2("selectAreaCode", areaCode, "iconUrl", iconUrl));
                    }
                }
            });
        });

        api.registerWebViewHandler("verifySuccess", (handlerArg, callback) -> {
            HLog.d(TAG, HLog.USR, "verifySuccess, argStr={}", handlerArg.argStr);
            String verifyInfo = handlerArg.argStr;
            RiskCaptchaHelper.INSTANCE.tryRequestAgainPost(verifyInfo);
        });

        api.registerWebViewHandler("showCoinBuyAlert", (handlerArg, callback) -> {
            try {
                JSONObject data = new JSONObject(handlerArg.argStr);
                int currencyType = data.optInt("currency_type", 0); // 0 表示钻石，1 表示金币
                int scene = data.optInt("scene", 0); // 0 表示普通场景，1 表示靓号
                HashMap<String, Object> trackParam = new HashMap<>();
                trackParam.put("gameid", data.optInt("gameid", -1));
                trackParam.put("refer_screen_name", data.optString("refer_screen_name", ""));
                DialogUtil.showPublicMoneyNotEnough(currencyType == 1, scene, trackParam, () -> callback.onCall(generateSuccessJson()));
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "sendCaptchaCode error! msg={}", e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });

        api.registerWebViewHandler("showGiftBoard", (handlerArg, callback) -> {
            HLog.d(TAG, HLog.USR, "showGiftBoard, argStr={}", handlerArg.argStr);
            JSONObject data;
            try {
                data = new JSONObject(handlerArg.argStr);
                int uid = data.optInt("uid");
                int challengeId = data.optInt("challengeId");
                boolean isMultiSend = data.optBoolean("isMultiSend");
                int selectCategoryId = data.optInt("selectCategoryId");
                int scene = data.optInt("scene");
                int trackScene = data.optInt("trackScene");
                String source = data.optString("source");
                String subSource = data.optString("sub_source");
                int selectGiftId = data.optInt("selectGiftId");
                int rid = data.optInt("rid");

                // 联盟群聊送礼 这里拿不到group_id, 只能调GroupChatActivity
                Activity topActivity = ActivityTaskManager.getInstance().getTopActivity();
                if (rid == 0 && topActivity instanceof GroupChatActivity groupChatActivity) {
                    groupChatActivity.sendGift(uid, scene, source, subSource, isMultiSend, selectGiftId);
                } else {
                    GiftShowConfig config = GiftShowConfigHelper.buildGiftShowConfig(
                            uid, scene, source, subSource, isMultiSend, selectGiftId, rid);
                    Context context = handlerArg.context;
                    showGiftView(context, config, callback);
                }
            } catch (JSONException e) {
                HLog.d(TAG, HLog.USR, "showGiftBoard error! msg={}", e);
                callback.onCall(generateErrorJson(e.toString()));
            }
        });
    }

    private static void goSingleChatAndShowGiftBroad(int recvUid, int giftId) {
        Activity topActivity = ActivityTaskManager.getInstance().getTopActivity();
        if (topActivity != null) {
            JumpUtil.gotoChatActivityAndShowGiftBoard(topActivity, recvUid, giftId);
        }
    }

    public static void copyToClipboard(Context context, String content) {
        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("info", content);
        // If your app is compiled with the API level 33 SDK or higher.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                PersistableBundle bundle = clip.getDescription().getExtras();
                if (null == bundle) {
                    bundle = new PersistableBundle();
                }
                bundle.putBoolean(ClipDescription.EXTRA_IS_SENSITIVE, true);
                clip.getDescription().setExtras(bundle);
            }
        }
        clipboard.setPrimaryClip(clip);
    }

    private static String interceptUrl(String oriUrl) {
        if (LibBaseUtil.isBuildOverSeas()) {
            Map<String, String> map = new ArrayMap<>();
            String lang = LibBaseUtil.getLang().value;
            map.put("lang", lang);
            map.put("region", GlobalConfigManager.getInstance().getRegion());
            map.put("prefer_lang", PrefUtil.getInstance().getString(PrefUtil.PREFER_LANG, lang));
            return appendUrlKeyValueIfNoKey(oriUrl, map);
        }
        return oriUrl;
    }

    private static String appendUrlKeyValueIfNoKey(String url, Map<String, String> kv) {
        try {
            Uri uri = Uri.parse(url);
            char spilt = '?';
            if (url.indexOf('?') > 0) {
                spilt = '&';
            }
            StringBuilder builder = new StringBuilder(url);
            for (Map.Entry<String, String> entry : kv.entrySet()) {
                String key = entry.getKey();
                String keyValue = uri.getQueryParameter(key);
                if (TextUtils.isEmpty(keyValue)) {
                    builder.append(spilt).append(key).append("=").append(entry.getValue());
                }
                if (spilt == '?') {
                    spilt = '&';
                }
            }
            return builder.toString();
        } catch (Exception e) {
            HLog.d(TAG, "error interceptUrl: {}", e);
        }
        return url;
    }

    private static String getSource(Uri uri) {
        String source = null;
        try {
            source = uri.getQueryParameter("source");
        } catch (Exception e) {
            //ignore
        }
        if (null == source) {
            source = TrackSource.WEB;
        }
        return source;
    }

    /**
     * 获取二级的source
     */
    private static String getSubSource(Uri uri) {
        String source = null;
        try {
            source = uri.getQueryParameter("sub_source");
        } catch (Exception e) {
            //ignore
        }
        if (null == source) {
            source = "";
        }
        return source;
    }

    private static void release() {
        if (webViewRecorder != null) {
            webViewRecorder.stopRecord();
            webViewRecorder = null;
        }
        BgMusicManager.getInstance().stop(MusicSource.SOURCE_WEB_VIEW_AUDIO);
    }

    public static void gotoAdvanceRoom(Context context, String rid, int giftId, String enterRoomSource) {
        EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildSearchRoom(context, rid);
        if (!TextUtils.isEmpty(enterRoomSource)) {
            enterRoomInfo.setSource(enterRoomSource);
        }
        enterRoomInfo.setDefaultSelectedGiftId(giftId);
        JumpRoomUtil.getInstance().searchRoom(enterRoomInfo, JumpRoomUtil.TMP_ROOM_SCENE);
    }

    private static void notifyFamilyFlightUpdate(String familyLightName) {
        EventBus.getDefault().post(new FamilyLightNameUpdateEvent(familyLightName));
    }

    private static void openEmail(Context context, String receiveAccount, String captchaCode) {
        Uri uri = Uri.parse("mailto:" + receiveAccount + "?subject=" + captchaCode);
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO, uri);
        emailIntent.putExtra(Intent.EXTRA_EMAIL, new String[]{receiveAccount});
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, captchaCode);
        boolean safelyStart = IntentExtKt.safelyStart(Intent.createChooser(emailIntent, ResUtil.getStr(R.string.choose_email)), context);
        if (!safelyStart) {
            HLog.d(TAG, HLog.USR, "openEmail startActivity error");
            FLog.e(new Throwable("openEmail startActivity error"));
        }
    }

    private static void sendSms(Context context, String receiveAccount, String captchaCode) {
        Intent smsIntent = new Intent(Intent.ACTION_SENDTO);
        smsIntent.setData(Uri.parse("smsto:" + receiveAccount));
        smsIntent.putExtra("sms_body", captchaCode);

        if (!IntentExtKt.safelyStart(smsIntent, context)) {
            HLog.d(TAG, HLog.USR, "sendSms! no application to send...");
            ToastUtil.show(ResUtil.getStr(R.string.captcha_no_application_to_send));
        }
    }

    private static void sendWhatsAppSms(Context context, String receiveAccount, String captchaCode) {
        Uri uri = Uri.parse("smsto:" + receiveAccount);
        Intent intent = new Intent(Intent.ACTION_SENDTO, uri);
        intent.setPackage("com.whatsapp");
        intent.putExtra("sms_body", captchaCode);

        if (!IntentExtKt.safelyStart(intent, context)) {
            HLog.d(TAG, HLog.USR, "sendWhatsAppSms! no application to send...");
            ToastUtil.show(ResUtil.getStr(R.string.uninstall_app, ResUtil.getStr(R.string.whatsapp)));
        }
    }

    private static void showGiftView(Context context, GiftShowConfig config, SingleCallback<String> callback) {
        GiftAnimUtil.showGiftView(context, config, new GiftDialogCallback() {
            @Override
            public void hideDialog() {
                callback.onCall(generateSuccessJson("isSuccess", "false"));
            }

            @Override
            public void onGiftSend(GiftSendInfo sendInfo) {
                sendInfo.subSource = config.subSource;
                ShenceGiftUtil.reportSendGift(sendInfo, config.source);
                if (config.rid > 0) { // 走语音房送礼
                    RoomSenderPresenter.checkSendRoomGift(sendInfo, new RoomCallback(context) {
                        @Override
                        public void onSuccess(int rid) {
                            callback.onCall(generateSuccessJson("isSuccess", "true"));
                            Activity activity = ContextUtil.getActivityFromContext(context);
                            if (activity instanceof BaseVoiceRoomActivity) {
                                ((BaseVoiceRoomActivity) activity).setComboViewData(sendInfo);
                            }
                        }

                        @Override
                        public void onFail(String msg) {
                            callback.onCall(generateSuccessJson("isSuccess", "false"));
                        }
                    });
                } else {
                    ChatUtil.sendGiftMsg(sendInfo, new HWTCPSocketThread.WriteCallback() {
                        @Override
                        public void onWriteSuccess() {
                            callback.onCall(generateSuccessJson("isSuccess", "true"));
                        }

                        @Override
                        public void onWriteFailed() {
                            callback.onCall(generateSuccessJson("isSuccess", "false"));
                        }
                    });
                }
            }
        });
    }
}
