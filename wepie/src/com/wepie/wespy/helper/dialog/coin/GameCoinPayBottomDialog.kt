package com.wepie.wespy.helper.dialog.coin

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.huiwan.base.str.ResUtil
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.lib.api.ApiService
import com.huiwan.user.UserService
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.module.pay.commonapi.GoodsListRvHelper
import com.wepie.wespy.module.pay.commonapi.WpPayResult
import com.wepie.wespy.net.http.api.OrderApi
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class GameCoinPayBottomDialog : BaseDialogFragment() {
    private lateinit var closeBtn: View
    private lateinit var coinTv: TextView
    private lateinit var titleTv: TextView
    private lateinit var helper: GoodsListRvHelper

    private var extTrackMap:Map<String, Any>? = null

    private val flow = MutableSharedFlow<Int>()

    private fun init() {
        initFullWidth()
        initBottom()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(getLayoutId(), container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        initEvent()
        updateData()
    }

    private fun initView(view: View) {
        view.apply {
            closeBtn = findViewById(R.id.close_iv)
            coinTv = findViewById(R.id.coin_tv)
            titleTv = findViewById(R.id.title_tv)
            titleTv.text = ResUtil.getStr(R.string.gold_bottom_buy_title)
            val firstChargeIv: ImageView = findViewById(R.id.first_charge_bg_iv)
            firstChargeIv.isVisible = false
            helper = GoodsListRvHelper(
                findViewById(R.id.goods_list_rv),
                getSpanCount()
            )
            helper.addTrackData(mapOf("screen_name" to TrackScreenName.PAY_DIALOG))
            helper.addTrackData(extTrackMap)
            coinTv.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.game_chip, 0, 0, 0
            )
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        MainScope().launch {
            flow.emit(0)
        }
    }

    private fun initEvent() {
        closeBtn.setOnClickListener {
            dismissAllowingStateLoss()
        }
        lifecycleScope.launch {
            val flow = ApiService.of(ChipApi::class.java).gameChipFlow() ?: return@launch
            flow.collectLatest {
                coinTv.text = it.toString()
            }
        }

        helper.setUpdateCallback(object : GoodsListRvHelper.UpdateCallback {
            override fun onSuccess(payResult: WpPayResult?) {
                MainScope().launch {
                    flow.emit(1)
                }
                dismissAllowingStateLoss()
            }

            override fun onFail() = Unit

            override fun onNeedUpdate() {
                updateData()
            }
        })
    }

    private fun updateData() {
        ApiService.of(ChipApi::class.java).updateChip()
        helper.refreshGameCoinList(OrderApi.GoodsType.GOLD)
    }


    protected fun getLayoutId(): Int {
        return R.layout.dialog_coin_not_enough_bottom
    }

    protected fun getSpanCount(): Int {
        return 3
    }

    companion object {
        @JvmStatic
        fun show(activity: FragmentActivity, trackData: MutableMap<String, Any> = mutableMapOf()): Flow<Int> {
            val fragment = GameCoinPayBottomDialog()
            fragment.init()
            fragment.show(activity.supportFragmentManager, "")
            if (trackData.isNotEmpty()) {
                val self = UserService.get().loginUser
                trackData["account_coin"] = self.chipCoin
                trackData["sub_screen_name"] = TrackScreenName.GOLD
                trackData["is_vip"] = self.isVip
                TrackUtil.appViewScreen(TrackScreenName.PAY_DIALOG, trackData)
                // js接口被调用时已包含了referScreenName字段，前提是web那边得传
                // gameid不一定有，因为可能是h5调用的（JSBridgePlugin.showCoinBuyAlert）
                fragment.extTrackMap = trackData
            }
            return fragment.flow
        }
    }

}