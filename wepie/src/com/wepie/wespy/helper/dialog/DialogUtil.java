package com.wepie.wespy.helper.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.JumpSystemSettingPageManagement;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.CustomTextView;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.share.IShareApi;
import com.wepie.lib.api.plugins.share.ShareCallback;
import com.wepie.lib.api.plugins.share.ShareInfo;
import com.wepie.lib.api.plugins.share.SharePlugin;
import com.wepie.lib.api.plugins.share.ShareType;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.TitleWebViewDialog;
import com.wepie.wespy.helper.dialog.coin.GameCoinPayBottomDialog;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayDialogActivity;
import com.wepie.wespy.helper.dialog.progress.IProgressDialog;
import com.wepie.wespy.helper.selecttime.SelectTimeView;
import com.wepie.wespy.helper.selecttime.TimeSelectListener;
import com.wepie.wespy.model.entity.AvatarImageInfo;
import com.wepie.wespy.model.entity.GameStateInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.contact.searchuser.AddFriendQRCodeDialog;
import com.wepie.wespy.module.game.game.base.SimpleDialogActivity;
import com.wepie.wespy.module.game.game.base.SimpleDialogBuilder;
import com.wepie.wespy.module.game.game.base.SimpleDialogManager;
import com.wepie.wespy.module.main.manage.GameRecoverActivity;
import com.wepie.wespy.module.pay.commonapi.GoodsListActivity;
import com.wepie.wespy.module.pay.commonapi.JumpPay;
import com.wepie.wespy.module.voiceroom.LittleGameRecoverActivity;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.util.DialogCallback;
import com.wepie.wespy.module.voiceroom.util.view.BaseViewDialog;
import com.wepie.wespy.module.voiceroom.util.view.ListViewDialog;
import com.wepie.wespy.net.http.api.PropApi;

import java.lang.ref.SoftReference;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by three on 15/3/10.
 */
public class DialogUtil {
    public static final String FRAG_BOTTOM = "frag_bottom_dialog";
    private static final Handler sHandler = new Handler(Looper.getMainLooper());
    private static boolean isInviteSpeakDialogShow;

    public static void showPublicVerifyIDDialog(String msg, String okBtn, String cancelBtn) {
        PublicTipIdVerifyActivity.show(LibBaseUtil.getApplication(), msg, okBtn, cancelBtn);
    }

    public static void showPublicNotifyDialogActivity(String imgUrl, String deeplink, String appScreen, String buttonName) {
        PublicNotifyDialogActivity.show(LibBaseUtil.getApplication(), imgUrl, deeplink, appScreen, buttonName);
    }

    public static void showPublicVerifyIDDialog(String msg) {
        showPublicVerifyIDDialog(msg, ResUtil.getStr(R.string.common_goto_verify), ResUtil.getStr(R.string.cancel));
    }

    public static void showCoinNotEnoughDialog(final Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_coin_not_enough, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.init();

        TextView payBt = view.findViewById(R.id.dialog_coin_pay_bt);
        payBt.setOnClickListener(v -> {
            JumpPay.showGoods(context);
            dialog.dismiss();
        });

        TextView cancelBt = view.findViewById(R.id.dialog_coin_pay_cancel);
        cancelBt.setOnClickListener(v -> dialog.dismiss());

        dialog.show();
    }

    public static void showPublicMoneyNotEnough(boolean subCoin) {
        showPublicMoneyNotEnough(subCoin, 0, Collections.emptyMap(), null);
    }

    public static void showPublicMoneyNotEnough(boolean subCoin, int scene, Map<String, Object> trackParam, Runnable finishTask) {
        Activity activity = ActivityTaskManager.getInstance().getTopActivity();
        if (activity instanceof FragmentActivity) {
            if (subCoin) {
                GameCoinPayBottomDialog.show((FragmentActivity) activity, trackParam);
            } else {
                PublicCoinPayBottomDialogView.showBottomDialog(activity, scene, trackParam, finishTask);
            }
        } else {
            showPublicCoinNotEnoughDialog();
        }
    }

    public static void showPublicCoinNotEnoughDialog() {
        showPublicCoinNotEnoughDialog(Collections.emptyMap());
    }

    public static void showPublicCoinNotEnoughDialog(Map<String, Object> map) {
        if (ActivityTaskManager.getInstance().isStack(GoodsListActivity.class)) {//买金币界面已经显示了
            ToastUtil.show(R.string.coin_not_enough);
            return;
        }
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, PublicCoinPayDialogActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
        if (!map.isEmpty()) {
            TrackUtil.appViewScreen(TrackScreenName.PAY_PAGE, map);
        }
    }

    public static void showGiftCardNotEnoughDialogInTop() {
        DialogShowConfig config = DialogShowConfig.config("", ResUtil.getString(R.string.send_fail_long_text_des1),
                ResUtil.getStr(R.string.sure), false, R.drawable.sel_ff92a9_ff4168_corner100);
        FragDialogSingleBtnTip.showDialogInTop(config, null);
    }

    public static void showDouDouNotEnough() {
//        DialogUtil.showDoubleBtnDialogActivity(null, "豆豆不足，是否去兑换更多的豆豆？", "兑换豆豆", ResUtil.getStr(R.string.cancel), new ActivityDialogCallback() {
//            @Override
//            public void onCancel(Context context) {
//
//            }
//
//            @Override
//            public void onEnter(Context context) {
//                DialogUtil.showExchangeGood();
//            }
//        });
    }

    public static void showForceUpdateDialog(String text, String btnText, String url) {
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, PublicForceUpdateDialogActivity.class);
        intent.putExtra("text", text);
        intent.putExtra("btnText", btnText);
        intent.putExtra("url", url);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void showAddFriendSuccessDialog(Context context, DialogBuild.DialogCallback callback) {
        DialogBuild.newBuilder(context).setSingleBtn(true)
                .setColorStateList(ResUtil.getColorStateList(R.color.sel_accent_common_selector))
                .setTitle(ResUtil.getStr(R.string.game_room_werewolf_add_success_notice))
                .setContent(ResUtil.getStr(R.string.game_room_werewolf_add_success_content))
                .setSureTx(ResUtil.getStr(R.string.game_room_werewolf_add_success_sure))
                .setDialogCallback(callback)
                .show();
    }

    public static void showConvertSuccessDialog(Context context, String msg, DialogBuild.DialogCallback callback) {
        DialogBuild.newBuilder(context).setSingleBtn(true).setTitle(null).setContent(msg).setDialogCallback(callback).show();
    }

    public static void showMsgCopyDeleteDialog(Context context, boolean canDelete, boolean canCopy,
                                               final DialogItemCallback callback) {
        String[] items = new String[1];
        if (canDelete && canCopy) {
            items = new String[]{ResUtil.getStr(R.string.common_copy), ResUtil.getStr(R.string.common_text_remove)};
        } else if (canDelete && !canCopy) {
            items = new String[]{ResUtil.getStr(R.string.common_text_remove)};
        } else if (!canDelete && canCopy) {
            items = new String[]{ResUtil.getStr(R.string.common_copy)};
        }
        showItemSelectDialog(context, items, true, ResUtil.getStr(R.string.common_text_option), true, callback);
    }

    public static void showBottomDialog(Context context, List<String> data, final ListViewDialog.Callback callback) {
        ListViewDialog view = new ListViewDialog(context);
        view.refresh(data);

        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initBottomDialog();
        dialog.show();

        view.setCallback(position -> {
            dialog.dismiss();
            if (callback != null) callback.onClickItem(position);
        });
    }

    public static void showFragBottomDialog(Context context, List<String> data, final ListViewDialog.Callback callback) {
        ListViewDialog view = new ListViewDialog(context);
        view.refresh(data);

        final BaseDialogFragment dialog = new BaseDialogFragment();
        dialog.setContentView(view);
        dialog.initBottom();
        dialog.initFullWidth();
        dialog.setWindowAnim(R.style.bottom_dialog_anim);
        dialog.show(context, FRAG_BOTTOM);

        view.setCallback(position -> {
            dialog.dismiss();
            if (callback != null) {
                callback.onClickItem(position);
            }
        });
    }

    public static void showSimpleTextDialog(Context context, String content, String enterTx, String cancelTx, boolean isCenter, boolean canCancel, final DialogCallback callback) {
        BaseViewDialog dialogView = new BaseViewDialog(context);
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_simple_text, null);
        final TextView centerTx = view.findViewById(R.id.dialog_simple_center_tx);
        final TextView fullTx = view.findViewById(R.id.dialog_simple_full_tx);
        dialogView.addContentView(view);
        if (isCenter) {
            centerTx.setVisibility(View.VISIBLE);
            fullTx.setVisibility(View.GONE);
            centerTx.setText(content);
        } else {
            centerTx.setVisibility(View.GONE);
            fullTx.setVisibility(View.VISIBLE);
            fullTx.setText(content);
        }

        dialogView.setCancelText(cancelTx);
        dialogView.setEnterText(enterTx);

        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(dialogView);
        dialog.init();
        dialog.show();
        dialog.setCancelable(canCancel);
        dialog.setCanceledOnTouchOutside(canCancel);

        dialogView.setCallback(new DialogCallback() {
            @Override
            public void onEnter() {
                if (callback != null) callback.onEnter();
                dialog.dismiss();
            }

            @Override
            public void onCancel() {
                if (callback != null) callback.onCancel();
                dialog.dismiss();
            }
        });
    }

    public static void showRoomKickedDialog(Context context, String reason) {
        String r = TextUtils.isEmpty(reason) ? ResUtil.getStr(R.string.voice_room_kicked_tip) : reason;
        showSimpleTextDialogWithSingleBt(context, r, ResUtil.getStr(R.string.got_it), true, null);
    }

    public static void showSimpleTextDialogWithSingleBt(Context context, String content, String enterTx, boolean isCenter, final DialogCallback callback) {
        BaseViewDialog dialogView = new BaseViewDialog(context);
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_simple_text, null);
        final TextView centerTx = view.findViewById(R.id.dialog_simple_center_tx);
        final TextView fullTx = view.findViewById(R.id.dialog_simple_full_tx);
        dialogView.addContentView(view);
        if (isCenter) {
            centerTx.setVisibility(View.VISIBLE);
            fullTx.setVisibility(View.GONE);
            centerTx.setText(content);
        } else {
            centerTx.setVisibility(View.GONE);
            fullTx.setVisibility(View.VISIBLE);
            fullTx.setText(content);
        }

        dialogView.initBottomButton(true);
        dialogView.setEnterText(enterTx);

        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(dialogView);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        dialog.init();
        dialog.show();

        dialogView.setCallback(new DialogCallback() {
            @Override
            public void onEnter() {
                if (callback != null) callback.onEnter();
                dialog.dismiss();
            }

            @Override
            public void onCancel() {
                if (callback != null) callback.onCancel();
                dialog.dismiss();
            }
        });
    }

    public static void showQRCodeDialog(Context context, ShareInfo info, ShareCallback shareCallback) {
        PropApi.getAvatarImage(LoginHelper.getLoginUid(), false, true,
                new LifeDataCallback<AvatarImageInfo>(ContextUtil.getLife(context)) {
                    @Override
                    public void onSuccess(Result<AvatarImageInfo> result) {
                        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, com.wepie.lib.share.R.style.dialog_background_80);
                        AddFriendQRCodeDialog addFriendQRCodeDialog = new AddFriendQRCodeDialog(context, v -> dialog.dismiss());
                        info.addShareTypes(ShareType.weCircle);
                        info.addTripartiteShareType();
                        info.addShareTypes(ShareType.android, ShareType.saveBmp);
                        List<SharePlugin> pluginList = ApiService.of(IShareApi.class).getSharePlugin(info);
                        addFriendQRCodeDialog.update(pluginList, info, shareCallback);

                        dialog.setContentView(addFriendQRCodeDialog);
                        dialog.setCanceledOnTouchOutside(true);
                        dialog.initFullWidth();
                        addFriendQRCodeDialog.setDialogCallback(() -> dialog.dismiss());
                        dialog.show();
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
    }

    public static void showSelectTime(Context context, int year, int mouth, int day, final TimeSelectListener listener) {
        SelectTimeView selectTimeView = new SelectTimeView(context);
        selectTimeView.setSelectTime(year, mouth, day);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setCanceledOnTouchOutside(true);
        selectTimeView.setListener(new TimeSelectListener() {
            @Override
            public void onFinish(int year, int mouch, int day) {
                if (listener != null) listener.onFinish(year, mouch, day);
                dialog.dismiss();
            }

            @Override
            public void onCancel() {
                if (listener != null) listener.onCancel();
                dialog.dismiss();
            }
        });

        dialog.initBottomDialog();
        dialog.setContentView(selectTimeView);
        dialog.show();
    }

    private static TextView addTitle(Context context, LinearLayout parent) {
        TextView textView = new CustomTextView(context);
        textView.setTextColor(Color.WHITE);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
        textView.setGravity(Gravity.START | Gravity.CENTER_VERTICAL);
        textView.setPaddingRelative(ScreenUtil.dip2px(context, 15), ScreenUtil.dip2px(context, 12), 0, ScreenUtil.dip2px(context, 12));
        textView.setBackgroundResource(R.drawable.inner_dialog_shape_tl);

        LinearLayout.LayoutParams param0 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        parent.addView(textView, param0);

        ImageView image = new ImageView(context);
        image.setBackgroundColor(Color.rgb(221, 220, 224));//#dddce0
        LinearLayout.LayoutParams param1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(context, 0.5f));
        parent.addView(image, param1);

        return textView;
    }

    private static TextView addItem(Context context, LinearLayout parent, boolean isLastItem, boolean hasTitle) {
        TextView textView = new CustomTextView(context);
        textView.setTextColor(Color.rgb(51, 51, 51));//#333333
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 17);
        textView.setGravity(Gravity.START | Gravity.CENTER_VERTICAL);
        textView.setTextAlignment(View.TEXT_ALIGNMENT_GRAVITY);
        textView.setPaddingRelative(ScreenUtil.dip2px(context, 15), 0, ScreenUtil.dip2px(context, 15), 0);
        textView.setBackgroundResource(hasTitle
                ? (isLastItem ? R.drawable.inner_dialog_sel_bottom_item : R.drawable.dialog_item_selector)
                : R.drawable.dialog_item_selector);

        int height = 60;

        LinearLayout.LayoutParams param0 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(context, height));
        parent.addView(textView, param0);

        if (isLastItem) return textView;

        ImageView image = new ImageView(context);
        image.setBackgroundColor(Color.rgb(221, 220, 224));
        LinearLayout.LayoutParams param1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(context, 0.5f));
        parent.addView(image, param1);

        return textView;
    }


    public static void showSimpleInput(Context context, String title, String hint, String ok, String defaultInput,
                                       int limitLen, BaseFragCb<String> cb) {
        SingleInputDialog.showDialog(context, title, hint, ok, defaultInput, limitLen, cb);
    }

    public static void dismissFragByTag(Context context, String tag) {
        BaseDialogFragment.dismissDialogByTag(context, tag);
    }

    public static void showProgressDialogDelay(Context context) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity instanceof IProgressDialog) {
            ((IProgressDialog) activity).showProgressDialogDelay();
        }
    }

    public static void showProgressDialog(Context context, String content, boolean canCancel) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity instanceof IProgressDialog) {
            ((IProgressDialog) activity).showProgressDialog(content, canCancel);
        }
    }

    public static void hideProgressDialog(Context context) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity instanceof IProgressDialog) {
            ((IProgressDialog) activity).hideProgressDialog();
        }
    }

    public static void showItemSelectDialog(Context context, String[] strs, boolean hasTitle,
                                            String title, boolean cancelOutside, final DialogItemCallback callback) {
        LinearLayout rootLay = new LinearLayout(context);
        rootLay.setOrientation(LinearLayout.VERTICAL);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(rootLay);
        dialog.setCanceledOnTouchOutside(cancelOutside);
        dialog.init();

        if (hasTitle) {
            TextView titleText = addTitle(context, rootLay);
            titleText.setText(title == null ? "" : title);
        }
        int len = strs.length;
        for (int i = 0; i < len; i++) {
            boolean isLastItem = (i == len - 1);
            TextView textView = addItem(context, rootLay, isLastItem, hasTitle);
            textView.setText(strs[i]);
            final int position = i;
            textView.setOnClickListener(v -> {
                dialog.dismiss();
                callback.onItemSelect(position);
            });
        }

        if (cancelOutside) {
            rootLay.setOnClickListener(v -> dialog.dismiss());
        }
        dialog.show();
    }

    public static void showDoubleBtnDialogWithSpan(Context context, String title, SpannableString content, String sureTx, String cancelTx, boolean canCancel, final DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_double_view, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(canCancel);
        dialog.setCancelable(canCancel);
        dialog.initFullWidth();

        final TextView titleView = view.findViewById(R.id.id_tip_double_bt_tl);
        final TextView contentView = view.findViewById(R.id.id_tip_double_bt_content_tx);
        final Space titleSpace = view.findViewById(R.id.id_title_space);

        if (TextUtils.isEmpty(title)) {
            titleView.setVisibility(View.GONE);
            titleSpace.setVisibility(View.VISIBLE);
        } else {
            titleView.setVisibility(View.VISIBLE);
            titleView.setText(title);
            titleSpace.setVisibility(View.GONE);
        }
        contentView.setText(content);

        TextView cancelBt = view.findViewById(R.id.certificate_cancel_btn);
        TextView sureBt = view.findViewById(R.id.certificate_sure_btn);
        cancelBt.setText(cancelTx);
        cancelBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickCancel();
        });
        sureBt.setText(sureTx);
        sureBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickSure();
        });
        dialog.show();
    }

    public static void showVoiceRoomInviteSpeakReplyDialog(Context context, String content, final DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_double_view, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(true);
        dialog.initFullWidth();

        final TextView titleView = view.findViewById(R.id.id_tip_double_bt_tl);
        final TextView contentView = view.findViewById(R.id.id_tip_double_bt_content_tx);

        titleView.setVisibility(View.GONE);
        view.findViewById(R.id.id_title_space).setVisibility(View.VISIBLE);
        contentView.setText(content);

        TextView cancelBt = view.findViewById(R.id.certificate_cancel_btn);
        TextView sureBt = view.findViewById(R.id.certificate_sure_btn);
        cancelBt.setText(ResUtil.getResource().getString(R.string.reject_count_down_sec, 10));
        cancelBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickCancel();
        });
        sureBt.setText(R.string.agree);
        sureBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickSure();
        });
        dialog.setOnDismissListener(dialog1 -> {
            isInviteSpeakDialogShow = false;
            VoiceRoomService.getInstance().setInviteSpeakReplyTimeLeft(0);
            sHandler.removeCallbacksAndMessages(null);
        });
        dialog.show();
        isInviteSpeakDialogShow = true;

        countDownInviteSpeak(10 - 1, cancelBt, dialog);
    }

    public static void showSingleBtDialogActivity(String content, String sureText) {
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, TextDialogActivity.class);
        intent.putExtra("content", content);
        intent.putExtra("sureText", sureText);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void showLitterGameRecoverActivity(VoiceRoomInfo voiceRoomInfo) {
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, LittleGameRecoverActivity.class);
        intent.putExtra("voice_room_info", voiceRoomInfo);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void showGameRecoverActivity(GameStateInfo state) {
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, GameRecoverActivity.class);
        intent.putExtra(GameRecoverActivity.KEY_STATE_PARCEL, state);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     * 不能同时弹两个，callback会响应有问题
     * 更新时需要 check 权限相关问题。注意 PublicPermissionCheckUtil#filterActivityList
     *
     * @param title
     * @param content
     * @param sureTx
     * @param cancelTx
     * @param callback
     */
    public static void showDoubleBtnDialogActivity(String title, String content, String sureTx, String cancelTx, ActivityDialogCallback callback) {
        Context context = LibBaseUtil.getApplication();
        Intent intent = new Intent(context, DoubleBtnDialogActivity.class);
        String key = System.currentTimeMillis() + "";
        DoubleBtnDialogActivity.setCallback(key, callback);
        intent.putExtra("title", title);
        intent.putExtra("content", content);
        intent.putExtra("sureText", sureTx);
        intent.putExtra("cancelText", cancelTx);
        intent.putExtra("callbackKey", key);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void showWeddingRoomSingleBtnDialog(Context context, String content, String sureTxt, boolean canCancel, final DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.wedding_room_single_btn_dialog, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(canCancel);
        dialog.setCancelable(canCancel);
        dialog.initFullWidth();

        final TextView contentView = view.findViewById(R.id.content);
        contentView.setText(content);

        TextView sureBt = view.findViewById(R.id.sure_btn);
        sureBt.setText(sureTxt);
        sureBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickSure();
        });
        dialog.setOnDismissListener(dialog1 -> {
            if (weddingDialogReference != null && weddingDialogReference.get() == dialog1) {
                weddingDialogReference = null;
            }
        });
        dialog.show();
        weddingDialogReference = new SoftReference<>(dialog);
    }

    public static void showWeddingRoomDoubleBtnDialog(Context context, String content, String sureTxt, String canceltxt, boolean canCancel, final DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.wedding_room_double_btn_dialog, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(canCancel);
        dialog.setCancelable(canCancel);
        dialog.initFullWidth();

        final TextView contentView = view.findViewById(R.id.id_tip_double_bt_tl);

        contentView.setText(content);

        TextView cancelBt = view.findViewById(R.id.certificate_cancel_btn);
        TextView sureBt = view.findViewById(R.id.certificate_sure_btn);
        cancelBt.setText(canceltxt);
        cancelBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickCancel();
        });
        sureBt.setText(sureTxt);
        sureBt.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickSure();
        });
        dialog.show();
        dialog.setOnDismissListener(dialog1 -> {
            if (weddingDialogReference != null && weddingDialogReference.get() == dialog1) {
                weddingDialogReference = null;
            }
        });
        weddingDialogReference = new SoftReference<>(dialog);
    }

    private static void countDownInviteSpeak(final int secondLeft, final TextView cancelBt, final BaseFullScreenDialog dialog) {
        if (secondLeft == 0) {
            dialog.dismiss();
            return;
        }
        cancelBt.setText(ResUtil.getResource().getString(R.string.reject_count_down_sec, secondLeft));
        sHandler.postDelayed(() -> countDownInviteSpeak(secondLeft - 1, cancelBt, dialog), 1000);
    }

    public static boolean isVoiceRoomInviteSpeakReplyDialogShowing() {
        return isInviteSpeakDialogShow;
    }

    public static SoftReference<BaseFullScreenDialog> weddingDialogReference;

    public static void showWeddingSpecialPeopleDialog(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_speacial_people_wedding, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(true);
        dialog.initFullWidth();

        ImageView closeBtn = view.findViewById(R.id.close_btn);
        closeBtn.setOnClickListener(v -> dialog.dismiss());

        dialog.show();
    }

    public static void hideWeddingDialog() {
        if (weddingDialogReference != null) {
            BaseFullScreenDialog dialog = weddingDialogReference.get();
            if (dialog != null) {
                dialog.dismiss();
            }
        }
    }

    public static void showOpenNotificationGuideDialog(final Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.notification_guide_dialog, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.initFullWidth();

        ImageView btn = view.findViewById(R.id.btn);
        btn.setOnClickListener(v -> {
            dialog.dismiss();
            JumpSystemSettingPageManagement.goToAppSetting(context, JumpSystemSettingPageManagement.TYPE_OPEN_NOTIFICATION);
        });

        dialog.show();
    }

    public static void showFriendOpenNotificationDialog(final Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.friend_open_notifi_dialog, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.initFullWidth();

        View btn = view.findViewById(R.id.btn);
        view.findViewById(R.id.close).setOnClickListener(v -> dialog.dismiss());

        btn.setOnClickListener(v -> {
            dialog.dismiss();
            DialogUtil.showOpenNotificationGuideDialog(context);
        });

        dialog.show();

        PrefUtil.getInstance().setLong(PrefUtil.OPEN_NOTIFICATION_DIALOG, System.currentTimeMillis());
        AddFriendManager.getInstance().shouldShowNotificationDialog = false;
    }

    public static void showSingleBtnDialogWithImg(Context context, String msg, String title, int res, DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_img_text_view, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(true);
        TextView contentTv = view.findViewById(R.id.content_tv);
        TextView titleTv = view.findViewById(R.id.title_tv);
        ImageView imageView = view.findViewById(R.id.dialog_iv);
        TextView sureBtn = view.findViewById(R.id.sure_btn);
        contentTv.setText(msg);
        titleTv.setText(title);
        imageView.setImageResource(res);
        sureBtn.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) callback.onClickSure();
        });
        dialog.initFullWidth();
        dialog.show();
    }

    public static void showDoubleBtnDialog(Context context, String msg, String title, String cancelName, String sureName, DialogBuild.DialogCallback callback) {
        DialogBuild.newBuilder(context).setSingleBtn(false).setCancelTx(cancelName).setTitle(title).setContent(msg).setSureTx(sureName).setDialogCallback(callback).show();
    }

    public static void showSingleBtnDialog(Context context, String msg, String title, DialogBuild.DialogCallback callback) {
        DialogBuild.newBuilder(context).setSingleBtn(true).setTitle(title).setContent(msg).setSureTx(ResUtil.getStr(R.string.sure)).setDialogCallback(callback).show();
    }

    public static void showSingleBtnDialogWithoutContext(String msg, String title, String sureName, SimpleDialogManager.SimpleDialogCallback callback) {
        String dialogId = "showSingleBtnDialogWithoutContext";
        Context context = LibBaseUtil.getApplication();
        SimpleDialogBuilder builder = new SimpleDialogBuilder().setTitle(title).setContent(msg).setSure(sureName).setDoubleBtn(false);
        SimpleDialogManager.getInstance().addBuilder(dialogId, builder);
        SimpleDialogManager.getInstance().addCallback(dialogId, callback);
        Intent intent = new Intent(context, SimpleDialogActivity.class);
        intent.putExtra(SimpleDialogActivity.DIALOG_ID, dialogId);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void show(Context context, DialogBuild.DialogCallback dialogCallback) {
        DialogBuild.newBuilder(context).setSingleBtn(false).setTitle(R.string.close_honor_seat_tip).setContent(R.string.close_honor_seat_content).setCancelTx(R.string.cancel).setSureTx(ResUtil.getStr(R.string.sure)).setDialogCallback(dialogCallback).show();
    }

    public static void showOpenHotBadge(final Context context, String content, String helpUrl, DialogBuild.DialogCallback callback) {
        View view = LayoutInflater.from(context).inflate(R.layout.open_hot_badge_dialog, null);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.initFullWidth();
        View noticeLay = view.findViewById(R.id.notice_lay);
        TextView sureBtn = view.findViewById(R.id.sure_tv);
        TextView cancelBtn = view.findViewById(R.id.cancel_tv);
        TextView contentTv = view.findViewById(R.id.content_tv);
        noticeLay.setOnClickListener(v ->
                TitleWebViewDialog.show(context, ResUtil.getString(R.string.hotbadge_help), helpUrl, R.drawable.ic_close_3));
        cancelBtn.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) {
                callback.onClickCancel();
            }
        });
        sureBtn.setOnClickListener(v -> {
            dialog.dismiss();
            if (callback != null) {
                callback.onClickSure();
            }
        });
        contentTv.setText(content);
        dialog.show();
    }

    /**
     * 弹出验证码弹窗
     */
    public static void showCaptchaDialogActivity(String msg, String setting, DataCallback<String> callback) {
        String dialogId = "showSimpleCaptchaDialogActivity";
        SimpleDialogBuilder builder = new SimpleDialogBuilder().setContent(msg)
                .setShowCaptchaDialog(true).setCaptchaCallBack(callback);
        SimpleDialogManager.getInstance().addBuilder(dialogId, builder);
        Context context = ActivityTaskManager.getInstance().getTopActivity();
        Intent intent;
        if (context != null) {
            intent = new Intent(context, SimpleDialogActivity.class);
        } else {
            context = LibBaseUtil.getApplication();
            intent = new Intent(context, SimpleDialogActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.putExtra(SimpleDialogActivity.DIALOG_ID, dialogId);
        intent.putExtra(SimpleDialogActivity.DIALOG_SET, setting);
        context.startActivity(intent);
    }

    public interface DialogItemCallback {
        void onItemSelect(int item);
    }
}
