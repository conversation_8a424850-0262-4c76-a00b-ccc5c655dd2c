package com.wepie.wespy.module.voiceroom.voicegame

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.view.LayoutInflater
import android.widget.TextView
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.BaseFullScreenDialog
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.ui.dialog.HWUIDialogBuilder.Companion.show
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.international.regoin.IDRegionUtil
import com.huiwan.constants.GameType
import com.huiwan.lib.api.impl
import com.wepie.wespy.module.voiceroom.seat.SeatOperateView
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.wejoy.weplay.ex.view.postAutoCancel
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.util.CommonUtil
import com.wepie.wespy.helper.shence.ShenceGameTypeSource
import com.wepie.wespy.module.voiceroom.dataservice.VoiceGameModel
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.invite.RoomInviteFriendCallback
import com.wepie.wespy.module.voiceroom.setting.IVoiceRoomChooseView
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomChooseCocosDialog
import com.wepie.wespy.net.tcp.sender.VoiceGameRoomPacketSender
import java.lang.ref.WeakReference

object VoieGameDialogUtil {
    fun showInviteFriendDialog(context: Context) {
        val service = VoiceRoomService.getInstance()
        val roomInfo = service.roomInfo
        val rid = roomInfo.rid
        val uid = LoginHelper.getLoginUid()
        val littleGameType = service.voiceGameViewModel.littleGameType
        val roomName = ResUtil.getStr(R.string.game_type_voice_room)
        val configHelper = ConfigHelper.getInstance()
        val shareInfo = ShareInfo().apply {
            setTitle(
                configHelper.roomShareTitle
                    .replace("{game_type}", roomName)
                    .replace(
                        "{rid}",
                        IDRegionUtil.getFinalIDStrByGameType(rid.toLong(), gameType)
                    )
            )
            setDesc(ConfigHelper.getInstance().roomShareDesc)
            bitmapPath = ConfigHelper.getInstance().shareIconUrl
            val link = if (rid > 0 && uid > 0) {
                getLinkIntercept(
                    configHelper.myShareUrl + "share/room?" +
                            "code=" + StringUtil.uid2code(rid)
                            + "&share=" + StringUtil.uid2code(uid)
                            + "&game_type=" + roomInfo.game_type
                )
            } else {
                ""
            }
            setLink(link)
            //添加分享的打点信息
            screenName = TrackScreenName.SHARE_PAGE
            scene = TrackString.SCENE_VOICE_ROOM
            gameType = roomInfo.game_type
            setRid(rid.toString())
            addTripartiteShareType()
            addShareTypes(ShareType.friend, ShareType.android, ShareType.copyLink)
            extTrackInfo["sub_game_type"] = littleGameType
        }

        val roomInviteFriendCallback = RoomInviteFriendCallback(
            roomInfo.rid, roomInfo.game_type, littleGameType
        )
        IShareApi::class.impl().showShareDialog(context, shareInfo, object : ShareCallback {
            override fun onShare(data: ShareResult): Boolean {
                if (data.shareType == ShareType.friend) {
                    roomInviteFriendCallback.onInvite(data.target)
                }
                return true
            }
        })
    }

    fun showSwitchSeatDialog(
        context: Context,
        countDownSecond: Int,
        applyUId: Int,
        applySeat: Int,
        targetUid: Int,
        targetSeat: Int,
        applyTIme: Long,
    ) {
        val view =
            LayoutInflater.from(context).inflate(R.layout.ice_ball_switch_seat_dialog_view, null)
        val switchSeatUserInfo = view.findViewById<TextView>(R.id.ice_ball_switch_info_tv)
        val switchSeatTimeTv = view.findViewById<TextView>(R.id.ice_ball_switch_time_tv)
        val switchSeatDialog = BaseFullScreenDialog(context, R.style.dialog_style)
        switchSeatTimeTv.text = String.format(LibBaseUtil.getLocale(), "(%d)", countDownSecond)
        switchSeatDialog.setContentView(view)
        switchSeatDialog.setCanceledOnTouchOutside(false)
        switchSeatDialog.init()
        val cancelBt = view.findViewById<TextView>(R.id.ice_ball_switch_cancel_btn)
        val sureBt = view.findViewById<TextView>(R.id.ice_ball_switch_sure_btn)
        val handler = Handler(Looper.getMainLooper())
        cancelBt.setOnClickListener {
            switchSeatDialog.dismiss()
            handler.removeCallbacksAndMessages(null)
            dealSwitchSeat(false, applyUId, applySeat, targetUid, targetSeat, applyTIme)

        }
        sureBt.setOnClickListener {
            switchSeatDialog.dismiss()
            handler.removeCallbacksAndMessages(null)
            dealSwitchSeat(true, applyUId, applySeat, targetUid, targetSeat, applyTIme)
        }
        UserService.get().getCacheSimpleUser(
            applyUId, object : LifeUserSimpleInfoCallback(ContextUtil.getLife(context)) {
                override fun onUserInfoSuccess(userInfo: UserSimpleInfo) {
                    val nickName = StringUtil.subName(userInfo.getNickname(), 6)
                    val src =
                        ResUtil.getResource()
                            .getString(R.string.cocos_req_change_seat_tip_s, nickName)
                    val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        Html.fromHtml(src, Html.FROM_HTML_MODE_LEGACY)
                    } else {
                        Html.fromHtml(src)
                    }
                    switchSeatUserInfo.text = result
                    if (!switchSeatDialog.isShowing) {
                        switchSeatDialog.show()
                    }
                }

                override fun onUserInfoFailed(description: String?) {
                    ToastUtil.show(description)
                }
            })
        val dialogRef = WeakReference(switchSeatDialog)
        val timeTvRef = WeakReference(switchSeatTimeTv)
        switchSeatTimeTv.postAutoCancel(1001L) {
            countDown(handler, countDownSecond, dialogRef, timeTvRef)
        }
    }

    private fun countDown(
        handler: Handler,
        timeToUpdate: Int,
        dialogRef: WeakReference<BaseFullScreenDialog>,
        timeTvRef: WeakReference<TextView>
    ) {
        if (timeToUpdate <= 0) {
            dialogRef.get()?.dismiss()
        } else if (dialogRef.get() != null && timeTvRef.get() != null) {
            val textView = timeTvRef.get() ?: return
            textView.text = String.format(LibBaseUtil.getLocale(), "(%s)", timeToUpdate - 1)
            textView.postAutoCancel(1001L) {
                countDown(handler, timeToUpdate - 1, dialogRef, timeTvRef)
            }
        }
    }

    private fun dealSwitchSeat(
        isAgree: Boolean,
        applyUid: Int,
        applySeatId: Int,
        targetUid: Int,
        targetSeatId: Int,
        applyTime: Long
    ) {
        val vm = VoiceGameRoomViewModel.get() ?: return
        VoiceGameRoomPacketSender.replyExchangeSeat(
            vm.littleGameInfo.rid,
            isAgree,
            applyUid,
            applySeatId,
            targetUid,
            targetSeatId,
            applyTime
        )
    }


    fun showTextDialog(context: Context, content: String) {
        HWUIDialogBuilder.newBuilder(context).setSingleBtn(true)
            .setTitle(content)
            .setSureTx(R.string.button_ok)
            .setCanCancel(false)
            .show()
    }

    /**
     * @param context
     * @param gameType 当前的语音房类型
     * @param littleGameType 语音房内的游戏类型
     * @param gameMode 语音房内的游戏模式
     */
    @JvmStatic
    fun showCocosChooseGameDialog(
        context: Context,
        gameType: Int,
        basicInfo: VoiceGameModel.LittleGameInfo.GameBasicInfo,
        onSure: (Int, GameConfig.MatchInfo) -> Unit
    ) {
        VoiceRoomChooseCocosDialog.show(
            context,
            basicInfo
        ) { gameType: Int, matchInfo: GameConfig.MatchInfo, baseFullScreenDialog: BaseFullScreenDialog, voiceRoomChooseCocosView: IVoiceRoomChooseView ->
            if (gameType == GameType.GAME_TYPE_VOICE_GAME_ROOM && gameType == basicInfo.littleGameType && basicInfo.getMatchInfoID() == matchInfo.id) {
                //all same
                baseFullScreenDialog.dismiss()
                return@show
            }
            val tips = ResUtil.getStr(R.string.voice_room_mode_change_tip)
            HWUIDialogBuilder(context).show {
                title(tips)
                positiveButton {
                    onSure(gameType, matchInfo)
                    baseFullScreenDialog.dismiss()
                }
                negativeButton {
                    voiceRoomChooseCocosView.initGameInfo(
                        basicInfo.littleGameType, basicInfo.gameMode, basicInfo.betLevel
                    )
                }
            }
        }
    }

    fun showUserDialogInGameSeat(context: Context, seatOperateView: SeatOperateView?, uid: Int, isGameSeat: Boolean):Dialog {
        val littleGameType = VoiceRoomService.getInstance().voiceGameViewModel.littleGameType
        val subSource = "game_type_$littleGameType"
        return CommonUtil.showUserDialogInVoiceRoom(
            uid,
            context,
            seatOperateView,
            littleGameType,
            isGameSeat,
            ShenceGameTypeSource.getGameTypeShortSource(VoiceRoomService.getInstance().roomInfo.game_type),
            subSource
        )
    }

    fun showKickedOutDialog(context: Context) {
        HWUIDialogBuilder.newBuilder(context)
            .setSingleBtn(true)
            .setTitle(R.string.game_match_kick_out_team)
            .setSureTx(R.string.game_match_kick_out_team_konw)
            .show()
    }

    fun showKickUseFromGameSeatTip(context: Context, onClickSure: () -> Unit) {
        HWUIDialogBuilder.newBuilder(context).setTitle(R.string.voice_game_room_kick_user_tip)
            .setSingleBtn(false).setDialogCallback(onClickSure).setSureTx(R.string.sure)
            .setCancelTx(R.string.cancel)
            .show()
    }

}