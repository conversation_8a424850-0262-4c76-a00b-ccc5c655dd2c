package com.wepie.wespy.module.voiceroom.voicegame.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.show
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.base.util.VibrateUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.component.gift.show.ICommonAnimData
import com.huiwan.component.gift.show.OnAnimListener
import com.huiwan.component.gift.show.PropShowInfo
import com.huiwan.component.gift.show.anim.AnimType
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.JKGameConfig
import com.huiwan.configservice.constentity.propextra.JackarooSkinExtra
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.liveData
import com.huiwan.constants.GameType
import com.huiwan.constants.GameType.GAME_TYPE_VOICE_GAME_ROOM
import com.huiwan.constants.IntentConfig
import com.huiwan.lib.api.impl
import com.huiwan.littlegame.cocos.CocosBridgeInterface
import com.huiwan.littlegame.cocos.CocosLaunchInfo
import com.huiwan.littlegame.cocos.CocosWebView
import com.huiwan.littlegame.cocos.CocosWebViewJsbConfig
import com.huiwan.littlegame.cocos.SCENE_VOICE_ROOM
import com.huiwan.littlegame.cocos.callOnPlayVideoEffectStatus
import com.huiwan.littlegame.cocos.callRefreshUserVolume
import com.huiwan.littlegame.cocos.callShareCallback
import com.huiwan.littlegame.cocos.getUnpackDir
import com.huiwan.littlegame.cocos.resCheck.loaderFactory.CocosResLoadConfig
import com.huiwan.littlegame.cocos.wrapUrl
import com.huiwan.littlegame.event.CocosOneMoreMatchEvent
import com.huiwan.littlegame.event.CocosShareEvent
import com.huiwan.littlegame.event.IceGameCommandEvent
import com.huiwan.littlegame.event.ShowGameRuleEvent
import com.huiwan.littlegame.util.CocosSoundUtil
import com.huiwan.littlegame.util.CommonUtil
import com.huiwan.littlegame.view.dialog.IceBallDialogUtil
import com.huiwan.media.OnVolumeChange
import com.huiwan.media.VolumeAdjustDialog
import com.huiwan.user.LoginHelper
import com.huiwan.voiceservice.VoiceManager
import com.three.http.core.KtResult
import com.three.http.core.KtResultSuccess
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGame
import com.wejoy.littlegame.LittleGame.fromTeamInfoNoSeat
import com.wejoy.littlegame.LittleGameRoomInfo
import com.wejoy.littlegame.LittleGameSeat
import com.wejoy.weplay.ex.context.toAppCompatActivity
import com.wejoy.weplay.ex.lifecycle.observe
import com.wejoy.weplay.ex.view.toLife
import com.wejoy.weplay.ex.view.viewScope
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.loader.CocoResLoader
import com.wepie.wespy.cocosnew.match.main.ar285.loader.CocosGameSilentPreloader
import com.wepie.wespy.cocosnew.update.CocosVersionManager
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil
import com.wepie.wespy.model.event.RoomInfoUpdateEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceGameModel
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.BaseVoiceRoomActivity
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IGiftContent
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager
import com.wepie.wespy.module.voiceroom.voicegame.VoiceGameRoomViewModel
import com.wepie.wespy.module.voiceroom.voicegame.VoieGameDialogUtil
import com.wepie.wespy.module.voiceroom.voicegame.data.VoiceGameState
import com.wepie.wespy.module.voiceroom.voicegame.data.VoiceRoomLittleGameEvent
import com.wepie.wespy.net.tcp.sender.VoiceGameRoomPacketSender
import com.wepie.wespy.voiceroom.VoiceStatus
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class VoiceRoomCocosView(context: Context, attrs: AttributeSet) : FrameLayout(context, attrs),
    LifecycleEventObserver {

    private lateinit var roomBg: ImageView
    private var webView: CocosWebView? = null
    private lateinit var loadingView: VoiceGameRoomLoadingView
    private lateinit var vibrateUtil: VibrateUtil
    var shareType = -1
    private var cocosStartLoadingTime = 0L

    private var webViewTop: Int = 0
    private var webViewBottom: Int = 0
    private var checkResJob: Job? = null

    private val vm: VoiceGameRoomViewModel?
        get() = VoiceGameRoomViewModel.get()

    private lateinit var observer: Observer<JKGameConfig>

    companion object {
        private const val TAG = "VoiceRoomCocosView"
    }

    init {
        initView()
        initData()
        ILittleGameApi::class.impl().preload(context)
    }

    private fun initView() {
        LayoutInflater.from(context).inflate(R.layout.room_game_cocos_view, this)
        roomBg = findViewById(R.id.room_bg)
        loadingView = findViewById(R.id.loading_view)
        vm?.collectEventFlow(viewScope) {
            when (it) {
                is VoiceRoomLittleGameEvent.GameChanged -> {
                    showBgView(it.gameType)
                    updateLoadingUI(it.gameType, true)
                }

                is VoiceRoomLittleGameEvent.GameStartResultSuccess -> checkResAndOpenGameView(it.gameType)
                is VoiceRoomLittleGameEvent.GameCloseCocos -> {
                    closeGameView()
                    releaseMedia()
                }

                is VoiceRoomLittleGameEvent.GameStatusChanged -> onGameStateChanged(it.gameState)

                is VoiceRoomLittleGameEvent.SetWebViewRect -> {
                    webViewTop = it.top
                    webViewBottom = it.bottom
                }

                is VoiceRoomLittleGameEvent.CheckResOk -> {
                    if (it.gameConfig.gameType != vm?.littleGameType) {
                        return@collectEventFlow
                    }
                    showBgView(it.gameConfig.gameType)
                    afterCheckResOk(it.gameConfig.gameType)
                }

                is VoiceRoomLittleGameEvent.CheckResFail -> {
                    HLog.e(TAG, HLog.USR, "checkRes failed:${it.msg}")
                    if (it.gameConfig.gameType != vm?.littleGameType) {
                        return@collectEventFlow
                    }
                    loadingView.updateStateLoadingFailed(VoiceGameRoomLoadingView.STATE_LOADING_RES_FAIL) {
                        updateStateLoading(it.gameConfig)
                        updateCocosRes(it.gameConfig)
                    }
                }


                else -> Unit
            }
        }
        vm?.let {
            onGameStateChanged(it.gameStateFlow.value)
        }
    }

    private fun afterCheckResOk(gameType: Int) {
        vm?.emitEvent(VoiceRoomLittleGameEvent.RefreshIvFromCocosZipRes)
        val curGameState = vm?.littleGameInfo?.littleGameState
        when (curGameState) {
            VoiceGameModel.LittleGameState.Created,
            VoiceGameModel.LittleGameState.Started -> {
                openGameView(gameType)
            }

            else -> {
                /**
                 * 在状态扭转时会调用 updateLoadingUI，然后当处于Created状态时不需要再加载完成后 隐藏 loadingView，
                 * @see com.wepie.wespy.module.voiceroom.voicegame.views.VoiceGamePrepareView#refreshByLittleGameState
                 */
                loadingView.gone()
                vm?.emitEvent(VoiceRoomLittleGameEvent.ShowPrepareSeat(true))
            }
        }
    }


    private fun initData() {
        ContextUtil.getActivityFromContext(context)?.let {
            vibrateUtil = VibrateUtil(it)
            if (it.intent.getBooleanExtra(IntentConfig.COCOS_START, false)) {
                checkResAndOpenGameView(vm?.littleGameType ?: GameType.GAME_TYPE_JACKAROO)
            }
        }
        CommonUtil.registerSpeakCallback(toLife()) { json: String ->
            if (webView == null) {
                return@registerSpeakCallback
            }
            post {
                webView?.let {
                    callRefreshUserVolume(json, it)
                }
            }
        }
    }

    private fun onGameStateChanged(state: VoiceGameState) {
        if (state.gameStatus == VoiceGameModel.LittleGameState.Init) {
            closeGameView()
        } else if (state.gameStatus == VoiceGameModel.LittleGameState.Created ||
            state.gameStatus == VoiceGameModel.LittleGameState.Started
        ) {
            checkReOpenWebViewAndOpenGameView(state.gameType)
        } else if (state.gameStatus == VoiceGameModel.LittleGameState.Over) {
            updateLoadingUI(state.gameType)
            if (ActivityTaskManager.getInstance().topActivity !is BaseVoiceRoomActivity) {
                releaseMedia()
            }
        }
    }

    private fun showBgView(gameType: Int) {
        CocosGameConfigUtil.loadResBgAsyncWithRecycle(
            roomBg, gameType, CocosGameConfigUtil.MATCH_BACKGROUND
        )
    }

    private fun showLoadingView(gameType: Int) {
        val gameConfig = ConfigHelper.getInstance().getGameConfig(gameType)
        updateStateLoading(gameConfig)
        loadingView.show()
    }

    private fun hideLoadingView() {
        loadingView.gone()
    }

    private fun checkReOpenWebViewAndOpenGameView(gameType: Int) {
        if (webView?.isVisible == true) {
            return
        }
        checkResAndOpenGameView(gameType)
    }

    private fun checkResAndOpenGameView(gameType: Int) {
        HLog.d(TAG, HLog.USR, "checkAndOpenGameView: gameType = {}", gameType)
        //这里先closeWbView是解决
        // 当前用户是观众在处于cocos结算页的情况下，房间内又开新的一局，但当前用户还是处于结算页的状态
        tryCloseWebView()
        roomBg.show()
        showLoadingView(gameType)
        val gameConfig = ConfigHelper.getInstance().getGameConfig(gameType)
        updateCocosRes(gameConfig)
    }

    private fun openGameView(gameType: Int) {
        val vm = VoiceGameRoomViewModel.get() ?: return
        if (webView == null) {
            webView = CocosWebView.getWebViewFromCache(context, CocosWebViewJsbConfig(true))
        }
        ViewUtil.addView(this, webView, 0)
        ViewUtil.setViewSize(
            webView, ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )

        vm.emitEvent(VoiceRoomLittleGameEvent.ShowPrepare(false))

        val gameInfo = fromTeamInfoNoSeat()
        val gameSeats = vm.littleGameInfo.gameSeats
        var watchUid: Int = gameSeats.firstOrNull { it.uid > 0 }?.uid ?: 0
        val seatList: MutableList<LittleGameSeat> = java.util.ArrayList()
        for (seatInfo in vm.littleGameInfo.gameSeats) {
            seatList.add(LittleGameSeat(seatInfo.uid, seatInfo.seatId))
            if (seatInfo.uid == LoginHelper.getLoginUid()) {
                watchUid = 0
            }
        }
        val roomInfo = LittleGameRoomInfo(VoiceRoomService.getInstance().rid, seatList)
        gameInfo.roomInfo = roomInfo
        gameInfo.beforeStartData = ""
        gameInfo.gameType = gameType
        gameInfo.unityScene = 0
        gameInfo.isRestore = false
        gameInfo.followUid = watchUid
        gameInfo.jumpMatch = true
        gameInfo.isVoiceGame = true
        LittleGame.updateVoiceGameInfo(gameInfo)

        val path: String = getUnpackDir(gameType, true)
        val url: String = wrapUrl(path)
        webView?.run {
            safeTop = webViewTop
            safeTop = webViewBottom
            this.path = path
            this.startTimeMs = System.currentTimeMillis()
            scene = SCENE_VOICE_ROOM
            this.gameInfo = gameInfo
            this.launchInfo = CocosLaunchInfo(
                CocosLaunchInfo.ENTER_MODE_VOICE_ROOM,
                deeplinkUrl = "",
                gameType = gameType,
                gameMode = gameInfo.gameMode,
                version = CocosVersionManager.getInstance().getGameVersion(gameType)
            )
            this.launchInfo.rid = roomInfo.rid
        }
        logCocosStart()
        webView?.visitUrl(url)
        initVoiceStatus()
    }

    private fun tryStopVoiceMusic() {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        val hasPermission = roomInfo.isSelfAdmin || roomInfo.isSelfOwner
        val isSelfChoose = VoiceMusicManager.get().curPlayInfo.selfChoose()
        val isPlaying = VoiceMusicManager.get().curPlayInfo.isPlaying
        if (isPlaying && (hasPermission || isSelfChoose)) {
            VoiceMusicManager.get().checkChangePlayStatus(roomInfo.rid)
        }
    }

    private fun logCocosStart() {
        cocosStartLoadingTime = System.currentTimeMillis()
        val map = mapOf(
            "name" to "cocos_web_start",
            "scene" to "voice_room"
        )
        HLog.d(TAG, HLog.USR, "cocos loading start: {}", map)
        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
    }

    private fun logLoadCocosSuccess() {
        val map = mutableMapOf(
            "name" to "cocos_web_success",
            "scene" to "voice_room",
        )
        if (cocosStartLoadingTime > 0) {
            map["duration"] = (System.currentTimeMillis() - cocosStartLoadingTime).toString()
        }
        HLog.d(TAG, HLog.USR, "cocos loading success: {}", map)
        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onVoiceStatusChanged(voiceStatus: VoiceStatus) {
        CocosSoundUtil.onVoiceStatusChanged(voiceStatus.openVoice)
    }

    private fun initVoiceStatus() {
        val isMuted =
            VoiceRoomService.getInstance().isMuteRoom(VoiceRoomService.getInstance().roomInfo.rid)
        onVoiceStatusChanged(VoiceStatus(!isMuted))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onIceGameCommand(event: IceGameCommandEvent) {
        val command = event.command
        if (command == IceGameCommandEvent.COMMAND_STOP_LOADING) {
            if (loadingView.isVisible) {
                logLoadCocosSuccess()
                hideLoadingView()
            }
            roomBg.gone()
        } else if (command == IceGameCommandEvent.COMMAND_SHOW_DIALOG) {
            VoieGameDialogUtil.showUserDialogInGameSeat(context, null, event.uid, true)
        } else if (command == IceGameCommandEvent.COMMAND_SHARE_GAME) {
            shareGame(LittleGame.gameInfo.gameType, event.subSource)
        } else if (command == IceGameCommandEvent.COMMAND_SHOW_REGULATE_VOLUME_DIALOG) {
            VolumeAdjustDialog.show(context, true, object : OnVolumeChange {
                override fun onBgmChange(percent: Float) {
                    super.onBgmChange(percent)
                    CocosSoundUtil.setVolume(percent, true)
                }

                override fun onVoiceChange(percent: Float) {
                    VoiceManager.getInstance().setPlayStreamVolume((percent * 100).toInt())
                }

                override fun onEffectChange(percent: Float) {
                    CocosSoundUtil.setVolume(percent, false)
                }
            })
        } else if (command == IceGameCommandEvent.COMMAND_SHAKE) {
            vibrateUtil.vibrate2(event.shakeTime)
        } else if (command == IceGameCommandEvent.COMMAND_GAME_RETURN) {
            vm?.let {
                closeGameView()
                viewScope.launch {
                    VoiceGameRoomPacketSender.leaveGameSeat(it.littleGameInfo.rid)
                }
            }
        } else if (command == IceGameCommandEvent.COMMAND_PLAY_PROP_ANIMATION) {
            val propItem = ConfigHelper.getInstance().propConfig.getPropItem(event.propId)
            val propAnimUrl = if (propItem != null) {
                val extra = propItem.getExtraByType(JackarooSkinExtra::class.java)
                extra?.effectVideo ?: ""
            } else {
                ""
            }
            val propShowInfo = PropShowInfo.getPropShowInfo(
                event.propId, propAnimUrl, AnimType.ANIM_TYPE_VAP,
                "vap_", event.propAnimaInfo
            )
            VoicePluginService.getPlugin(IGiftContent::class.java).showAnim(
                propShowInfo,
                object : OnAnimListener {
                    override fun onAnimStart(showInfo: ICommonAnimData) {}

                    override fun onAnimEnd(showInfo: ICommonAnimData) {
                        webView?.let {
                            callOnPlayVideoEffectStatus(event.propAnimaInfo.funcId, true, it)
                        }
                    }

                    override fun onFailed(showInfo: ICommonAnimData?) {
                        webView?.let {
                            callOnPlayVideoEffectStatus(event.propAnimaInfo.funcId, false, it)
                        }
                    }
                })
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onShowGameRuleEvent(event: ShowGameRuleEvent) {
        val info = event.info
        ILittleGameApi::class.impl().showGameRuleDialog(context, info, null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onOneMoreMatch(event: CocosOneMoreMatchEvent) {
        vm?.let {
            closeGameView()
            viewScope.launch {
                VoiceGameRoomPacketSender.readyOnGameSeat(it.rid, true, it.selfSeatId)
            }
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doCocosShare(event: CocosShareEvent) {
        shareType = event.type
        val shareInfo = ShareInfo()
        shareInfo.setTitle(event.title)
        shareInfo.bitmapPath = event.icon_url
        shareInfo.setDesc(event.desc)
        shareInfo.setLink(shareInfo.getLinkIntercept(event.link))

        //添加分享的打点信息
        shareInfo.screenName = TrackScreenName.SHARE_PAGE
        shareInfo.scene = TrackString.SCENE_GAME
        shareInfo.gameType = vm?.littleGameType ?: GameType.GAME_TYPE_JACKAROO
        val shortLink = IShareApi::class.impl().shareShortLinkCallback
        if (shortLink != null) {
            shortLink.shareShortLinkCallback(shareInfo.getLink()) { data ->
                shareInfo.setLink(data.short_url)
                IShareApi::class.impl().showShareDialog(context, shareInfo, null)
            }
        } else {
            IShareApi::class.impl().showShareDialog(context, shareInfo, null)
        }
    }

    private fun shareGame(gameType: Int, topic: String) {
        val bitmapPath: String = webView?.path + "game.png"
        IceBallDialogUtil.showGameShareDialog(
            context,
            bitmapPath,
            GAME_TYPE_VOICE_GAME_ROOM,
            topic,
            VoiceRoomService.getInstance().roomInfo.rid,
            gameType
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRoomInfoUpdate(event: RoomInfoUpdateEvent) {
        if (VoiceRoomService.getInstance().roomInfo.isNeedShowLeaveRoom) {
            // 高级房房主离开了
            closeGameView()
        }
    }

    private fun closeGameView() {
        if (checkResJob?.isActive == false) {
            loadingView.gone()
            vm?.emitEvent(VoiceRoomLittleGameEvent.ShowPrepareSeat(true))
        }
        if (tryCloseWebView()) {
            return
        }
        vm?.emitEvent(VoiceRoomLittleGameEvent.ShowPrepare(true))
        roomBg.show()
    }

    private fun tryCloseWebView(): Boolean {
        try {
            webView ?: return true
            webView?.let {
                ViewUtil.removeFromParent(it)
                it.destroy()
            }
            webView = null
            releaseMedia()
            return false
        } finally {
            ILittleGameApi::class.impl().preload(context)
        }
    }

    private fun updateLoadingUI(gameType: Int, fromGameChanged: Boolean = false) {
        if (webView != null) {
            return
        }
        val gameConfig = ConfigHelper.getInstance().getGameConfig(gameType)
        updateStateLoading(gameConfig)
        loadingView.show()

        vm?.emitEvent(VoiceRoomLittleGameEvent.ShowPrepareSeat(false))

        updateCocosRes(gameConfig, fromGameChanged)
    }

    private fun updateCocosRes(
        gameConfig: GameConfig,
        forceStartNewCheck: Boolean = false,
    ) {
        if (checkResJob?.isActive == true) {
            if (forceStartNewCheck) {
                HLog.d(TAG, HLog.USR, "cancel pre check task")
                checkResJob?.cancel()
            } else {
                return
            }
        }
        HLog.d(TAG, HLog.USR, "checkRes  start")
        checkResJob = viewScope.launch {
            CocosGameSilentPreloader.cancel(gameConfig.gameType)
            val loadResult = CocoResLoader(
                gameConfig.gameType,
                CocosResLoadConfig(null)
            ).checkRes {
                clearBg()
            }
            handleLoadResult(loadResult, gameConfig)
        }
    }

    private fun handleLoadResult(
        result: KtResult<String>,
        gameConfig: GameConfig
    ) {
        when (result) {
            is KtResultSuccess -> {
                HLog.d(TAG, HLog.USR, "checkRes ok")
                vm?.emitEvent(VoiceRoomLittleGameEvent.CheckResOk(gameConfig))
            }

            else -> {
                HLog.d(TAG, HLog.USR, "checkRes failed  result:$result")
                vm?.emitEvent(VoiceRoomLittleGameEvent.CheckResFail(gameConfig, result.failedDesc))
            }
        }
    }

    private fun clearBg() {
        roomBg.setImageDrawable(null)
        roomBg.setBackgroundColor(ColorUtil.getColor("#41837D"))
    }

    private fun onResume() {
        if (shareType > 0) {
            webView?.let {
                callShareCallback(shareType, 200, it)
                shareType = -1
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        EventBus.getDefault().register(this)
        context.toAppCompatActivity()?.lifecycle?.addObserver(this)
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        if (VoiceRoomService.getInstance().voiceGameViewModel.littleGameInfo.littleGameState !=
            VoiceGameModel.LittleGameState.Started
        ) {
            releaseMedia()
        }
        webView?.let {
            CocosBridgeInterface.callJsGameExit(it)
        }
        webView?.destroy()
        webView = null
        context.toAppCompatActivity()?.lifecycle?.removeObserver(this)
    }

    private fun releaseMedia() {
        HLog.d(TAG, HLog.USR, "releaseMedia")
        CocosSoundUtil.releaseAll()
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_RESUME) {
            onResume()
        }
    }

    private fun updateStateLoading(config: GameConfig) {
        if (!::observer.isInitialized) {
            observer = object : Observer<JKGameConfig> {
                override fun onChanged(value: JKGameConfig) {
                    val gameIcon = value.getGameModeInfo(
                        vm?.littleGameType ?: GameType.GAME_TYPE_JACKAROO,
                        vm?.gameMode ?: GameConfig.JACKAROO_MODE_COMPLEX
                    )?.icon ?: ""
                    if (TextUtil.isEmpty(gameIcon)) {
                        HLog.aliLog(
                            AliNetLogUtil.PORT.HttpConfig, AliNetLogUtil.TYPE.warning,
                            "gameType: ${vm?.littleGameType}"
                        )
                    } else {
                        loadingView.updateGameIcon(gameIcon)
                    }
                    JKGameConfig::class.liveData().removeObserver(this)
                }
            }
        }
        JKGameConfig::class.liveData().observe(toLife(), observer)
        loadingView.updateStateLoading(config.shortName)
    }

}