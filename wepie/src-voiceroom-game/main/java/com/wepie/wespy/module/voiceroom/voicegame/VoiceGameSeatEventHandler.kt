package com.wepie.wespy.module.voiceroom.voicegame

import android.content.Context
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ToastUtil
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.voicegame.data.VoiceGameSeatEventListener
import com.wepie.wespy.net.tcp.sender.VoiceGameRoomPacketSender
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object VoiceGameSeatEventHandler : VoiceGameSeatEventListener {

    private var lastApplySwitchSeatTime = 0L
    private const val SWITCH_WAIT_TIME_IN_MILLIS = 10_000L

    override fun onReqSit(uid: Int, seatId: Int) {
        val rid = VoiceGameRoomViewModel.get()?.littleGameInfo?.rid ?: return
        CoroutineScope(Dispatchers.IO).launch {
            VoiceGameRoomPacketSender.sitOnGameSeat(rid)
        }
    }

    override fun onReqChange(seatId: Int, uid: Int) {
        if (uid > 0) {
            val now = System.currentTimeMillis()
            if ((now - lastApplySwitchSeatTime) <= SWITCH_WAIT_TIME_IN_MILLIS) {
                ToastUtil.show(ResUtil.getStr(R.string.cocos_match_change_seat_fail_1))
                return
            }
        }
        val vm = VoiceGameRoomViewModel.get() ?: return
        CoroutineScope(Dispatchers.IO).launch {
            val ret = VoiceGameRoomPacketSender.applyExchangeGameSeat(
                vm.littleGameInfo.rid, vm.selfSeatId, uid, seatId
            )
            if (ret.codeOk() && uid > 0) {
                lastApplySwitchSeatTime = System.currentTimeMillis()
                ToastUtil.show(ResUtil.getStr(R.string.cocos_match_change_seat_tips_1))
            }
        }

    }

    override fun onKick(seatId: Int, uid: Int) {
        val vm = VoiceGameRoomViewModel.get() ?: return
        VoiceGameRoomPacketSender.kickUserFromGameSeat(vm.littleGameInfo.rid, seatId, uid)
    }

    override fun onUserClick(context: Context, uid: Int) {
        VoieGameDialogUtil.showUserDialogInGameSeat(context, null, uid, true)
    }
}