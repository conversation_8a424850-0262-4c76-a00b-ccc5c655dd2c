package com.wejoy.jackaroo.create

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.collectFlow
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.observer
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.JKGameConfig
import com.huiwan.configservice.constentity.JKGameConfig.Companion.SCENE_NORMAL_ROOM
import com.huiwan.configservice.constentity.JKGameConfig.Companion.SCENE_VIP_ROOM
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.configservice.editionentity.instance
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.impl
import com.huiwan.libtcp.callback.ContinuationSeqCallback
import com.huiwan.store.PrefUserUtil
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.wejoy.jackaroo.utils.jackarooShowSearchDialog
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.welib.alinetlog.AliNetLogUtil
import com.welib.alinetlog.AliNetLogUtil.PORT
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.MatchPacketPresenter
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameEventHandler
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wepie.wespy.model.event.RefreshSelfSuccessEvent
import com.wepie.wespy.module.pay.commonapi.JumpPay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class JackarooRoomCreateViewModel(app: Application) : AndroidViewModel(app),
    JackarooCreateIntentHandler {

    companion object {
        private const val KEY_LAST_CREATE_MODE = "_jk_rc_game_create_mode"
        private const val KEY_LAST_CREATE_BET_LEVEL = "_jk_rc_game_create_level"
        private const val KEY_LAST_CREATE_PRIVATE = "_jk_rc_game_create_private"
        private const val TAG = "VipRoomCreate"
    }

    private val pref = PrefUserUtil.getInstance()

    private val _status = MutableStateFlow(
        GameCreateRoomStatus(
            coinNum = LoginHelper.getLoginUser()?.chipCoin ?: 0
        )
    )
    private var matchInfoList: List<MatchInfo> = emptyList()
    private var showBottomScore: Boolean = false

    /**
     * 缓存记录一下上一个 gameMode 已选的 bet_level_index
     */
    private val betLevelIndexCache = mutableMapOf<Int, Int>()

    private val _action = MutableSharedFlow<JackarooCreateAction>()

    val action: SharedFlow<JackarooCreateAction> = _action
    val status: StateFlow<GameCreateRoomStatus> = _status

    init {
        observerChip()
        observerSelfInfo()
    }

    fun init(isVip: Boolean, gameType: Int) {
        val vipBgData = ConfigHelper.getInstance().gameConfig.getGameConfig(gameType)
            .generateHome.toVipBgData(gameType)
        _status.value = _status.value.copy(
            vipMode = isVip,
            gameType = gameType,
            vipBgData = vipBgData
        )
        val defaultMode = if (isVip) {
            GameConfig.JACKAROO_MODE_VIP_COMPLEX
        } else {
            GameConfig.JACKAROO_MODE_COMPLEX
        }
        val targetMode = pref.getInt(getKey(KEY_LAST_CREATE_MODE), defaultMode)
        val privateMode = pref.getBoolean(getKey(KEY_LAST_CREATE_PRIVATE), false)
        val gamEntryList = loadGameEntryListAndUpdateBottomScoreShow(status.value.gameType, isVip)
        _status.value =
            _status.value.copy(allowObserve = !privateMode, entryEntryList = gamEntryList)
        if (gamEntryList.isNotEmpty()) {
            refreshToGameMode(gamEntryList, targetMode)
        }
    }

    private fun updateBetLevelCache(gameMode: Int) {
        val betLevel = pref.getInt(getKey(KEY_LAST_CREATE_BET_LEVEL), 0)
        val list = matchInfoList.filter { it.gameMode == gameMode }
        val index = list.indexOfFirst { betLevel == it.betLevel }
        if (index >= 0) {
            betLevelIndexCache[gameMode] = index
        }
    }

    private fun loadGameEntryListAndUpdateBottomScoreShow(
        gameType: Int,
        isVip: Boolean
    ): List<GameBetEntryItem> {
        val jkGameTypeInfo = JKGameConfig::class.instance().getGameTypeInfo(gameType)
        showBottomScore = jkGameTypeInfo?.showBottomScore ?: false
        val allModeList =
            if (jkGameTypeInfo?.isShow == true) jkGameTypeInfo.gameModeList else emptyList()
        if (allModeList.isEmpty()) {
            logErrMsg("game mode list is empty,gameType:$gameType, jkGameType info.show:${jkGameTypeInfo?.isShow ?: false}")
            return emptyList()
        }
        return allModeList.map { it.toGameBetEntryItem(isVip) }
            .rearrangeGameModeOrder(gameType, isVip)
    }

    private fun List<GameBetEntryItem>.rearrangeGameModeOrder(
        gameType: Int,
        isVip: Boolean,
    ): List<GameBetEntryItem> {
        val orderTemplateList = ConfigHelper.getInstance().jkGameConfig.getOrderByScene(
            if (isVip) SCENE_VIP_ROOM else SCENE_NORMAL_ROOM
        ).filter { it.gameType == gameType }
        //没有指定顺序使用默认的
        if (orderTemplateList.isEmpty()) {
            return this
        }
        val srcEntrySeMap = this.associateBy { it.gameMode }.toMutableMap()
        val resultEntryList = ArrayList<GameBetEntryItem>()
        orderTemplateList.forEach {
            srcEntrySeMap.remove(it.gameMode)?.let { entry ->
                resultEntryList.add(entry)
            }
        }
        return resultEntryList
    }

    private fun adjustGameModeToFitNowGameType(
        targetGameMode: Int,
        gameEntryList: List<GameBetEntryItem>
    ): Int {
        return if (gameEntryList.any { it.gameMode == targetGameMode }) {
            targetGameMode
        } else {
            gameEntryList.first().gameMode
        }
    }

    private fun observerChip() {
        val flow = ApiService.of(ChipApi::class.java).gameChipFlow() ?: return
        viewModelScope.collectFlow(flow) {
            val self = LoginHelper.getLoginUser() ?: return@collectFlow
            _status.value = _status.value.copy(coinNum = self.chipCoin)
        }
    }

    private fun observerSelfInfo() {
        viewModelScope.observer(RefreshSelfSuccessEvent::class.java) {
            val self = LoginHelper.getLoginUser() ?: return@observer
            _status.value = _status.value.copy(coinNum = self.chipCoin)
        }
    }

    override fun onIntent(intent: GameCreateIntent) {
        when (intent) {
            is GameCreateIntent.ChangeGameMode -> {
                refreshToGameMode(_status.value.entryEntryList, intent.gameMode)
            }

            is GameCreateIntent.ChangeObserver -> {
                _status.value = _status.value.copy(allowObserve = intent.allow)
            }

            is GameCreateIntent.Create -> {
                val context = ActivityTaskManager.getInstance().topActivity ?: return
                checkReqCreate(context)
            }

            is GameCreateIntent.ShowHelpAction -> {
                val context = ActivityTaskManager.getInstance().topActivity ?: return
                if (matchInfoList.isEmpty()) {
                    logErrMsg("matchInfoList is empty,gameType:${_status.value.gameType},entry size:${status.value.entryEntryList.size}")
                    return
                }
                val matchInfo = matchInfoList[status.value.betLevelIndex]
                val gameMode = if (matchInfo.gameMode > 100) {
                    matchInfo.gameMode - 100
                } else {
                    matchInfo.gameMode
                }
                val info = LittleGameSimpleInfo(
                    status.value.gameType, matchInfo.matchMode,
                    gameMode, matchInfo.betLevel, matchInfo.currencyType
                )
                ILittleGameApi::class.impl().showGameRuleDialog(context, info)
            }

            is GameCreateIntent.DecEntryFeeAction -> {
                changeBetLevelIndex(status.value.betLevelIndex - 1)
            }

            is GameCreateIntent.IncEntryFeeAction -> {
                changeBetLevelIndex(status.value.betLevelIndex + 1)
            }

            is GameCreateIntent.JoinBtnAction -> {
                val context = ActivityTaskManager.getInstance().topActivity ?: return
                jackarooShowSearchDialog(context, status.value.gameType, status.value.vipMode)
            }

            is GameCreateIntent.ChargeCoinAction -> {
                val context = ActivityTaskManager.getInstance().topActivity ?: return
                JumpPay.showGoods(context, true, mapOf("refer_screen_name" to TrackScreenName.VIP_GAME_ROOM_CREATE))
            }
        }
    }

    private fun changeBetLevelIndex(targetIndex: Int) {
        val maxSize = matchInfoList.size
        if (maxSize == 0) {
            logErrMsg("matchInfoList is empty,gameType:${_status.value.gameType},entry size:${status.value.entryEntryList.size}")
            return
        }
        if (maxSize == 1) {
            if (!status.value.vipMode) {
                tipOnlyFree()
            }
            return
        }
        var newIndex = targetIndex
        if (newIndex >= maxSize) {
            newIndex = maxSize - 1
        }
        if (newIndex < 0) {
            newIndex = 0
        }
        betLevelIndexCache[status.value.selectedMode] = newIndex
        _status.value = _status.value.copy(
            betLevelIndex = newIndex,
            entryFeeData = matchInfoList[newIndex].toEntryFeeData(showBottomScore)
        )
    }

    private fun refreshMatchInfo(targetGameMode: Int) {
        val gameConfig = ConfigHelper.getInstance().getGameConfig(status.value.gameType)
        this.matchInfoList =
            gameConfig.matchInfoListForCreate.filter { it.gameMode == targetGameMode }
    }

    private fun refreshToGameMode(
        gamEntryList: List<GameBetEntryItem>,
        targetGameMode: Int
    ) {
        val afterAdjustGameMode =
            adjustGameModeToFitNowGameType(targetGameMode, gamEntryList)
        refreshMatchInfo(afterAdjustGameMode)
        updateBetLevelCache(afterAdjustGameMode)
        changeBetLevelIndex(betLevelIndexCache[afterAdjustGameMode] ?: 0)
        _status.value = _status.value.copy(
            coinNum = UserService.get().selfUser.value?.chipCoin ?: 0,
            selectedMode = afterAdjustGameMode,
        )
    }

    private fun tipOnlyFree() {
        val s = ResUtil.getStr(R.string.jackaroo_only_free_in_friends_mod_room)
        viewModelScope.launch {
            _action.emit(JackarooCreateAction.ShowToast(s))
        }
    }


    private fun checkReqCreate(context: Context) {
        viewModelScope.launch {
            if (matchInfoList.isEmpty()) {
                return@launch
            }
            val matchInfo = matchInfoList[status.value.betLevelIndex]
            val privateTeam = !status.value.allowObserve
            trackCreate(status.value, matchInfo, privateTeam)
            LittleGameEventHandler.checkShowResOrPermissionDialog(
                context,
                status.value.gameType,
                matchInfo
            ) {
                viewModelScope.launch {
                    doReqCreate(status.value, matchInfo, privateTeam)
                }
            }
        }
    }

    private fun trackCreate(
        status: GameCreateRoomStatus,
        matchInfo: MatchInfo,
        privateTeam: Boolean
    ) {
        val screenRes = if (status.vipMode)
            R.string.jackaroo_track_vip_room_create_page
        else
            R.string.jackaroo_track_room_create_page
        val screenName = ResUtil.getStr(screenRes)
        val map = mapOf(
            "game_type" to status.gameType,
            "mode_type" to TeamInfo.trackModeType(status.gameType, status.selectedMode),
            "bet_level" to matchInfo.betLevel,
            "enter_coin" to matchInfo.coin,
            "is_open" to !privateTeam
        )
        TrackUtil.appClick(screenName, TrackSource.CREATE_ROOM2, map)
    }

    private suspend fun doReqCreate(
        status: GameCreateRoomStatus,
        matchInfo: MatchInfo,
        privateTeam: Boolean
    ) {
        saveCacheData(matchInfo, privateTeam)
        if (status.vipMode) {
            doReqCreateForVip(status, matchInfo, privateTeam)
        } else {
            doReqCreateToTmpRoom(matchInfo)
        }
    }

    private suspend fun doReqCreateForVip(
        status: GameCreateRoomStatus,
        matchInfo: MatchInfo,
        privateTeam: Boolean
    ) {
        _action.emit(JackarooCreateAction.ShowLoading)
        val ret = suspendCancellableCoroutine {
            MatchPacketPresenter.createTeamReq2(
                false,
                matchInfo,
                status.gameType,
                privateTeam,
                ContinuationSeqCallback(it)
            )
        }
        _action.emit(JackarooCreateAction.HideLoading)
        if (ret.codeOk()) {
            CocosMatchUtil.saveMatchInfo(ret)
            _action.emit(JackarooCreateAction.JumpToWait(status.vipMode, status.gameType))
            return
        } else {
            val info = LittleGameSimpleInfo(
                status.gameType,
                matchInfo.matchMode,
                matchInfo.gameMode,
                matchInfo.betLevel,
                currencyType = matchInfo.currencyType,
                isCreate = true
            )
            info.joinFailedVipReferScreen = ResUtil.getStr(R.string.jackaroo_track_room_create)
            _action.emit(JackarooCreateAction.ShowError(ret.code, ret.desc, info) {
                viewModelScope.launch {
                    doReqCreate(status, matchInfo, privateTeam)
                }
            })
        }
    }

    private suspend fun doReqCreateToTmpRoom(
        matchInfo: MatchInfo
    ) {
        _action.emit(
            JackarooCreateAction.CreateTmpVoiceRoom(
                matchInfo,
                status.value.gameType
            )
        )
    }

    private fun logErrMsg(msg: String) {
        HLog.aliLog(PORT.performance, AliNetLogUtil.TYPE.err, msg)
        HLog.d(
            TAG,
            HLog.USR,
            msg
        )
    }

    private fun saveCacheData(matchInfo: MatchInfo, privateTeam: Boolean) {
        pref.setInt(getKey(KEY_LAST_CREATE_MODE), matchInfo.gameMode)
        pref.setInt(getKey(KEY_LAST_CREATE_BET_LEVEL), matchInfo.betLevel)
        pref.setBoolean(getKey(KEY_LAST_CREATE_PRIVATE), privateTeam)
    }

    private fun getKey(pref: String): String {
        return pref + status.value.vipMode
    }
}