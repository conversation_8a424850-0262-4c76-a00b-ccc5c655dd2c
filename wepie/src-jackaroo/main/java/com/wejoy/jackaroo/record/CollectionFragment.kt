package com.wejoy.jackaroo.record

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiwan.base.collectOn
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.instance
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.wepie.module.rank.activity.NewRankActivity
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooCollectionFragmentContentBinding
import com.wepie.wespy.module.common.jump.JumpUtil
import kotlinx.coroutines.launch

class CollectionFragment : Fragment() {
    private lateinit var binding: JackarooCollectionFragmentContentBinding
    private val viewModel: JackarooGameCareerViewModel by lazy { ViewModelProvider(requireActivity())[JackarooGameCareerViewModel::class.java] }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return JackarooCollectionFragmentContentBinding.inflate(inflater, container, false).let {
            binding = it
            it.root
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initView()
        initData()
    }

    private fun onClickCollectionRank(context: Context) {
        JumpUtil.gotoRankActivity(context, NewRankActivity.RANK_TAB_INDEX_JK, 1)
    }

    private fun onClickCollectionValueHelp(context: Context) {
        //todo 收藏帮助页 ConstV3Info::class.instance().collectionHelpUrl
        HalfHeightBottomWebDialog.show(
            context,
            ResUtil.getStr(R.string.jackaroo_game_collection_intro),
            ConstV3Info::class.instance().collectionHelpUrl
        )
    }


    private fun onClickGradeItem(context: Context, grade: Int) {
        //todo 打开等级详情弹窗

    }

    private fun initView() {
        val adapter = CollectionPageAdapter(object : CollectionGradeAction {
            override fun onClickGrade(context: Context, grade: Int) {
                viewModel.notifyEvent(JackarooGameCareerEvent.ShowGradeDetail(grade))
            }
        })
        val headAdapter = CollectionPageHeadAdapter(
            ::onClickCollectionValueHelp,
            ::onClickCollectionRank
        )
        binding.collectionRv.adapter = ConcatAdapter(headAdapter, adapter)
        binding.collectionRv.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        binding.collectionRv.addItemDecoration(SpaceItemDecoration(0, 0, 12.dp, 0))
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.collectionPageStateFlow.collect {
                    if (it is CollectionPageState.LoadSuccess) {
                        headAdapter.update(it.headInfo)
                        adapter.refresh(it.gradeOverview)
                    }
                }
            }
        }
    }

    private fun initData() {
        viewModel.flow.collectOn(lifecycleScope) {
            if (it is JackarooGameCareerEvent.ShowGradeDetail) {
                JackarooCollectionDialogFragment.show(requireActivity(), viewModel.uid, it.grade)
            }
        }
    }

}