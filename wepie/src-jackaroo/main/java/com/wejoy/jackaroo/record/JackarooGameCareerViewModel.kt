package com.wejoy.jackaroo.record

import android.util.Log
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.configservice.model.PropItem
import com.huiwan.lib.api.ApiService
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserServiceKt
import com.huiwan.user.entity.User
import com.wejoy.weplay.ex.view.postAutoCancel
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.wespy.databinding.JackarooGameRecordActivityBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class JackarooGameCareerViewModel : ViewModel() {
    private val _collectionAlbumFlow: MutableStateFlow<CollectionAlbum?> =
        MutableStateFlow(null)
    val collectionAlbumFlow = _collectionAlbumFlow.asStateFlow()

    internal val collectionPageStateFlow: StateFlow<CollectionPageState> =
        _collectionAlbumFlow.mapLatest {
            it.CollectionPageState()
        }.stateIn(
            scope = viewModelScope,
            initialValue = CollectionPageState.Idle,
            started = SharingStarted.WhileSubscribed(5_000),
        )

    val shouldHideGameRecord: Boolean
        get() = LoginHelper.getLoginUid() != uid && _userInfoStateFlow.value?.achivement_not_show != 0

    private val _userInfoStateFlow: MutableStateFlow<User?> = MutableStateFlow(null)
    val userInfoStateFlow = _userInfoStateFlow.asStateFlow()

    private val _recordInfoLiveData = MutableLiveData<UserRecordInfo>()
    val recordInfoLiveData: LiveData<UserRecordInfo> = _recordInfoLiveData

    private val _collectionTypeFlow = MutableStateFlow(JackarooCollectionInfo())
    val collectionTypeFlow: Flow<JackarooCollectionInfo> = _collectionTypeFlow.asStateFlow()

    private val _eventFlow = MutableSharedFlow<JackarooGameCareerEvent>()

    val flow: Flow<JackarooGameCareerEvent> = _eventFlow
    var uid = 0
        private set

    val GAME_CAREER_TAB_COLLECTIONINDEX = 0
    val GAME_CAREER_TAB_GAME_RECORD_INDEX = 1

    fun init(uid: Int) {
        if (this.uid == uid) {
            return
        }
        this.uid = uid
        viewModelScope.launch {
            val info = UserServiceKt.get().getCacheUser(uid) ?: return@launch
            _userInfoStateFlow.value = info
        }
        GameCareerCache.collectionAlbumFlow(uid, viewModelScope).let { albumFlow ->
            viewModelScope.launch {
                albumFlow.collectLatest {
                    _collectionAlbumFlow.value = it
                }
            }
        }
        GameCareerCache.userRecordInfoFlow(uid, viewModelScope).let { userRecordFlow ->
            viewModelScope.launch {
                userRecordFlow.collect {
                    it?.let {
                        _recordInfoLiveData.value = it
                    }
                }
            }
        }

        GameCareerCache.userFlow(uid, viewModelScope).let { userFlow ->
            viewModelScope.launch {
                userFlow.collectLatest {
                    _userInfoStateFlow.value = it
                }
            }

        }
    }

    fun isGameRecordTab(gameCareerTabIndex: Int): Boolean =
        gameCareerTabIndex == GAME_CAREER_TAB_GAME_RECORD_INDEX

    fun notifyEvent(event: JackarooGameCareerEvent) {
        viewModelScope.launch {
            _eventFlow.emit(event)
        }
    }

    fun onCollectionIntent(intent: JackarooCollectionIntent) {
        when (intent) {
            is JackarooCollectionIntent.ShowDialog -> onShowCollectionDialog(intent.level)
            is JackarooCollectionIntent.ShowMore -> onShowCollectionMore(intent.type)
        }
    }

    private fun onShowCollectionDialog(grade: Int) {
        if (_collectionTypeFlow.value.grade == grade) {
            return
        }

        val gradeItem = _collectionAlbumFlow.value?.items?.find { it.grade == grade }
            ?: CollectionGradeItem()
        viewModelScope.launch(Dispatchers.IO) {
            val DISPLAY_COUNT = 6
            var total = 0
            val config = PropItemConfig::class.java.instance()
            val typeList = mutableListOf<JackarooCollectionType>()
            for (gradeItem in gradeItem.props) {
                val allList: MutableList<PropItem> =
                    GameCareerCache.getPropItemList(gradeItem.type, grade).toMutableList()
                val obtainedList = gradeItem.obtainedPropIds.mapNotNull {
                    config.getPropItem(it)
                }
                var lockedList = allList.filter {
                    !gradeItem.obtainedPropIds.contains(it.itemId) && it.vipLevel < LIMIT_VIP_LEVEL
                }

                if (obtainedList.isEmpty() && lockedList.isEmpty()) {
                    continue
                }
                lockedList = lockedList.toMutableList()
                lockedList.sortWith(Comparator { o1, o2 -> o1.itemId - o2.itemId })
                Log.e("jhk", "lockedList==${lockedList.map { it.itemId }}")
                val size = obtainedList.size + lockedList.size
                val showList = buildList {
                    add(JackarooCollectionTitle(gradeItem.typeName))
                    addAll(obtainedList.take(DISPLAY_COUNT).map { item ->
                        JackarooCollectionContent(item, false)
                    })
                    addAll(lockedList.take(DISPLAY_COUNT - obtainedList.size).map { item ->
                        JackarooCollectionContent(item, true)
                    })
                    if (size > DISPLAY_COUNT) {
                        add(JackarooCollectionMore(gradeItem.type))
                    }
                }
                total = total + size
                typeList.add(
                    JackarooCollectionType(
                        gradeItem.type, gradeItem.typeName, showList, obtainedList, lockedList
                    )
                )
            }
            val info = JackarooCollectionInfo(
                gradeItem.grade, gradeItem.obtainedNum,
                total, typeList
            )
            _collectionTypeFlow.emit(info)
        }
    }

    private fun onShowCollectionMore(type: Int) {
        viewModelScope.launch {
            val value = _collectionTypeFlow.value
            val newTypeList = value.typeList.map { t ->
                if (t.type == type) {
                    val list = buildList {
                        add(JackarooCollectionTitle(t.name))
                        addAll(t.obtainedList.map { JackarooCollectionContent(it, false) })
                        addAll(t.lockedList.map { JackarooCollectionContent(it, true) })
                    }
                    t.copy(showList = list)
                } else {
                    t
                }
            }
            _collectionTypeFlow.emit(value.copy(typeList = newTypeList))
        }
    }

    internal fun doShare(
        context: FragmentActivity,
        binding: JackarooGameRecordActivityBinding,
        gameCareerTabIndex: Int,
        gameRecordTabIndex: Int,
        gameRecordTabSubIndex: Int,
    ) {
        val currentTabInfo: GameCareerTabInfo =
            buildTabInfo(gameCareerTabIndex, gameRecordTabIndex, gameRecordTabSubIndex)
        val info = recordInfoLiveData.value ?: return
        val holder =
            JackarooGameCareerShareViewHolder(context, binding.jackarooGameRecordShareLay)
        holder.init(
            _userInfoStateFlow.value,
            GameCareerInfo(
                jackarooGameInfo = info.rankStat,
                collectionAlbum = collectionAlbumFlow.value
            ),
            currentTabInfo
        )
        holder.rootView.postAutoCancel(200) {
            val bitmap = holder.getBitmap() ?: return@postAutoCancel
            binding.jackarooGameRecordShareLay.removeAllViews()
            val shareInfo = ShareInfo()
            shareInfo.setTitle(ConfigHelper.getInstance().myShareTitle)
            shareInfo.content = ConfigHelper.getInstance().myShareDesc
            shareInfo.setLink(shareInfo.getLinkIntercept(ConfigHelper.getInstance().myShareUrl))

            shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_IMG)
            shareInfo.screenName = TrackScreenName.SHARE_PAGE
            shareInfo.scene = TrackString.SCENE_USER_RECORD
            shareInfo.gameType = -1
            shareInfo.setBitmap(bitmap)
            val shareApi = ApiService.of(IShareApi::class.java)
            val sharePictureDialogWithShortLink = shareApi.sharePictureDialogWithShortLink
            if (sharePictureDialogWithShortLink != null) {
                sharePictureDialogWithShortLink.sharePictureDialogWithShortLink(
                    context, shareInfo, object : ShareCallback {
                        override fun onShare(data: ShareResult): Boolean {
                            if (data.shareType == ShareType.saveBmp) {
                                ToastUtil.show(data.msg)
                            }
                            return false
                        }
                    })
            } else {
                shareApi.showPictureShareDialog(context, shareInfo, object : ShareCallback {
                    override fun onShare(data: ShareResult): Boolean {
                        if (data.shareType == ShareType.saveBmp) {
                            ToastUtil.show(data.msg)
                        }
                        return false
                    }
                })
            }
        }
    }

    private fun buildTabInfo(
        gameCareerTabIndex: Int,
        gameRecordTabIndex: Int,
        gameRecordTabSubIndex: Int,
    ): GameCareerTabInfo {
        return if (isGameRecordTab(gameCareerTabIndex)) {
            GameCareerTabInfo.GameRecordInfo(gameRecordTabIndex, gameRecordTabSubIndex)
        } else {
            GameCareerTabInfo.CollectionTab
        }
    }

    private fun CollectionAlbum?.CollectionPageState(): CollectionPageState {
        return if (this == null) {
            CollectionPageState.Idle
        } else {
            CollectionPageState.LoadSuccess(
                headInfo = CollectionHeadInfo(
                    collectionValue,
                    collectionRank
                ), gradeOverview = items.map { it.toCollectionPageItem() })
        }
    }
}

sealed class JackarooGameCareerEvent {
    data class SwitchGameCareerTab(val tabIndex: Int) : JackarooGameCareerEvent()
    data class SwitchGameGameRecordTab(val tabIndex: Int) : JackarooGameCareerEvent()
    data class SelectGameType(val index: Int) : JackarooGameCareerEvent()

    data class ShowGradeDetail(val grade: Int) : JackarooGameCareerEvent()
}

internal sealed interface CollectionPageState {
    data object Idle : CollectionPageState
    data class LoadSuccess(
        val headInfo: CollectionHeadInfo,
        val gradeOverview: List<CollectionPageItem>
    ) : CollectionPageState

    data class LoadFail(val msg: String) : CollectionPageState
}

internal sealed interface GameCareerTabInfo {
    data object CollectionTab : GameCareerTabInfo
    data class GameRecordInfo(val index: Int, val subIndex: Int) : GameCareerTabInfo
}

sealed interface JackarooCollectionIntent {
    class ShowDialog(val level: Int) : JackarooCollectionIntent
    class ShowMore(val type: Int) : JackarooCollectionIntent
}