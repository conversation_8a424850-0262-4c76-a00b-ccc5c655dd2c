package com.wejoy.jackaroo.record

import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.FontUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.model.PropItem
import com.huiwan.widget.CustomTextView
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.inflate
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R

interface IJackarooCollection

data class JackarooCollectionTitle(val title: String) : IJackarooCollection

class JackarooCollectionContent(
    val item: PropItem, val locked: Boolean = true
) : IJackarooCollection {

    companion object {
        val EMPTY = JackarooCollectionContent(PropItem(), false)
    }
}

class JackarooCollectionMore(val type: Int) : IJackarooCollection

class JackarooCollectionDialogAdapter(private val callback: IJackarooCollectionItemCallback) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val data = mutableListOf<IJackarooCollection>()

    fun refresh(list: List<JackarooCollectionType>) {
        data.clear()
        list.forEach { type ->
            data.addAll(type.showList)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_CONTENT -> JackarooCollectionSkinViewHolder.create(parent)
            TYPE_MORE -> JackarooCollectionMoreViewHolder.create(parent)
            else -> JackarooCollectionTitleViewHolder.create(parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is JackarooCollectionTitleViewHolder -> {
                holder.bindView(data[position])
            }

            is JackarooCollectionSkinViewHolder -> {
                holder.bindView(data[position], callback)
            }

            is JackarooCollectionMoreViewHolder -> {
                holder.bindView(data[position], callback)
            }
        }
    }

    override fun getItemCount(): Int = data.size

    override fun getItemViewType(position: Int): Int {
        return data[position].let {
            when (it) {
                is JackarooCollectionTitle -> TYPE_TITLE
                is JackarooCollectionContent -> TYPE_CONTENT
                is JackarooCollectionMore -> TYPE_MORE
                else -> TYPE_TITLE
            }
        }
    }

    companion object {
        val TYPE_TITLE = 0
        val TYPE_CONTENT = 1
        val TYPE_MORE = 2

        val padding = (ScreenUtil.getScreenWidth() - (108 * 3 + 16 * 2).dp) / 4
    }
}

class JackarooCollectionTitleViewHolder(private val tv: TextView) : RecyclerView.ViewHolder(tv) {

    fun bindView(item: IJackarooCollection) {
        tv.text = (item as? JackarooCollectionTitle)?.title ?: ""
    }

    companion object {
        fun create(parent: ViewGroup): RecyclerView.ViewHolder {
            val tv = CustomTextView(parent.context)
            tv.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            tv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
            tv.setTextColor(ResUtil.getColor(R.color.color_text_primary))
            tv.setPaddingRelative(
                JackarooCollectionDialogAdapter.padding, 16.dp,
                JackarooCollectionDialogAdapter.padding, 8.dp
            )
            FontUtil.setTextStyle(tv, Typeface.BOLD)
            return JackarooCollectionTitleViewHolder(tv)
        }
    }
}

class JackarooCollectionMoreViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private var callback: IJackarooCollectionItemCallback? = null

    init {
        itemView.setOnClickListener {
            val type = itemView.tag as? Int ?: 0
            callback?.onMoreClick(type)
        }
    }

    fun bindView(item: IJackarooCollection, callback: IJackarooCollectionItemCallback) {
        this.callback = callback
        val type = (item as? JackarooCollectionMore)?.type ?: 0
        itemView.tag = type
    }

    companion object {
        fun create(parent: ViewGroup): RecyclerView.ViewHolder {
            return JackarooCollectionMoreViewHolder(parent.inflate(R.layout.jackaroo_collection_item_more_view))
        }
    }
}

class JackarooCollectionSkinViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val skinIv = itemView.findViewById<ImageView>(R.id.jackaroo_skin_iv)
    private val lockIv = itemView.findViewById<ImageView>(R.id.jackaroo_skin_lock_iv)
    private val skinName = itemView.findViewById<TextView>(R.id.jackaroo_skin_name_tv)

    private var callback: IJackarooCollectionItemCallback? = null

    init {
        itemView.findViewById<View>(R.id.jackaroo_skin_lay).let {
            it.clipToOutline = true
            it.outlineProvider = SimpleOutlineProvider(dp8)
        }
        itemView.setOnClickListener {
            val content = itemView.tag as? JackarooCollectionContent ?: return@setOnClickListener
            callback?.onItemClick(content.item, content.locked)
        }
    }

    fun bindView(item: IJackarooCollection, callback: IJackarooCollectionItemCallback) {
        this.callback = callback
        val content = (item as? JackarooCollectionContent) ?: JackarooCollectionContent.EMPTY
        val propItem = content.item
        if (propItem.mediaUrl.isNullOrEmpty()) {
            skinIv.setImageDrawable(ColorDrawable(Color.TRANSPARENT))
        } else {
            WpImageLoader.load(propItem.mediaUrl, skinIv)
        }
        skinName.text = propItem.name
        lockIv.isVisible = content.locked
        itemView.tag = content
    }

    companion object {
        private val dp8 = ScreenUtil.dip2px(8F).toFloat()

        fun create(parent: ViewGroup): RecyclerView.ViewHolder {
            return JackarooCollectionSkinViewHolder(parent.inflate(R.layout.jackaroo_collection_item_skin_view))
        }
    }
}