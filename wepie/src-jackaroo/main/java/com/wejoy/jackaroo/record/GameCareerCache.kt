package com.wejoy.jackaroo.record

import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.configservice.editionentity.liveData
import com.huiwan.configservice.model.PropItem
import com.huiwan.user.UserServiceKt
import com.huiwan.user.entity.User
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.jackaroo.api.getAchievement
import com.wejoy.jackaroo.api.getCollectionAlbum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

object GameCareerCache {
    private val tag = "GameCareerCache"

    @Volatile
    private var userRecordInfo: UserData<UserRecordInfo>? = null

    @Volatile
    private var collectionAlbum: UserData<CollectionAlbum>? = null

    @Volatile
    private var userInfo: UserData<User>? = null

    private val propItemCache = ConcurrentHashMap<Int, List<PropItem>>()

    init {
        PropItemConfig::class.liveData().observeForever {
            propItemCache.clear()
        }
    }

    private fun <T> getOrUpdate(
        uid: Int,
        scope: CoroutineScope,
        userdata: UserData<T>?,
        userDataUpdater: (data: UserData<T>) -> Unit,
        dataFetcher: suspend () -> KtResult<T>
    ): StateFlow<T?> {
        val dataFlow = MutableStateFlow<T?>(null)
        scope.launch {
            if (userdata == null || userdata.uid != uid) {
                val ret = dataFetcher()
                if (ret !is KtResultSuccess) {
                    ToastUtil.show(ret.failedDesc)
                    return@launch
                } else {
                    userDataUpdater(UserData(uid, ret.data))
                    dataFlow.emit(ret.data)
                }
            } else {
                dataFlow.emit(userdata.data)
            }
        }
        return dataFlow.asStateFlow()
    }

    fun userRecordInfoFlow(uid: Int, scope: CoroutineScope): StateFlow<UserRecordInfo?> {
        return getOrUpdate(
            uid, scope, userRecordInfo,
            dataFetcher = { getAchievement(uid) },
            userDataUpdater = { userRecordInfo = it })
    }

    fun collectionAlbumFlow(uid: Int, scope: CoroutineScope): StateFlow<CollectionAlbum?> {
        return getOrUpdate(
            uid, scope, collectionAlbum,
            dataFetcher = { getCollectionAlbum(uid) },
            userDataUpdater = { collectionAlbum = it })
    }

    fun userFlow(uid: Int, scope: CoroutineScope): StateFlow<User?> {
        return getOrUpdate(
            uid, scope, userInfo, dataFetcher = {
                val user = UserServiceKt.get().getCacheUser(uid)
                if (user == null) {
                    KtResultFailed(-1, "fail to load user")
                } else {
                    KtResultSuccess(user)
                }
            }, userDataUpdater = { userInfo = it })
    }

    private fun getPropItemGradeMap(types: List<Int>): Map<Int, List<PropItem>> {
        val localMap = mutableMapOf<Int, MutableList<PropItem>>()
        PropItemConfig::class.instance().allPropItemList.forEach {
            if (it.type in types) {
                val grade = it.parseExtAsInt(KEY_GRADE) ?: 0
                val list = localMap.getOrPut(grade shl 8 or it.type) { mutableListOf() }
                list.add(it)
            }
        }
        return localMap
    }

    fun getPropItemList(type: Int, grade: Int): List<PropItem> {
        val key = grade shl 8 or type
        val list = propItemCache.get(key)
        if (list != null) {
            return list
        }
        val map = getPropItemGradeMap(listOf(type))
        propItemCache.putAll(map)
        return map[key] ?: emptyList()
    }

    fun visitPropItem(grade: Int, types: List<Int>, visitor: (PropItem) -> Unit) {
        val map = mutableMapOf<Int, List<PropItem>>()
        val missTypes = types.toMutableList()
        val iterator = missTypes.iterator()
        while (iterator.hasNext()) {
            val key = grade shl 8 or iterator.next()
            val list = propItemCache.get(key)
            if (list != null) {
                map[key] = list
                iterator.remove()
            }
        }
        if (missTypes.isNotEmpty()) {
            val localMap = getPropItemGradeMap(missTypes)
            propItemCache.putAll(localMap)
            missTypes.forEach {
                val key = grade shl 8 or it
                map.put(key, localMap[key] ?: emptyList())
            }
        }
        map.forEach { _, list ->
            list.forEach(visitor)
        }
    }

    //道具 的 extraJsonString 中ItemGrade字段的key
    const val KEY_GRADE = "item_grade"

    private fun PropItem.parseExtAsInt(key: String): Int? {
        kotlin.runCatching {
            return parseExtraPrimitive(key)?.toInt()
        }
        return null
    }
}

private data class UserData<T>(
    val uid: Int,
    val data: T,
)