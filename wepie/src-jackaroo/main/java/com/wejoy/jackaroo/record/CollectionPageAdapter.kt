package com.wejoy.jackaroo.record

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ui.Utils.dp
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.wepie.wespy.databinding.CollectionPageHeadBinding
import com.wepie.wespy.databinding.JackarooCollectionPageItemBinding

internal class CollectionPageAdapter(private val action: CollectionGradeAction) :
    RecyclerView.Adapter<CollectionPageVh>() {
    private val data: MutableList<CollectionPageItem> = ArrayList()
    fun refresh(data: List<CollectionPageItem>) {
        this.data.clear()
        this.data.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CollectionPageVh {
        return CollectionPageVh(
            JackarooCollectionPageItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), action
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: CollectionPageVh, position: Int) {
        holder.bind(data[position])
    }
}

internal class CollectionPageVh(
    private val binding: JackarooCollectionPageItemBinding,
    private val action: CollectionGradeAction
) :
    RecyclerView.ViewHolder(binding.root) {
    private val fixedPropItemSize = 5
    private val adapter: CollectionPropItemAdapter = CollectionPropItemAdapter(fixedPropItemSize)
    private val propListPaddingEnd = 12.dp
    private val propItemWidth = 60.dp

    init {
        binding.propList.layoutManager =
            LinearLayoutManager(binding.root.context, LinearLayoutManager.HORIZONTAL, false)

        binding.propList.post {
//            val rightPadding =
//                ((binding.propList.measuredWidth - propListPaddingEnd) - (fixedPropItemSize * propItemWidth)) / (fixedPropItemSize - 1)
            binding.propList.addItemDecoration(SpaceItemDecoration(0, 5.dp, 0, 0))
        }
        binding.propList.adapter = adapter
    }

    fun bind(item: CollectionPageItem) {
        binding.collectionGradeView.update(item.grade, item.ownedNum, item.totalNum)
        binding.root.setOnDoubleClick {
            action.onClickGrade(binding.root.context, item.grade)
        }
        adapter.refresh(item.propList)
    }
}

internal class CollectionPageHeadAdapter(
    private val onClickCollectionValueHelp: (context: Context) -> Unit,
    private val onClickCollectionRank: (context: Context) -> Unit
) : RecyclerView.Adapter<CollectionPageHeadVh>() {
    var headInfo: CollectionHeadInfo? = null

    fun update(headInfo: CollectionHeadInfo) {
        this.headInfo = headInfo
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CollectionPageHeadVh {
        return CollectionPageHeadVh(
            CollectionPageHeadBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), onClickCollectionValueHelp, onClickCollectionRank
        )
    }

    override fun getItemCount(): Int = if (headInfo == null) {
        0
    } else 1

    override fun onBindViewHolder(holder: CollectionPageHeadVh, position: Int) {
        holder.bind(headInfo)
    }
}

internal class CollectionPageHeadVh(
    private val binding: CollectionPageHeadBinding,
    onClickCollectionValueHelp: (context: Context) -> Unit,
    onClickCollectionRank: (context: Context) -> Unit
) :
    RecyclerView.ViewHolder(binding.root) {
    init {
        binding.init(onClickCollectionValueHelp, onClickCollectionRank)
    }

    private fun CollectionPageHeadBinding.init(
        onClickCollectionValueHelp: (context: Context) -> Unit,
        onClickCollectionRank: (context: Context) -> Unit
    ) {
        collectionValueHelp.setOnDoubleClick {
            onClickCollectionValueHelp(it.context)
        }
        collectionValueTitle.setOnDoubleClick {
            onClickCollectionValueHelp(it.context)
        }

        collectionRankTitle.setOnDoubleClick {
            onClickCollectionRank(it.context)
        }
        collectionRankArrow.setOnDoubleClick {
            onClickCollectionRank(it.context)
        }
    }

    fun bind(headInfo: CollectionHeadInfo?) {
        headInfo?.let {
            binding.bind(it)
        }
    }
}

internal data class CollectionPageItem(
    val grade: Int,
    val ownedNum: Int,
    val totalNum: Int,
    val propList: List<Int>
)

internal interface CollectionGradeAction {
    /**
     * @param grade 等级 SS等
     */
    fun onClickGrade(context: Context, grade: Int)
}