package com.wejoy.jackaroo.record

import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.huiwan.base.ktx.dp
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.util.BitmapUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.user.entity.User
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooGameRecordHeadViewBinding
import com.wepie.wespy.utils.QRCodeEncodingUtils

class JackarooGameCareerShareViewHolder(private val activity: FragmentActivity, parent: ViewGroup) {

    val rootView: View
    val headViewHolder: JackarooGameRecordHeadViewHolder

    init {
        parent.removeAllViews()
        rootView = activity.layoutInflater.inflate(R.layout.jackaroo_game_record_share_view, parent)
        headViewHolder = JackarooGameRecordHeadViewHolder(
            JackarooGameRecordHeadViewBinding.bind(rootView.findViewById(R.id.header))
        )

        val descTv = rootView.findViewById<TextView>(R.id.jackaroo_app_desc_tv)
        val slogan = ConfigHelper.getInstance().appSlogan
        if (slogan.isNullOrEmpty()) {
            descTv.gone()
        } else {
            descTv.text = slogan
        }
        initQrCode()
    }

    fun getBitmap(): Bitmap? {
        val bitmap = getBitmap(rootView)
        if (bitmap == null) {
            return null
        }
        return BitmapUtil.getRoundBitmap(bitmap, 8.dp.toFloat())
    }

    private fun getBitmap(view: View): Bitmap? {
        if (view.measuredWidth <= 0 || view.measuredHeight <= 0) {
            return null
        }
        val bitmap = Bitmap.createBitmap(
            view.measuredWidth, view.measuredHeight,
            Bitmap.Config.ARGB_8888
        )
        view.draw(Canvas(bitmap))
        return bitmap
    }

    private fun initQrCode() {
        val url = ConfigHelper.getInstance().appShareUrl
        val qrcodeBitmap = QRCodeEncodingUtils.createQRCode(
            url, ScreenUtil.dip2px(68f), ScreenUtil.dip2px(68f), null
        )
        rootView.findViewById<ImageView>(R.id.qrcode_iv)?.setImageBitmap(qrcodeBitmap)
    }

    internal fun init(userInfo: User?, careerInfo: GameCareerInfo, tabInfo: GameCareerTabInfo) {
        val labelView = activity.findViewById<View>(R.id.jackaroo_game_career_label_lay)
        rootView.findViewById<ImageView>(R.id.jackaroo_game_record_label_iv)
            .setImageBitmap(getBitmap(labelView))
        headViewHolder.updateUser(userInfo)
        val inUseChessPropId = careerInfo.collectionAlbum?.inUseChessPropId ?: 0
        if (tabInfo is GameCareerTabInfo.GameRecordInfo) {
            headViewHolder.update(
                careerInfo.jackarooGameInfo ?: JackarooGameInfo(),
            )
            headViewHolder.showRankInfo()
        } else {
            headViewHolder.updateChessInfo(inUseChessPropId, true)
            headViewHolder.showInUseChessInfo()
        }
        updateGameCareerBg(
            rootView.findViewById<ImageView>(R.id.jackaroo_game_record_head_bg),
            inUseChessPropId
        )
        val widthSpec = View.MeasureSpec.makeMeasureSpec(
            ScreenUtil.getScreenWidth(), View.MeasureSpec.EXACTLY
        )
        val heightSpec = View.MeasureSpec.makeMeasureSpec(
            ScreenUtil.getScreenHeight(), View.MeasureSpec.EXACTLY
        )
        initContentByTabInfo(tabInfo)
        rootView.measure(widthSpec, heightSpec)
    }

    private fun initContentByTabInfo(tabInfo: GameCareerTabInfo) {
        when (tabInfo) {
            is GameCareerTabInfo.CollectionTab -> {
                showFragment(CollectionFragment())
            }

            is GameCareerTabInfo.GameRecordInfo -> {
                val gameType =
                    ConfigHelper.getInstance().jkHomeConfig.gameStatsConfig.totalGameStat[tabInfo.subIndex].gameType
                showFragment(GameRecordFragment.newInstance(tabInfo.index, gameType, true))
            }
        }
    }

    private fun showFragment(fragment: Fragment) {
        activity.supportFragmentManager.beginTransaction().apply {
            replace(R.id.jackaroo_game_career_sub_container, fragment)
            commitNowAllowingStateLoss()
        }
    }

}