package com.wejoy.jackaroo.record

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.huiwan.base.ui.Utils.dp
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.widget.image.DrawableUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.CollectionGradePropItemBinding
import kotlin.math.min

internal class CollectionPropItemAdapter(private val fixedSize: Int) :
    RecyclerView.Adapter<CollectionPropItemVh>() {
    private val data: MutableList<CollectionPropItemData> = ArrayList()

    fun refresh(data: List<Int>) {
        this.data.clear()
        this.data.addAll(
            data.subList(
                0,
                min(data.size, fixedSize)
            ).map(::mapPropId2Data).fillToFixedSize()
        )
        notifyDataSetChanged()
    }

    private fun List<CollectionPropItemData>.fillToFixedSize(targetSize: Int = fixedSize): List<CollectionPropItemData> {
        return if (size < targetSize) {
            ArrayList<CollectionPropItemData>().apply {
                addAll(this@fillToFixedSize)
                repeat(targetSize - size) {
                    add(EmptyPropItemData)
                }
            }
        } else this
    }

    private fun mapPropId2Data(propId: Int): CollectionPropItemData {
        val propConfig =
            PropItemConfig::class.instance().getPropItem(propId) ?: return EmptyPropItemData
        return CollectionPropItemData(false, propConfig.mediaUrl ?: "", propConfig.name)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CollectionPropItemVh {
        return CollectionPropItemVh(
            CollectionGradePropItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: CollectionPropItemVh, position: Int) {
        holder.update(data[position])
    }
}

internal class CollectionPropItemVh(private val binding: CollectionGradePropItemBinding) :
    ViewHolder(binding.root) {
    init {
        binding.propIv.background = DrawableUtil.genColorRadius(Color.parseColor("#e6e7ec"), 8)
    }

    fun update(item: CollectionPropItemData) {
        if (item.isEmpty) {
            binding.propIv.setPadding(9.dp)
        } else {
            binding.propIv.setPadding(5.dp)
        }
        binding.propTv.text = item.propName
        WpImageLoader.load(
            item.propModel,
            binding.propIv,
            ImageLoadInfo().width(48.dp).height(48.dp).setCornerPixel(4.dp)
                .placeholder(R.drawable.jackaroo_collection_prop_empty)
        )
    }
}

internal data class CollectionPropItemData(
    val isEmpty: Boolean,
    val propModel: Any,
    val propName: String,
)

private val EmptyPropItemData =
    CollectionPropItemData(true, R.drawable.jackaroo_collection_prop_empty, "")