package com.wejoy.jackaroo.record

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.collectOn
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.model.PropItem
import com.huiwan.user.LoginHelper
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.module.shop.dialogs.PropDialogCallback
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog

class JackarooCollectionDialogFragment : BaseDialogFragment() {

    private val viewModel: JackarooGameCareerViewModel by lazy {
        ViewModelProvider(requireActivity())[JackarooGameCareerViewModel::class.java]
    }

    private lateinit var gradeView: CollectionGradeView

    private val mAdapter: JackarooCollectionDialogAdapter by lazy {
        JackarooCollectionDialogAdapter(object : IJackarooCollectionItemCallback {
            override fun onMoreClick(type: Int) {
                viewModel.onCollectionIntent(JackarooCollectionIntent.ShowMore(type))
            }

            override fun onItemClick(item: PropItem, locked: Boolean) {
                showPropInfoDialog(item, locked)
            }
        })
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val dragView = WpDragDialog(requireContext())
        dragView.setContentView(R.layout.jackaroo_collection_dialog_fragment) {
            dismissAllowingStateLoss()
        }
        return dragView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        initData()
    }

    private fun initView(view: View) {
        gradeView = view.findViewById(R.id.jackaroo_collection_grade_view)
        view.findViewById<RecyclerView>(R.id.jackaroo_collection_type_rv)?.apply {
            val layoutManager = GridLayoutManager(requireContext(), 3)
            layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val type = mAdapter.getItemViewType(position)
                    if (type == JackarooCollectionDialogAdapter.TYPE_TITLE || type == JackarooCollectionDialogAdapter.TYPE_MORE) {
                        return 3
                    }
                    return 1
                }
            }
            this.layoutManager = layoutManager
            val paddingH = 16.dp - JackarooCollectionDialogAdapter.padding
            setPaddingRelative(paddingH, 0, paddingH, 0)
            adapter = mAdapter
        }
    }

    private fun initData() {
        viewModel.collectionTypeFlow.collectOn(lifecycleScope) {
            mAdapter.refresh(it.typeList)
            gradeView.update(it.grade, it.obtainedNum, it.totalNum)
        }
    }

    private fun showPropInfoDialog(item: PropItem, locked: Boolean) {
        val bottomText: String? = if (!locked || !item.isOpen) {
            null
        } else if (viewModel.uid == LoginHelper.getLoginUid()) {
            ResUtil.getString(R.string.jackaroo_game_record_go_to_buy)
        } else {
            ResUtil.getString(R.string.jackaroo_game_record_go_to_send)
        }

        PropInfoDialog.showOutOfShop(
            requireContext(),
            item.itemId,
            bottomText,
            "",
            object : PropDialogCallback() {
                override fun onClickSure(what: PropItem) {
                    super.onClickSure(what)
                    CocosShopUtil.gotoShop(context = requireContext(), propId = item.itemId)
                }
            })
    }

    companion object {

        /**
         * @param targetUid 用户UID
         * @param level 皮肤等级
         */
        fun show(activity: FragmentActivity, targetUid: Int, level: Int) {
            val fragment = JackarooCollectionDialogFragment()
            fragment.initFullWidth()
            fragment.initBottom()
            fragment.setWindowAnim(R.style.bottomDialogAnimation)
            fragment.setWindowAnim(com.huiwan.dialog.R.style.bottom_dialog_anim)
            val viewModel = ViewModelProvider(activity)[JackarooGameCareerViewModel::class.java]
            viewModel.onCollectionIntent(JackarooCollectionIntent.ShowDialog(level))
            fragment.show(activity, Bundle())
            val map = mapOf("goods_level" to level)
            TrackUtil.appViewScreen(ResUtil.getString(R.string.track_screen_collection_dialog), map)
        }
    }
}

interface IJackarooCollectionItemCallback {
    fun onMoreClick(type: Int)
    fun onItemClick(item: PropItem, locked: Boolean)
}