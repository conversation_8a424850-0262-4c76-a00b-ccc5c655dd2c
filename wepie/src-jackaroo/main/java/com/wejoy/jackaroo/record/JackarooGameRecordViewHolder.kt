package com.wejoy.jackaroo.record

import android.view.View
import android.widget.ImageView
import androidx.core.view.isVisible
import com.huiwan.base.ktx.invisible
import com.huiwan.base.ktx.show
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.dp
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.ui.tab.HWUITabLayout
import com.huiwan.base.util.ViewUtil
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.user.entity.User
import com.huiwan.widget.WejoyLabelLayout
import com.huiwan.widget.WejoyLabelTextColorTransformer
import com.huiwan.widget.WejoyLabelTextStyleTransformer
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.CollectionPageHeadBinding
import com.wepie.wespy.databinding.JackarooGameCareerChessShowLayBinding
import com.wepie.wespy.databinding.JackarooGameRecordFragementContentBinding
import com.wepie.wespy.databinding.JackarooGameRecordHeadViewBinding

internal val gameCareerTitleList = listOf(
    ResUtil.getStr(R.string.jackaroo_game_career_collection),
    ResUtil.getStr(R.string.user_game_career_title)
)

internal val gameRecordTitleList = listOf(
    ResUtil.getStr(R.string.jackaroo_game_record_total_stat),
    ResUtil.getStr(R.string.jackaroo_game_record_recent_stat)
)

fun initGameRecordLabelView(rootView: View): WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder> {
    val labelView =
        rootView.findViewById<WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder>>(R.id.jackaroo_game_record_tab_view)
    labelView.addTransformer(WejoyLabelTextStyleTransformer())
    labelView.addTransformer(
        WejoyLabelTextColorTransformer(
            ResUtil.getColor(R.color.color_text_accent_dark),
            ResUtil.getColor(R.color.color_text_primary_ex)
        )
    )

    labelView.setAdapter(object : WejoyLabelLayout.DefaultTabAdapter(gameRecordTitleList) {
        override fun tabLayoutRes(): Int = com.wepie.module.medal.R.layout.item_medal_type

        override fun findTextViewId(): Int = com.wepie.module.medal.R.id.medal_type_tv
    })
    return labelView
}

fun initJackarooCollectionLabelView(rootView: View): WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder> {
    val labelView =
        rootView.findViewById<WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder>>(R.id.jackaroo_collection_tab_view)
    labelView.addTransformer(WejoyLabelTextStyleTransformer())
    labelView.addTransformer(
        WejoyLabelTextColorTransformer(
            ResUtil.getColor(R.color.color_text_accent_dark),
            ResUtil.getColor(R.color.color_text_primary_ex)
        )
    )

    labelView.setAdapter(object : WejoyLabelLayout.DefaultTabAdapter(gameCareerTitleList) {
        override fun tabLayoutRes(): Int = com.wepie.module.medal.R.layout.item_medal_type

        override fun findTextViewId(): Int = com.wepie.module.medal.R.id.medal_type_tv
    })
    return labelView
}

fun gameRecordGameTypeTab(tabLayout: HWUITabLayout) {
    tabLayout.tabLayout.tabAdapter.apply {
        rootBackgroundStrategy = { _, select, v ->
            if (select) {
                v.setBackgroundResource(R.drawable.shape_ecf9f7_corner16)
            } else {
                v.setBackgroundResource(R.drawable.shape_fafafa_corner16)
            }
        }
        selectTextColor = ResUtil.getColor(R.color.color_accent)
        normalTextColor = ResUtil.getColor(R.color.color_text_secondary)
    }
}

class JackarooGameRecordHeadViewHolder(private val header: JackarooGameRecordHeadViewBinding) {

    fun updateUser(info: User?) {
        info ?: return
        header.userHeadImv.showUserHeadWithDecorationCache(info.uid, info.headimgurl)
        header.nameTv.text = info.nickname
        header.jackarooLevelView.setLevel(info.level)
    }

    fun setMarginTop(top: Int) {
        ViewUtil.setTopMargins(header.root, top)
    }

    fun getHeight(): Int {
        return header.root.measuredHeight
    }

    fun update(info: JackarooGameInfo) {
        val qualifyingGradeInfo = info.qualifyingGradeInfo
        val grade = ConstV3Info::class.instance().findQFGrade(qualifyingGradeInfo.grade)
        if (grade == null || grade.grade == 0) {
            header.userQualifyLay.jackarooCurrentQualifyingIv.setBackgroundResource(R.drawable.jackaroo_game_record_no_qualifying)
            header.userQualifyLay.jackarooCurrentQualifyingTv.text = ResUtil.getStr(R.string.none)
            header.userQualifyLay.jackarooGameRecordQualifyingGradeKingLay.gone()
        } else {
            WpImageLoader.load(grade.icon, header.userQualifyLay.jackarooCurrentQualifyingIv)
            header.userQualifyLay.jackarooCurrentQualifyingTv.text = grade.name
            header.userQualifyLay.jackarooGameRecordQualifyingGradeKingLay.isVisible =
                qualifyingGradeInfo.star > 0
            header.userQualifyLay.jackarooGameRecordQualifyingGradeKingTv.text =
                qualifyingGradeInfo.star.toString()
        }

    }

    fun updateChessInfo(inUseChessManId: Int, forceShowStaticPic: Boolean) {
        header.jackarooChessShowLay.bind(inUseChessManId, forceShowStaticPic)
    }

    fun showInUseChessInfo() {
        header.jackarooChessShowLay.root.show()
        header.userQualifyLay.root.gone()
    }

    fun showRankInfo() {
        header.jackarooChessShowLay.root.gone()
        header.userQualifyLay.root.show()
    }
}

internal fun JackarooGameRecordFragementContentBinding.bindRecordSummary(
    info: JackarooGameInfo,
    isUserHideGameRecord: Boolean
) {
    val qualifyingGradeInfo = info.qualifyingGradeInfo
    jackarooGameRecordTotalRoundNumTv.text =
        if (isUserHideGameRecord) "***" else info.playCount.toString()
    jackarooGameRecordWinRateTv.text = if (isUserHideGameRecord) "***" else
        ResUtil.getStr(R.string.jackaroo_game_record_total_win_ratio, info.getWinRatio())
    val grade = ConstV3Info::class.instance().findQFGrade(qualifyingGradeInfo.highestGrade)
    if (grade == null || grade.grade == 0) {
        jackarooGameRecordHighestQualifyingIv.setBackgroundResource(R.drawable.jackaroo_game_record_no_qualifying)
        jackarooGameRecordHighestQualifyingGradeKingLay.gone()
    } else {
        WpImageLoader.load(grade.icon, jackarooGameRecordHighestQualifyingIv)
        jackarooGameRecordHighestQualifyingGradeKingLay.isVisible =
            qualifyingGradeInfo.highestStar > 0
        jackarooGameRecordHighestQualifyingGradeKingTv.text =
            qualifyingGradeInfo.highestStar.toString()
    }
}

internal fun CollectionPageHeadBinding.bind(collectionHeadInfo: CollectionHeadInfo) {
    collectionValueNum.text = collectionHeadInfo.collectionValue.toString()
    collectionRankIv.invisible()
    collectionRankTv.invisible()
    if (collectionHeadInfo.collectionRank <= 0) {
        collectionRankTv.visible()
        collectionRankTv.text = "--"
        return
    }
    when (collectionHeadInfo.collectionRank) {
        1 -> {
            collectionRankIv.visible()
            collectionRankIv.setImageResource(R.drawable.rank_crown_rank1)
        }

        2 -> {
            collectionRankIv.visible()
            collectionRankIv.setImageResource(R.drawable.rank_crown_rank2)
        }

        3 -> {
            collectionRankIv.visible()
            collectionRankIv.setImageResource(R.drawable.rank_crown_rank3)
        }

        else -> {
            collectionRankTv.visible()
            collectionRankTv.text = collectionHeadInfo.collectionRank.toString()
        }
    }
}

/**
 * @param forceShowStaticPic 强制展示静态图
 */
internal fun JackarooGameCareerChessShowLayBinding.bind(
    inUseChessManId: Int,
    forceShowStaticPic: Boolean = false
) {
    val propItem = PropItemConfig::class.instance().getPropItem(inUseChessManId)
    propItem?.let {
        chessSkinNameTv.text = it.name
    }
    getChessGradeConfig(inUseChessManId)?.let {
        WpImageLoader.load(it.gradeIconUrl, gradeIv, ImageLoadInfo().height(24.dp).width(24.dp))
    }
    jackarooChessPicShowLay.bind(propItem, forceShowStaticPic)
}

internal fun updateGameCareerBg(iv: ImageView, inUseChessManId: Int) {
    getChessGradeConfig(inUseChessManId)?.let {
        WpImageLoader.load(it.gradeBackgroundUrl, iv)
    }
}

