package com.wejoy.jackaroo.record

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.core.graphics.toColorInt
import androidx.core.view.get
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.huiwan.base.ktx.dp
import com.huiwan.base.ktx.updateVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.component.activity.BaseActivity
import com.huiwan.constants.IntentConfig
import com.huiwan.user.LoginHelper
import com.huiwan.widget.WejoyLabelLayout
import com.huiwan.widget.image.DrawableUtil
import com.wejoy.littlegame.LittleGame
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooGameRecordActivityBinding
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wespy.component.suspend.SuspendManager
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.abs

class JackarooGameRecordActivity : BaseActivity() {

    private lateinit var binding: JackarooGameRecordActivityBinding
    private lateinit var labelView: WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder>

    private lateinit var adapter: FragmentStateAdapter

    private val headerViewHolder: JackarooGameRecordHeadViewHolder by lazy {
        JackarooGameRecordHeadViewHolder(binding.header)
    }

    private lateinit var viewModel: JackarooGameCareerViewModel

    private var gameCareerTabIndex = 0
    private var gameRecordTabIndex = 0
    private var gameRecordTabSubIndex = 0

    private var verticalOffset = -1
    private var isAppbarCollapsed = false

    private var uid = 0
    private val vpRv: RecyclerView by lazy { binding.jackarooCollectionVp[0] as RecyclerView }
    private val itemTouchListener: RvItemTouchListener by lazy {
        RvItemTouchListener(
            this
        )
    }

    private var subTabType: Int = SUB_TAB_TYPE_COLLECTION

    companion object {
        const val KEY_SUB_TAB_TYPE: String = "KEY_SUB_TAB_TYPE"

        //收藏页
        const val SUB_TAB_TYPE_COLLECTION: Int = 0

        //战绩页
        const val SUB_TAB_TYPE_GAME_RECORD: Int = 1
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.initStatusBar(this)
        binding = JackarooGameRecordActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        uid = intent.getIntExtra(IntentConfig.UID, -1)
        subTabType = intent.getIntExtra(KEY_SUB_TAB_TYPE, SUB_TAB_TYPE_COLLECTION)
        viewModel = ViewModelProvider(this)[JackarooGameCareerViewModel::class.java]
        viewModel.init(uid)
        initView()
        initData()
    }

    private fun initView() {
        initTitle()
        adapter = object : FragmentStateAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                if (position == 0) {
                    return CollectionFragment()
                }
                return GameRecordFragment().also {
                    itemTouchListener.iChildScrollStateProvider = it
                }
            }

            override fun getItemCount(): Int = gameCareerTitleList.size
        }
        binding.jackarooCollectionVp.adapter = adapter
        labelView = initJackarooCollectionLabelView(binding.root)
        labelView.setupViewPager(binding.jackarooCollectionVp)

        binding.jackarooCollectionVp.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                val isGameRecordTab = viewModel.isGameRecordTab(position)
                binding.goShopLay.updateVisible(!isGameRecordTab)
                viewModel.notifyEvent(JackarooGameCareerEvent.SwitchGameCareerTab(position))
                if (isGameRecordTab) {
                    headerViewHolder.showRankInfo()
                } else {
                    headerViewHolder.showInUseChessInfo()
                }
            }
        })
        binding.goShopLay.setOnDoubleClick {
            tryGotoCocosShop(it.context)
        }
        binding.goShopLay.background =
            DrawableUtil.genColorRadius(ResUtil.getColor(R.color.color_yellow_bg), 20.dp)
        binding.recordTitle.post {
            val height = binding.recordTitle.measuredHeight + headerViewHolder.getHeight()
            var imgHeight = (ScreenUtil.getScreenWidth() / 1500F * 1120).toInt()
            if (imgHeight + 40.dp < height) {
                imgHeight = height - 40.dp
                val imgWidth = (imgHeight / 1120F * 1500).toInt()
                ViewUtil.setViewSize(binding.jackarooGameRecordHeadBg, imgWidth, imgHeight)
            } else {
                ViewUtil.setViewSize(
                    binding.jackarooGameRecordHeadBg,
                    ScreenUtil.getScreenWidth(),
                    imgHeight
                )
            }
            ViewUtil.setViewHeight(binding.jackarooGameRecordHeadBg.parent as View, height)
            headerViewHolder.setMarginTop(binding.recordTitle.measuredHeight)
        }
    }

    private fun initTitle() {
        binding.recordTitle.apply {
            leftIcon =
                ResUtil.getDrawableWithTint2(R.drawable.action_bar_icon_back, R.color.white_color)
            titleColor = ResUtil.getColor(R.color.white_color)
            leftClick = {
                finish()
            }
            builder.title = ResUtil.getStr(R.string.user_game_career_title)
            if (uid == LoginHelper.getLoginUid()) {
                builder.rightIcon1Res = R.drawable.jackaroo_game_record_share_icon
                builder.rightClick = {
                    viewModel.doShare(
                        this@JackarooGameRecordActivity,
                        binding,
                        gameCareerTabIndex,
                        gameRecordTabIndex,
                        gameRecordTabSubIndex
                    )
                }
            } else {
                builder.title = ResUtil.getStr(R.string.activity_userinfo_detail_14)
            }
        }
        binding.recordAppbar.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            if (<EMAIL> == verticalOffset) {
                return@addOnOffsetChangedListener
            }
            isAppbarCollapsed = abs(verticalOffset) == appBarLayout.totalScrollRange
            <EMAIL> = verticalOffset
            initRecordTitle()
        }

    }

    private fun initData() {
        viewModel.recordInfoLiveData.observe(this) {
            headerViewHolder.update(it?.rankStat ?: JackarooGameInfo())
        }
        lifecycleScope.launch {
            viewModel.userInfoStateFlow.collect {
                headerViewHolder.updateUser(it)
            }
        }
        lifecycleScope.launch {
            viewModel.collectionAlbumFlow.collectLatest {
                updateGameCareerBg(binding.jackarooGameRecordHeadBg, it?.inUseChessPropId ?: 0)
                headerViewHolder.updateChessInfo(it?.inUseChessPropId ?: 0, false)
            }
        }
        lifecycleScope.launch {
            viewModel.flow.collect {
                when (it) {
                    is JackarooGameCareerEvent.SwitchGameCareerTab -> onSwitchGameCareerTab(it.tabIndex)

                    is JackarooGameCareerEvent.SwitchGameGameRecordTab -> onSwitchSubTab(it.tabIndex)

                    is JackarooGameCareerEvent.SelectGameType -> onSwitchThirdTab(it.index)

                    else -> Unit
                }
            }
        }
        when (subTabType) {
            SUB_TAB_TYPE_COLLECTION -> {
                binding.jackarooCollectionVp.currentItem = 0
            }

            SUB_TAB_TYPE_GAME_RECORD -> {
                binding.jackarooCollectionVp.currentItem = 1
            }
        }
    }

    private fun initRecordTitle() {
        val limitHeight = ScreenUtil.dip2px(70f)
        binding.recordTitle.apply {
            if (abs(verticalOffset.toDouble()) >= limitHeight) {
                //  Collapsed
                val backDrawable: Drawable = ResUtil.getDrawableWithTint(
                    R.drawable.action_bar_icon_back,
                    R.color.color_text_accent_dark
                )
                leftIcon = backDrawable
                titleColor = ResUtil.getColor(R.color.color_text_accent_dark)
                if (uid == LoginHelper.getLoginUid()) {
                    val shareDrawable: Drawable = ResUtil.getDrawableWithTint2(
                        R.drawable.jackaroo_game_record_share_icon,
                        R.color.color_text_accent_dark
                    )
                    builder.rightIcon1Res = shareDrawable
                }
                binding.titleBg.setAlpha(1f)
                StatusBarUtil.setStatusFontDarkColor(this@JackarooGameRecordActivity)
            } else {
                //Expanded
                val backDrawable: Drawable = ResUtil.getDrawableWithTint2(
                    R.drawable.action_bar_icon_back,
                    R.color.white_color
                )
                leftIcon = backDrawable
                titleColor = ResUtil.getColor(R.color.white_color)
                if (uid == LoginHelper.getLoginUid()) {
                    val shareDrawable: Drawable = ResUtil.getDrawableWithTint2(
                        R.drawable.jackaroo_game_record_share_icon,
                        R.color.white_color
                    )
                    builder.rightIcon1Res = shareDrawable
                }
                val alpha = (abs(verticalOffset.toDouble()) * 1f / limitHeight).toFloat()
                binding.titleBg.setAlpha(alpha)
                StatusBarUtil.setStatusFontWhiteColor(this@JackarooGameRecordActivity)
            }
        }
    }

    private fun tryGotoCocosShop(context: Context) {
        if (!checkCanGotoCocosShop()) {
            return
        }
        CocosShopUtil.gotoShop(context)
    }

    private fun checkCanGotoCocosShop(): Boolean {
        val isInForbidScene =
            VoiceRoomService.getInstance().isInVoiceRoom || SuspendManager.isStateMatching()
                    || LittleGame.teamInfo.tid > 0
        return !isInForbidScene
    }

    private fun onSwitchGameCareerTab(index: Int) {
        gameCareerTabIndex = index
    }

    private fun onSwitchSubTab(index: Int) {
        gameRecordTabIndex = index

    }

    private fun onSwitchThirdTab(subIndex: Int) {
        gameRecordTabSubIndex = subIndex
    }

}