package com.wejoy.jackaroo.home

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.DrawableRecycleUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.JackarooLevelView
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.user.LoginHelper
import com.wejoy.weplay.ex.context.toActivity
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.module.activity.ActivityDialogUtil
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.pay.commonapi.JumpPay

/**
 * from
 * [R.layout.jackaroo_home_main]
 */
class JackarooHomeHead(root: View) {
    private val headIv = root.findViewById<DecorHeadImgView>(R.id.head_iv)
    private val headBgIv = root.findViewById<ImageView>(R.id.home_head_bg_iv)
    private val coinTv = root.findViewById<TextView>(R.id.coin_tv)
    private val diamondTv = root.findViewById<TextView>(R.id.diamond_tv)
    private val levelView = root.findViewById<JackarooLevelView>(R.id.level_view)


    init {
        initEvents(root)
        JackarooFixTextUI(root, R.id.event_tv)
        root.findViewById<View>(R.id.status_bar_view).layoutParams.height =
            ScreenUtil.getStatusBarHeight()
        headIv.setBorderWidth(ScreenUtil.dip2px(1f))
        headIv.borderColor = Color.WHITE
        onVisibilityChanged(View.VISIBLE)
    }

    private fun initEvents(root: View) {
        headIv.setOnDoubleClick {
            JumpUtil.enterUserInfoDetailActivity(
                it.context, LoginHelper.getLoginUid(),
                TrackSource.HOME_PAGE
            )
        }
        root.findViewById<View>(R.id.coin_lay).setOnDoubleClick {
            JumpPay.showGoods(it.context, true, mapOf("refer_screen_name" to TrackScreenName.MAIN_GAME_PAGE))
        }
        root.findViewById<View>(R.id.diamond_lay).setOnDoubleClick {
            JumpPay.showGoods(it.context, false, mapOf("refer_screen_name" to TrackScreenName.MAIN_GAME_PAGE))
        }
        root.findViewById<View>(R.id.event_iv).setOnDoubleClick {
            ShenceEvent.appClick(
                TrackScreenName.HOME_PAGE,
                "top",
                "",
                TrackButtonName.ACTIVITY,
                ""
            )
            ActivityDialogUtil.showEventsPage(it.context.toActivity())
        }
        levelView.setOnDoubleClick { v: View ->
            ApiService.of(WebApi::class.java).gotoWebActivity(
                v.context,
                "${ConfigHelper.getInstance().constV3Info.gradeCenter.url}&target=${LoginHelper.getLoginUid()}&tab=hot"
            )
        }
    }


    fun refresh(stat: JackarooHomeStat) {
        refreshUserInfo(stat.user)
    }

    private fun refreshUserInfo(user: JackarooHomeStat.User) {
        // 使用 my decor 时刷新较晚。
        headIv.showUserHeadWithDecoration(user.uid.toInt())
        levelView.setLevel(user.level)
        coinTv.text = StringUtil.formatInteger(user.subCoin, "0.0")
        diamondTv.text = StringUtil.formatInteger(user.mainCoin, "0.0")
    }

    fun onVisibilityChanged(visibility: Int) {
        DrawableRecycleUtil.updateDrawableByVisibility(headBgIv, visibility) {
            WpImageLoader.load(
                ResUtil.getStr(R.string.jackaroo_home_nav_bg_url), headBgIv,
                ImageLoadInfo.newInfo().placeholder(R.drawable.jackaroo_home_nav_bg).screenWidth()
            )
        }
    }

}