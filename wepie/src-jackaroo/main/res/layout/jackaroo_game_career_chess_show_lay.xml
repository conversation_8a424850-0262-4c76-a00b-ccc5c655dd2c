<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="100dp"
    android:layout_height="120dp"
    android:clipChildren="false"
    tools:background="@color/color_blue_00cbf9">

    <com.huiwan.decorate.ChessSkinShowView
        android:id="@+id/jackaroo_chess_pic_show_lay"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/grade_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/chess_skin_name_tv"
        tools:src="@drawable/default_head_icon" />

    <View
        android:id="@+id/name_bg_view"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/jackaroo_game_career_chess_name_bg"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/chess_skin_name_tv"
        app:layout_constraintEnd_toEndOf="@id/chess_skin_name_tv"
        app:layout_constraintStart_toStartOf="@id/grade_iv"
        app:layout_constraintTop_toTopOf="@id/chess_skin_name_tv"
        app:layout_constraintWidth_max="88dp" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/chess_skin_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="26dp"
        android:ellipsize="end"
        android:maxWidth="68dp"
        android:maxLines="1"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jackaroo_chess_pic_show_lay"
        app:sizeAndFont="Body3|tajawal"
        tools:text="ofkeo"
        tools:textColor="@color/black" />

</androidx.constraintlayout.widget.ConstraintLayout>