<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="12dp">

    <View
        android:id="@+id/bg"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:background="@drawable/shape_f7f8fa_corner8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/collection_value_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="@id/collection_value_num"
        app:layout_constraintEnd_toStartOf="@id/collection_value_num"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/collection_value_num"
        app:srcCompat="@drawable/ic_collection_value" />

    <View
        android:id="@+id/divider"
        android:layout_width="1dp"
        android:layout_height="32dp"
        android:background="#D9D9D9"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/collection_value_num"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="2dp"
        android:gravity="center"
        android:textColor="@color/color_text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/collection_value_title"
        app:layout_constraintEnd_toStartOf="@id/divider"
        app:layout_constraintStart_toEndOf="@id/collection_value_iv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="200" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/collection_value_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="start"
        android:text="@string/jackaroo_game_career_current_collection_value"
        android:textColor="@color/color_text_tertiary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/collection_value_help"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/collection_value_num"
        app:sizeAndFont="Body2|tajawal"
        tools:text="/300" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/collection_value_help"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginStart="2dp"
        android:layout_marginTop="1dp"
        app:layout_constraintBottom_toBottomOf="@id/collection_value_title"
        app:layout_constraintEnd_toEndOf="@id/divider"
        app:layout_constraintStart_toEndOf="@id/collection_value_title"
        app:layout_constraintTop_toTopOf="@id/collection_value_title"
        app:srcCompat="@drawable/action_bar_icon_help"
        app:tint="@color/color_text_tertiary" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/collection_rank_iv"
        android:layout_width="24dp"
        android:layout_height="32dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/collection_rank_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/collection_rank_tv"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:gravity="center"
        android:textColor="@color/color_text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/collection_rank_iv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/divider"
        tools:text="/300" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/collection_rank_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="@string/jackaroo_game_record_current_qualifying"
        android:textColor="@color/color_text_tertiary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/collection_rank_arrow"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/divider"
        app:layout_constraintTop_toBottomOf="@id/collection_rank_iv"
        app:sizeAndFont="Body2|tajawal" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/collection_rank_arrow"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:tint="@color/color_text_default"
        app:layout_constraintBottom_toBottomOf="@id/collection_rank_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/collection_rank_title"
        app:layout_constraintTop_toTopOf="@id/collection_rank_title"
        app:srcCompat="@drawable/ic_arrow_right" />

</androidx.constraintlayout.widget.ConstraintLayout>