<?xml version="1.0" encoding="utf-8"?>
<com.huiwan.widget.Pager2HostConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/total_stat_host"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.huiwan.base.ui.tab.HWUITabLayout
        android:id="@+id/jackaroo_game_type_tab"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:hwTabLayout="button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/jackaroo_game_record_game_type_vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/jackaroo_game_type_tab" />

</com.huiwan.widget.Pager2HostConstraintLayout>