<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/jackaroo_game_record_share_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible" />

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/record_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:clipChildren="false"
        android:theme="@style/Theme.AppCompat.Light"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/jackaroo_collection_detail">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <FrameLayout
                android:id="@+id/jackaroo_game_record_head_lay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/jackaroo_game_record_head_bg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="-16dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitEnd"
                    android:src="@drawable/jackaroo_game_record_head_bg" />

                <include
                    android:id="@+id/header"
                    layout="@layout/jackaroo_game_record_head_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    app:layout_collapseMode="pin"
                    app:layout_collapseParallaxMultiplier="0.5" />

            </FrameLayout>

            <androidx.appcompat.widget.Toolbar
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:contentInsetEnd="0dp"
                app:contentInsetStart="0dp"
                app:layout_collapseMode="pin">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/title_bg"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:alpha="0"
                        android:background="@color/white"
                        app:layout_constraintBottom_toBottomOf="@+id/record_title"
                        app:layout_constraintEnd_toEndOf="@+id/record_title"
                        app:layout_constraintStart_toStartOf="@+id/record_title"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.huiwan.base.ui.WPNavBarView
                        android:id="@+id/record_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:with_status_bar="true" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <FrameLayout
            android:id="@+id/jackaroo_game_career_label_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-8dp"
            android:background="@drawable/shape_ffffff_tl12_tr12"
            android:paddingTop="8dp"
            app:layout_scrollFlags="noScroll">

            <com.huiwan.widget.WejoyLabelLayout
                android:id="@+id/jackaroo_collection_tab_view"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:paddingVertical="5dp"
                app:tabIndicator="@drawable/jackaroo_game_record_tab_indicator"
                app:tabIndicatorAnimationMode="linear"
                app:tabIndicatorColor="@color/color_accent"
                app:tabIndicatorFullWidth="false"
                app:tabMode="scrollable"
                app:tabPaddingEnd="16dp"
                app:tabPaddingStart="16dp"
                app:tabRippleColor="@null" />

            <LinearLayout
                android:id="@+id/go_shop_lay"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="16dp"
                android:orientation="horizontal"
                android:paddingHorizontal="8dp">

                <com.huiwan.base.ui.WPUIText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:text="@string/jackaroo_game_career_go_shop"
                    android:textColor="@color/color_yellow_default"
                    app:sizeAndFont="Body2|tajawal_bold" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    app:srcCompat="@drawable/ic_arrow_right"
                    app:tint="@color/color_yellow_default" />
            </LinearLayout>
        </FrameLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
        android:id="@+id/jackaroo_collection_detail"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/jackaroo_collection_vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>