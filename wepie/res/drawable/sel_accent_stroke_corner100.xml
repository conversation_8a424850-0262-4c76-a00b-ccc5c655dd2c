<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">



    <item android:state_enabled="false">
        <shape >
            <corners android:radius="100dp"/>
            <stroke android:color="@color/color_accent_grey"
                android:width="1dp"/>
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape >
            <corners android:radius="100dp"/>

            <stroke android:color="@color/color_accent_grey"
                android:width="1dp"/>
        </shape>
    </item>

    <item>
        <shape >
            <corners android:radius="100dp"/>

            <stroke android:color="@color/color_accent_ex"
                android:width="1dp"/>
        </shape>
    </item>

</selector>