<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M8,0L32,0A8,8 0,0 1,40 8L40,32A8,8 0,0 1,32 40L8,40A8,8 0,0 1,0 32L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F8F8F8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M13.716,23.844C15.092,23.874 16.687,22.891 18.5,20.895C21.22,17.902 22.824,15.715 26.234,16.431"
      android:strokeAlpha="0.3018043"
      android:strokeLineJoin="round"
      android:strokeWidth="1.8"
      android:fillColor="#00000000"
      android:strokeColor="@color/color_accent"
      android:fillType="evenOdd"
      android:fillAlpha="0.3018043"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13.716,16.295C15.238,16.676 16.446,17.27 17.34,18.078M21.22,22.139C22.661,23.461 24.091,24.158 26.234,23.708"
      android:strokeAlpha="0.3018043"
      android:strokeLineJoin="round"
      android:strokeWidth="1.8"
      android:fillColor="#00000000"
      android:strokeColor="@color/color_accent"
      android:fillType="evenOdd"
      android:fillAlpha="0.3018043"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.233,14l2.434,2.421l-2.434,2.421"
      android:strokeAlpha="0.3018043"
      android:strokeLineJoin="round"
      android:strokeWidth="1.8"
      android:fillColor="#00000000"
      android:strokeColor="@color/color_accent"
      android:fillType="evenOdd"
      android:fillAlpha="0.3018043"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.233,21.429l2.434,2.421l-2.434,2.421"
      android:strokeAlpha="0.3018043"
      android:strokeLineJoin="round"
      android:strokeWidth="1.8"
      android:fillColor="#00000000"
      android:strokeColor="@color/color_accent"
      android:fillType="evenOdd"
      android:fillAlpha="0.3018043"
      android:strokeLineCap="round"/>
</vector>
