<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="false" android:state_enabled="true">
        <shape>
            <solid android:color="@color/color_transparent50" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item android:state_pressed="true" android:state_enabled="true">
        <shape>
            <solid android:color="@color/color_transparent50" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/color_transparent50" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>