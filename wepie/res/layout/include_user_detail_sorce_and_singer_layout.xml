<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/setting_item_simple_bg"
    android:padding="16dp">

    <TextView
        android:id="@+id/score_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/activity_userinfo_detail_14"
        android:textColor="@color/color_text_accent_dark"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/game_stat_desc_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#999CB4"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="@+id/score_title"
        app:layout_constraintEnd_toStartOf="@+id/arrow"
        app:layout_constraintTop_toTopOf="@+id/score_title" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleX="@integer/image_scale_x"
        app:layout_constraintBottom_toBottomOf="@+id/score_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/score_title"
        app:srcCompat="@drawable/forward_icon_new" />

    <View
        android:id="@+id/qualifying_bg"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/shape_fafafa_corner8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/score_title" />

    <com.huiwan.decorate.ChessSkinShowView
        android:id="@+id/jackaroo_chess_pic_show_lay"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="12dp"
        app:layout_constraintBottom_toBottomOf="@id/qualifying_bg"
        app:layout_constraintStart_toStartOf="@id/qualifying_bg"
        app:layout_constraintTop_toTopOf="@id/qualifying_bg" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/collection__value_iv"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_collection_value"
        app:layout_constraintBottom_toBottomOf="@id/jackaroo_chess_pic_show_lay"
        app:layout_constraintStart_toEndOf="@id/jackaroo_chess_pic_show_lay"
        app:layout_constraintTop_toTopOf="@id/jackaroo_chess_pic_show_lay" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/collection_value_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:gravity="center"
        android:lines="1"
        android:textColor="@color/color_text_accent_dark"
        app:layout_constraintBottom_toBottomOf="@id/jackaroo_chess_pic_show_lay"
        app:layout_constraintStart_toEndOf="@id/collection__value_iv"
        app:layout_constraintTop_toTopOf="@id/jackaroo_chess_pic_show_lay"
        app:sizeAndFont="tajawal_bold|Body1"
        tools:text="155" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/collection_summary_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="@color/color_text_primary_ex"
        app:layout_constraintStart_toEndOf="@id/jackaroo_chess_pic_show_lay"
        app:layout_constraintTop_toBottomOf="@id/collection_value_tv"
        app:sizeAndFont="Body2|tajawal"
        tools:text="15/99" />

    <ImageView
        android:id="@+id/line"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:adjustViewBounds="true"
        android:background="@drawable/tilted_divider"
        app:layout_constraintBottom_toBottomOf="@id/qualifying_bg"
        app:layout_constraintEnd_toEndOf="@id/qualifying_bg"
        app:layout_constraintStart_toStartOf="@id/qualifying_bg"
        app:layout_constraintTop_toTopOf="@id/qualifying_bg" />

    <include
        android:id="@+id/game_current_rank_lay"
        layout="@layout/include_rank_score_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/qualifying_bg"
        app:layout_constraintEnd_toStartOf="@id/game_highest_rank_lay"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/line"
        app:layout_constraintTop_toTopOf="@id/qualifying_bg" />

    <include
        android:id="@+id/game_highest_rank_lay"
        layout="@layout/include_rank_score_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="@id/qualifying_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/game_current_rank_lay"
        app:layout_constraintTop_toTopOf="@id/qualifying_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>