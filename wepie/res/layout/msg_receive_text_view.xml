<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="start"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.huiwan.component.prop.ChatMsgBubbleItem
            android:id="@+id/chat_msg_bubble_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignStart="@id/msg_receive_text_lay"
            android:layout_alignTop="@id/msg_receive_text_lay"
            android:layout_alignEnd="@id/msg_receive_text_lay"
            android:layout_alignBottom="@id/msg_receive_text_lay" />

        <RelativeLayout
            android:id="@+id/msg_receive_text_lay"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="36dp"
            android:paddingStart="25dp"
            android:paddingEnd="20dp">

            <include
                layout="@layout/msg_gift_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layoutDirection="rtl" />

            <TextView
                android:id="@+id/text_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center_vertical"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="6dp"
                android:layout_marginBottom="16dp"
                android:gravity="start"
                android:lineSpacingExtra="2dp"
                android:minWidth="20dp"
                android:textColor="@color/color_primary_dark"
                android:textSize="16dp"
                tools:text="sdjkajdkajdjaksdjklasjdkasjkldjskadjkasjdkasjkdjaskdjskaj"
                tools:visibility="visible" />

            <include
                android:id="@+id/invite_layout"
                layout="@layout/msg_chat_invite_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:layout_marginBottom="12dp"
                android:visibility="gone" />

        </RelativeLayout>

        <include
            layout="@layout/msg_gift_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="36dp"
            android:layoutDirection="rtl"
            android:paddingStart="25dp"
            android:paddingEnd="20dp" />

    </RelativeLayout>

    <com.wepie.wespy.module.chat.gamemodel.MsgQuoteModel
        android:id="@+id/quote_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:visibility="gone"
        app:is_self="false" />
</LinearLayout>
