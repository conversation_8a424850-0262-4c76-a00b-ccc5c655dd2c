<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="start"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.huiwan.component.prop.ChatMsgBubbleItem
            android:id="@+id/chat_msg_bubble_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignStart="@id/msg_receive_text_lay"
            android:layout_alignTop="@id/msg_receive_text_lay"
            android:layout_alignEnd="@id/msg_receive_text_lay"
            android:layout_alignBottom="@id/msg_receive_text_lay" />

        <include
            android:id="@+id/msg_receive_text_lay"
            layout="@layout/msg_gift_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="36dp"
            android:layoutDirection="rtl"
            android:paddingStart="25dp"
            android:paddingEnd="20dp" />

    </RelativeLayout>

    <com.wepie.wespy.module.chat.gamemodel.MsgQuoteModel
        android:id="@+id/quote_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:visibility="gone"
        app:is_self="false" />
</LinearLayout>
