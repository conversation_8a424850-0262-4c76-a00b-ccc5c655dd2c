<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="end"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/msg_send_text_fail"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="4dp"
            android:background="@drawable/chat_send_fail"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/msg_send_text_progress"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_centerVertical="true"
            android:background="@drawable/msg_progress_circle"
            android:visibility="gone" />

        <com.huiwan.component.prop.ChatMsgBubbleItem
            android:id="@+id/chat_msg_bubble_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignStart="@id/msg_send_text_lay"
            android:layout_alignTop="@id/msg_send_text_lay"
            android:layout_alignEnd="@id/msg_send_text_lay"
            android:layout_alignBottom="@id/msg_send_text_lay" />

        <include
            android:id="@+id/msg_send_text_lay"
            layout="@layout/msg_gift_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:paddingStart="14dp"
            android:paddingEnd="18dp" />

        <LinearLayout
            android:id="@+id/emoji_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal" />

        <com.wepie.wespy.module.chat.gamemodel.MsgQuoteModel
            android:id="@+id/quote_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:gravity="end"
            android:visibility="gone"
            app:is_self="true" />

    </RelativeLayout>
</LinearLayout>