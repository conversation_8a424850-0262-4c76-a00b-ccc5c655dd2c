<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/new_gift_lay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="18dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/text_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/gift_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:layout_constraintBottom_toBottomOf="@+id/gift_desc_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/gift_title_tv"
            tools:src="@color/red"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/gift_title_tv"
            android:layout_width="167dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="56dp"
            android:layout_marginTop="14dp"
            android:gravity="start|center_vertical"
            android:minHeight="40dp"
            android:textAlignment="viewStart"
            android:textColor="@color/color_text_primary"
            android:textSize="15dp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="啦啦啦啦啦啦啦啦"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/gift_desc_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="56dp"
            android:layout_marginTop="2dp"
            android:paddingBottom="16dp"
            android:textAlignment="viewStart"
            android:textColor="@color/color_gift_desc"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gift_title_tv"
            tools:text="啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/gift_div_view"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="10dp"
        android:background="#F3F3F3"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/gift_ext_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:textColor="@color/color_gift_desc"
        android:textSize="12dp"
        android:visibility="gone"
        tools:text="收礼人获得99金币"
        tools:visibility="visible" />

    <View
        android:id="@+id/gift_extra_div_view"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="10dp"
        android:background="#F3F3F3"
        android:visibility="visible" />

    <TextView
        android:id="@+id/gift_extra_return_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:includeFontPadding="false"
        android:lineSpacingExtra="6dp"
        android:lineSpacingMultiplier="1"
        android:textColor="@color/color_gift_desc"
        android:textSize="12dp"
        android:visibility="gone"
        tools:text="触发了送礼防黑，金币额外+2000"
        tools:visibility="visible" />
</LinearLayout>