<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/personal_chat_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@id/quick_enter_rv"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/chat_bg_iv_mask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#33000000"
        android:scaleType="fitXY"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/quick_enter_rv"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/quick_enter_rv"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="10dp"
        android:paddingTop="10dp"
        app:layout_constraintBottom_toTopOf="@id/personal_chat_send_lay"
        app:layout_constraintStart_toStartOf="parent"
        tools:itemCount="4"
        tools:layoutManager="LinearLayoutManager"
        tools:listitem="@layout/personal_chat_view_quick_enter_item"
        tools:orientation="horizontal" />

    <!-- 发送消息按钮布局 -->
    <com.wepie.wespy.module.chat.send.SendViewModel
        android:id="@+id/personal_chat_send_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator_lay"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/quick_enter_rv"
        app:layout_constraintTop_toTopOf="parent">

        <com.huiwan.widget.actionbar.BaseWpActionBar
            android:id="@+id/personal_chat_tl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:liftOnScroll="true"
            app:liftOnScrollTargetViewId="@id/personal_chat_list_srl"
            app:show_status_view="true">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize">

                <LinearLayout
                    android:id="@+id/wp_title_left"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="0dp"
                    android:paddingEnd="16dp">

                    <ImageView style="@style/action_bar_left_arrow" />

                    <TextView
                        android:id="@+id/chat_unread_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:layout_marginStart="-8dp"
                        android:background="@drawable/shape_e7e7e7_corner100"
                        android:gravity="center"
                        android:maxWidth="64dp"
                        android:maxLines="1"
                        android:minWidth="24dp"
                        android:paddingStart="6dp"
                        android:paddingEnd="6dp"
                        android:singleLine="true"
                        android:textColor="#4A4A4A"
                        android:textSize="14dp"
                        tools:text="22" />
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/wp_title_right"
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:paddingStart="15dp"
                    android:paddingEnd="0dp">

                    <TextView
                        android:id="@+id/wp_title_right_tx"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="16dp"
                        android:gravity="center"
                        android:textColor="#ffffff"
                        android:textSize="15dp" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/wp_title_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="72dp"
                    android:layout_marginEnd="72dp"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxWidth="180dp"
                    android:singleLine="true"
                    android:text="@string/personal_chat_view_2"
                    android:textColor="#4A4A4A"
                    android:textSize="16dp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/add_friend_lay"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/white_color"
                app:layout_constraintTop_toBottomOf="@id/coordinator_lay">

                <View style="@style/LineHorizontal_333_0_5dp" />

                <ImageView
                    android:id="@+id/user_head_iv"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="7dp" />

                <TextView
                    android:id="@+id/add_friend_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@id/user_head_iv"
                    android:text="@string/personal_chat_view_3"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/add_friend_button"
                    android:layout_width="70dp"
                    android:layout_height="28dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/sel_accent_corner4"
                    android:backgroundTint="@color/sel_accent_common_selector"
                    android:gravity="center"
                    android:text="@string/personal_chat_view_4"
                    android:textColor="@color/white_color"
                    android:textSize="14sp" />
            </RelativeLayout>

        </com.huiwan.widget.actionbar.BaseWpActionBar>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/chat_refresher"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
            app:srlEnableAutoLoadMore="false"
            app:srlEnableOverScrollBounce="false"
            app:srlEnableOverScrollDrag="false">

            <com.wepie.wespy.module.game.game.activity.PullListView
                android:id="@+id/personal_chat_list_srl"
                style="@style/scrollbar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:cacheColorHint="#00000000"
                android:layoutDirection="ltr"
                android:overScrollMode="never" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <LinearLayout
            android:id="@+id/new_msg_indication_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/personal_chat_tl"
            android:layout_gravity="end"
            android:layout_marginTop="100dp"
            android:background="@drawable/new_group_msg_background"
            android:gravity="end"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                app:srcCompat="@drawable/two_arrow"
                app:tint="@color/color_accent" />

            <TextView
                android:id="@+id/new_msg_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="5dp"
                android:layout_marginBottom="1dp"
                android:includeFontPadding="false"
                android:textColor="@color/color_accent"
                tools:text="144条新消息" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/has_new_user_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/chat_refresher"
            android:layout_alignParentEnd="true"
            android:layout_gravity="bottom|end"
            android:layout_marginTop="64dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/bg_new_group_msg"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                app:srcCompat="@drawable/pulldown_icon"
                app:tint="@color/color_accent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="1dp"
                android:includeFontPadding="false"
                android:text="@string/msg_bottom_new_msg"
                android:textColor="@color/color_accent" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/can_down_view_lay"
            android:layout_width="52dp"
            android:layout_height="38dp"
            android:layout_alignBottom="@id/chat_refresher"
            android:layout_alignParentEnd="true"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/chat_backbottom_bg"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:srcCompat="@drawable/pulldown_icon"
                app:tint="@color/color_accent" />
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.huiwan.component.gift.show.GiftContentView
        android:id="@+id/gift_content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.gift.GiftComboView
        android:id="@+id/combo_view"
        android:layout_width="match_parent"
        android:layout_height="360dp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.wepie.wespy.module.chat.conversation.CustomListPopupWindow
        android:id="@+id/list_popupwindow"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>