<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/voice_room_member_group_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/voice_room_members_title"
        android:textColor="@color/color_text_primary"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@+id/voice_room_member_group_name_tv"
        app:layout_constraintEnd_toStartOf="@+id/voice_room_member_group_name_tv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/voice_room_member_group_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="@color/color_text_primary"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@+id/voice_room_member_group_title_tv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="wwwwwwwww" />
</androidx.constraintlayout.widget.ConstraintLayout>