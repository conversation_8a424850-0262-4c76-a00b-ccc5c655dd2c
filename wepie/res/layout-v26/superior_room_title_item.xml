<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-10dp"
        android:clipChildren="false"
        android:paddingHorizontal="15dp">

        <ImageView
            android:id="@+id/tab_iv"
            android:layout_width="86dp"
            android:layout_height="80dp"
            android:src="@drawable/superior_room_tab_select" />

        <TextView
            android:id="@+id/tab_tv"
            android:layout_width="60dp"
            android:layout_height="40dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:autoSizeMaxTextSize="14dp"
            android:autoSizeMinTextSize="8dp"
            android:autoSizePresetSizes="14dp"
            android:autoSizeStepGranularity="1dp"
            android:autoSizeTextType="uniform"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="60dp"
            android:maxLines="2"
            android:textAlignment="gravity"
            android:textColor="#ffffff"
            android:textSize="14dp"
            tools:text="房间 管理员" />
    </RelativeLayout>
</RelativeLayout>