package com.wepie.lib.huawei.pay;

import android.app.Activity;
import android.content.Intent;
import android.content.IntentSender;
import android.os.SystemClock;
import android.text.TextUtils;

import androidx.annotation.Keep;

import com.huawei.hmf.tasks.Task;
import com.huawei.hms.common.ResolvableApiException;
import com.huawei.hms.iap.Iap;
import com.huawei.hms.iap.IapApiException;
import com.huawei.hms.iap.IapClient;
import com.huawei.hms.iap.entity.ConsumeOwnedPurchaseReq;
import com.huawei.hms.iap.entity.ConsumeOwnedPurchaseResult;
import com.huawei.hms.iap.entity.InAppPurchaseData;
import com.huawei.hms.iap.entity.IsEnvReadyResult;
import com.huawei.hms.iap.entity.IsSandboxActivatedReq;
import com.huawei.hms.iap.entity.OrderStatusCode;
import com.huawei.hms.iap.entity.OwnedPurchasesReq;
import com.huawei.hms.iap.entity.OwnedPurchasesResult;
import com.huawei.hms.iap.entity.ProductInfo;
import com.huawei.hms.iap.entity.ProductInfoReq;
import com.huawei.hms.iap.entity.ProductInfoResult;
import com.huawei.hms.iap.entity.PurchaseIntentReq;
import com.huawei.hms.iap.entity.PurchaseIntentResult;
import com.huawei.hms.iap.entity.PurchaseResultInfo;
import com.huawei.hms.support.api.client.Status;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.platform.ThreadUtil;
import com.three.http.callback.EmptyDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackEvent;

import org.json.JSONObject;

import java.lang.ref.SoftReference;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("unused")
@Keep
public class HuaweiPay implements IapApi {
    private static final int PAY_REQ_CODE = 0x2024;
    private static final int RESOLVE_REQ_CODE = 0x2025;
    private static final int CURRENCY_RETRY_MAX_TIMES = 5;
    private static final String TEST_CURRENCY_PRODUCT = "com.wejoy.weplay.testgold100";
    private IapClient client;
    private IsEnvReadyResult isEnvReady;
    private Exception envErrorException;
    private String currency = "";
    private SoftReference<DataCallback<PayResult>> callbackWeakRef;

    private String cancelTempOrderId = null;

    @Override
    public void init() {
        HuaweiPayLog.e("init huawei iap client start");
        client = Iap.getIapClient(LibBaseUtil.getApplication());
        client.isEnvReady().addOnSuccessListener(isEnvReadyResult -> {
            HuaweiPay.this.isEnvReady = isEnvReadyResult;
            HuaweiPayLog.e("init huawei iap client success " + isEnvReadyResult.getReturnCode());
            registerObserver();
            tryUpdateCurrency();
            checkSandBox();
            checkUnHandledPurchase(false);
        }).addOnFailureListener(e -> {
            isEnvReady = null;
            envErrorException = e;
            if (e instanceof IapApiException) {
                IapApiException exception = (IapApiException) e;
                HuaweiPayLog.e("error init huawei iap client  " + exception.getStatus().toString());
            } else if (e instanceof ResolvableApiException) {
                HuaweiPayLog.e("error init huawei iap client resolvable " + e);
            } else {
                HuaweiPayLog.e("error init huawei iap client  " + e);
            }
        });
    }

    private void registerObserver() {
        ActivityTaskBuilderHolder.get().register(activity -> new ActivityTask() {
            @Override
            public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                if (requestCode == RESOLVE_REQ_CODE) {
                    checkHmsCoreUpdateResult(data);
                } else if (requestCode == PAY_REQ_CODE) {
                    if (data != null) {
                        checkPurchaseResult(data);
                    } else {
                        trackPay(cancelTempOrderId, "pay fail,code:-1,msg:data is null");
                    }
                }
            }
        });
    }

    private void checkPurchaseResult(Intent data) {
        PurchaseResultInfo purchaseResultInfo = client.parsePurchaseResultInfoFromIntent(data);
        int code = purchaseResultInfo.getReturnCode();
        HuaweiPayLog.e("huawei purchase code: {} msg: {},data: {}", code, purchaseResultInfo.getErrMsg(), purchaseResultInfo.getInAppPurchaseData());
        if (code == OrderStatusCode.ORDER_STATE_SUCCESS) {
            String purchaseData = purchaseResultInfo.getInAppPurchaseData();
            String purchaseSignature = purchaseResultInfo.getInAppDataSignature();
            checkVerifyPurchaseData(purchaseData, "buy");
            trackPay(cancelTempOrderId, "");
        } else if (code == OrderStatusCode.ORDER_PRODUCT_OWNED) {
            HuaweiPayLog.e("huawei purchase owned, try check unhandled purchase");
            checkUnHandledPurchase(true);
            trackPay(cancelTempOrderId, "pay fail,code:" + code + " msg:order already owned");
        } else if (code == OrderStatusCode.ORDER_STATE_CANCEL) {
            HuaweiPayLog.e("huawei purchase cancel");
            trackPay(cancelTempOrderId, "pay fail,code:" + code + " msg:cancel");
            cancel(code, ResUtil.getStr(R.string.ggp_user_cancelled));
        } else {
            String msg = purchaseResultInfo.getErrMsg();
            HuaweiPayLog.e("huawei purchase error {}, {}, __ {}", code, msg, purchaseResultInfo.getInAppPurchaseData());
            invokeFailedCallback(code, msg);
            trackPay(cancelTempOrderId, "pay fail,code:" + code + " msg:" + msg);
        }
    }

    private void checkHmsCoreUpdateResult(Intent data) {
        HuaweiPayLog.e("huawei hms core result " + data);
    }

    private void tryUpdateCurrency() {
        ProductInfoReq req = new ProductInfoReq();
        req.setPriceType(IapClient.PriceType.IN_APP_CONSUMABLE);
        req.setProductIds(Collections.singletonList(TEST_CURRENCY_PRODUCT));
        Task<ProductInfoResult> task = client.obtainProductInfo(req);
        task.addOnSuccessListener(productInfoResult -> {
            List<ProductInfo> productInfoList = productInfoResult.getProductInfoList();
            if (productInfoList == null || productInfoList.isEmpty()) {
                checkRetryUpdateCurrency();
                HuaweiPayLog.e("tryUpdateCurrency obtainProductInfo failed list null empty " + productInfoResult.getReturnCode() + ", " + productInfoResult.getErrMsg());
            } else {
                currency = productInfoList.get(0).getCurrency();
                HuaweiPayLog.e("tryUpdateCurrency obtainProductInfo successfully " + currency);
            }
        });
        task.addOnFailureListener(e -> {
            HuaweiPayLog.e("tryUpdateCurrency obtainProductInfo failed " + e);
            checkRetryUpdateCurrency();
        });
    }

    private void checkSandBox() {
        IsSandboxActivatedReq sandboxActivatedReq = new IsSandboxActivatedReq();
        client.isSandboxActivated(sandboxActivatedReq).addOnSuccessListener(isSandboxActivatedResult -> {
            boolean isSandBox = isSandboxActivatedResult.getIsSandboxUser();
            HuaweiPayLog.e("is sand box ok: {}", isSandBox);
            if (isSandBox) {
                ToastUtil.debugShow("huawei pay sandbox account");
            }
        });
    }

    private int currencyRetryTimes = 0;

    private final Runnable retryUpdateCurrencyRunner = () -> {
        HuaweiPayLog.e("retryUpdateCurrencyRunner " + currencyRetryTimes);
        tryUpdateCurrency();
    };

    private void checkRetryUpdateCurrency() {
        if (currencyRetryTimes < CURRENCY_RETRY_MAX_TIMES) {
            currencyRetryTimes++;
            ThreadUtil.runOnUiThreadDelay(currencyRetryTimes * 1000 * 3L, retryUpdateCurrencyRunner);
        } else {
            HuaweiPayLog.e("checkRetryUpdateCurrency obtainProductInfo failed ");
        }
    }

    private void verifyPurchaseToken(InAppPurchaseData purchaseData, String data, String tag) {
        HuaweiPayLog.e("verify token start " + tag + ",  " + data);
        HuaweiPayOrderApi.consumeServer(purchaseData.getDeveloperPayload(), purchaseData.getProductId(),
                purchaseData.getPurchaseToken(), String.valueOf(purchaseData.getPrice()), purchaseData.getCurrency(),
                purchaseData.getAccountFlag(), new com.three.http.callback.DataCallback<Object>() {
                    @Override
                    public void onSuccess(Result<Object> result) {
                        HuaweiPayLog.e("verify token success " + tag + ",  " + data);
                        consumePurchaseProduct(purchaseData, data);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        HuaweiPayLog.e("verify token failed " + tag + ",  " + code + ", " + msg);
                        invokeFailedCallback(code, msg);
                    }
                });
    }

    private void consumePurchaseProduct(InAppPurchaseData purchaseData, String data) {
        HuaweiPayLog.e("consume Purchase Product ");
        ConsumeOwnedPurchaseReq consumeOwnedPurchaseReq = new ConsumeOwnedPurchaseReq();
        consumeOwnedPurchaseReq.setPurchaseToken(purchaseData.getPurchaseToken());
        Task<ConsumeOwnedPurchaseResult> task = client.consumeOwnedPurchase(consumeOwnedPurchaseReq);
        PayResult result = new PayResult(purchaseData.getDeveloperPayload(), purchaseData.getProductId());
        result.msg = ResUtil.getStr(R.string.ggp_success);
        task.addOnSuccessListener(consumeOwnedPurchaseResult -> {
            HuaweiPayLog.e("consume success: " + data);
            invokeSuccessCallback(result);
        }).addOnFailureListener(e -> {
            HuaweiPayLog.e("consume failed: " + data);
            invokeSuccessCallback(result);
        });
    }

    private void invokeSuccessCallback(PayResult payResult) {
        SoftReference<DataCallback<PayResult>> ref = callbackWeakRef;
        if (ref != null) {
            DataCallback<PayResult> callback = ref.get();
            if (callback != null) {
                callback.onCall(payResult);
            }
        }
    }

    private void invokeFailedCallback(int code, String msg) {
        SoftReference<DataCallback<PayResult>> ref = callbackWeakRef;
        if (ref != null) {
            DataCallback<PayResult> callback = ref.get();
            if (callback != null) {
                callback.onFailed(code, msg);
            }
        }
    }

    @Override
    public void doPay(Activity activity, Object goodsInfo, Map<String, String> extParam, DataCallback<PayResult> callback) {
        HuaweiPayLog.e("start pay");
        if (goodsInfo instanceof WespyGoods) {
            checkEnv(activity, (WespyGoods) goodsInfo, callback, () -> {
                callbackWeakRef = new SoftReference<>(callback);
                queryProduct(activity, (WespyGoods) goodsInfo, extParam, callback);
            });
        }
    }

    private void queryProduct(Activity activity, WespyGoods goodsInfo, Map<String, String> extParam, DataCallback<PayResult> callback) {
        HuaweiPayLog.e("query Product");
        ProductInfoReq productInfoReq = new ProductInfoReq();
        productInfoReq.setProductIds(Collections.singletonList(goodsInfo.getGpProductId()));
        productInfoReq.setPriceType(IapClient.PriceType.IN_APP_CONSUMABLE);
        Task<ProductInfoResult> task = client.obtainProductInfo(productInfoReq);
        task.addOnSuccessListener(productInfoResult -> {
            if (productInfoResult.getReturnCode() == OrderStatusCode.ORDER_STATE_SUCCESS) {
                List<ProductInfo> productInfoList = productInfoResult.getProductInfoList();
                if (!productInfoList.isEmpty()) {
                    HuaweiPayLog.e("query Product -> start create order.");
                    createOrder(activity, goodsInfo, extParam, callback, productInfoList.get(0));
                } else {
                    HuaweiPayLog.e("get product info list empty");
                    invokeFailedCallback(-1, "ProductInfo Empty");
                    track("create_order", goodsInfo, "query product fail, error msg: result is empty");
                }
            } else {
                HuaweiPayLog.e("get product info error: {}" + productInfoResult.getReturnCode());
                invokeFailedCallback(-1, "ProductInfo Get Error " + productInfoResult.getReturnCode());
                track("create_order", goodsInfo, "query product fail, error code:" + productInfoResult.getReturnCode());
            }
        });
        task.addOnFailureListener(e -> {
            callback.onFailed(-1, "error query product info, " + e.getLocalizedMessage());
            track("create_order", goodsInfo, "query product fail, error msg:" + e);
        });
    }

    private void createOrder(Activity activity, WespyGoods goodsInfo, Map<String, String> extParam, DataCallback<PayResult> callback, ProductInfo productInfo) {
        cancelTempOrderId = "";
        if (productInfo != null) {
            currency = productInfo.getCurrency();
            HuaweiPayLog.e("start create order, update currency: " + currency);
        }
        HuaweiPayOrderApi.createOrder(goodsInfo, currency, extParam, new com.three.http.callback.DataCallback<OrderInfo>() {
            @Override
            public void onSuccess(Result<OrderInfo> result) {
                HuaweiPayLog.e("create wp order success,wp order id " + result.data.orderId);
                cancelTempOrderId = result.data.orderId;
                Map<String, Object> trackEventParam = getTrackEventParamCopy();
                clearTrackEventParam();
                // 订单创建完毕后上传数据，该数据是调用showPay时传入的，即click对应的埋点数据，需要稍作修改
                if (!trackEventParam.isEmpty()) {
                    trackEventParam.remove("btn_name");
                    trackEventParam.remove("btn_pos");
                    trackEventParam.put("order_id", result.data.orderId);
                    trackEventParam.put("goods_id", goodsInfo.goods_id);
                }
                TrackUtil.trackEvent(TrackEvent.CREATE_ORDER, trackEventParam);
                tryPurchase(activity, goodsInfo, extParam, result.data.orderId, callback, productInfo);
            }

            @Override
            public void onFail(int code, String msg) {
                HuaweiPayLog.e("create wp order failed " + code + ", " + msg);
                callback.onFailed(code, msg);
                track("create_order", goodsInfo, "create order fail,error msg:" + msg);
            }
        });
    }

    private void tryPurchase(Activity activity, WespyGoods goodsInfo, Map<String, String> extParam, String orderId, DataCallback<PayResult> callback, ProductInfo productInfo) {
        PurchaseIntentReq purchaseIntentReq = new PurchaseIntentReq();
        purchaseIntentReq.setPriceType(productInfo.getPriceType());
        purchaseIntentReq.setDeveloperPayload(orderId);
        purchaseIntentReq.setProductId(productInfo.getProductId());
        Task<PurchaseIntentResult> task = client.createPurchaseIntent(purchaseIntentReq);
        task.addOnSuccessListener(purchaseIntentResult -> {
            HuaweiPayLog.e("createPurchaseIntent successfully");
            Status status = purchaseIntentResult.getStatus();
            try {
                status.startResolutionForResult(ActivityTaskManager.getInstance().getTopActivity(), PAY_REQ_CODE);
                HuaweiPayLog.e("startResolutionForResult successfully");
                track("create_order", goodsInfo, orderId, "");
            } catch (IntentSender.SendIntentException exp) {
                HuaweiPayLog.e("error start purchase resolution {}", exp.getMessage());
                track("create_order", goodsInfo, "launch billing fail,error msg:" + exp);
            }
            callbackWeakRef = new SoftReference<>(callback);
        }).addOnFailureListener(e -> {
            HuaweiPayLog.e("error createPurchaseIntent" + e);
            callback.onFailed(-1, "error create purchase intent " + e);
            track("create_order", goodsInfo, orderId, "launch billing fail,error msg:" + e);
        });
    }

    private void checkEnv(Activity activity, WespyGoods goods, DataCallback<PayResult> callback, Runnable nextStep) {
        Task<IsEnvReadyResult> task = client.isEnvReady(false);
        task.addOnSuccessListener(isEnvReadyResult -> {
            if (isEnvReadyResult.getReturnCode() == 0) {
                track("connect", goods);
                nextStep.run();
            } else {
                track("connect", goods, "HMS is disconnect,error code:" + isEnvReadyResult.getReturnCode());
                callback.onFailed(-1, "env not ready: {}" + isEnvReadyResult.getReturnCode());
            }
        });
        task.addOnFailureListener(e -> {
            track("connect", goods, "HMS is disconnect,error msg:" + e);
            if (e instanceof ResolvableApiException) {
                ResolvableApiException exception = (ResolvableApiException) e;
                try {
                    exception.startResolutionForResult(activity, RESOLVE_REQ_CODE);
                    callback.onFailed(-1, "");
                } catch (IntentSender.SendIntentException sendIntentException) {
                    callback.onFailed(-1, sendIntentException.getLocalizedMessage());
                }
            } else {
                callback.onFailed(-1, e.getLocalizedMessage());
            }
        });
    }

    @Override
    public void tryRestore() {
        OwnedPurchasesReq ownedPurchasesReq = new OwnedPurchasesReq();
        ownedPurchasesReq.setPriceType(IapClient.PriceType.IN_APP_CONSUMABLE);
        Task<OwnedPurchasesResult> task = client.obtainOwnedPurchaseRecord(ownedPurchasesReq);
        task.addOnSuccessListener(ownedPurchasesResult -> {
            List<String> items = ownedPurchasesResult.getInAppPurchaseDataList();
            int count = Math.min(items.size(), 5);
            HuaweiPayLog.e("tryRestore record item num " + count);
            for (int i = 0; i < count; i++) {
                String item = items.get(i);
                ThreadUtil.runOnUiThreadDelay(1500 * i, () -> {
                    HuaweiPayLog.e("tryRestore record item " + item);
                    checkVerifyPurchaseData(item, "restore");
                });
            }
        });
        task.addOnFailureListener(e -> HuaweiPayLog.e("tryRestore failure " + e));
    }

    @Override
    public String getCurrency() {
        return currency;
    }

    private long lastCheckUnHandlePurchaseTime = 0;

    @Override
    public void checkUnHandledPurchase(boolean ignoreTime) {
        HuaweiPayLog.e("huawei checkUnHandledPurchase -> ignoreTime: " + ignoreTime);
        if (!ignoreTime) {
            if (SystemClock.elapsedRealtime() - lastCheckUnHandlePurchaseTime < 30_000L) {
                HuaweiPayLog.e("huawei checkUnHandledPurchase return.");
                return;
            }
        }

        if (!valid()) {
            HuaweiPayLog.e("huawei checkUnHandledPurchase is not valid.");
            return;
        }
        lastCheckUnHandlePurchaseTime = SystemClock.elapsedRealtime();
        OwnedPurchasesReq ownedPurchasesReq = new OwnedPurchasesReq();
        ownedPurchasesReq.setPriceType(IapClient.PriceType.IN_APP_CONSUMABLE);
        Task<OwnedPurchasesResult> task = client.obtainOwnedPurchases(ownedPurchasesReq);
        task.addOnSuccessListener(ownedPurchasesResult -> {
            HuaweiPayLog.e("checkUnHandledPurchase item num: " + ownedPurchasesResult.getInAppPurchaseDataList().size());
            for (String item : ownedPurchasesResult.getInAppPurchaseDataList()) {
                HuaweiPayLog.e("checkUnHandledPurchase item " + item);
                checkVerifyPurchaseData(item, "unhandled");
            }
        });
        task.addOnFailureListener(e -> HuaweiPayLog.e("checkUnHandledPurchase failure " + e));
    }

    private void checkVerifyPurchaseData(String data, String tag) {
        HuaweiPayLog.e("checkVerifyPurchaseData");
        try {
            InAppPurchaseData tokenData = new InAppPurchaseData(data);
            int state = tokenData.getPurchaseState();
            if (state == InAppPurchaseData.PurchaseState.PURCHASED) {
                verifyPurchaseToken(tokenData, data, tag);
            } else {
                invokeFailedCallback(-1, "purchase state error: " + state);
                HuaweiPayLog.e("checkVerifyPurchaseData purchase state error: " + state);
            }
        } catch (Exception e) {
            invokeFailedCallback(-1, "purchase state parse error: " + e);
            HuaweiPayLog.e("error parse purchase data, {}", e);
        }
    }

    private void cancel(int code, String msg) {
        HuaweiPayLog.e("cancelOrderPay, orderId: {}", cancelTempOrderId);
        try {
            if (!TextUtils.isEmpty(cancelTempOrderId)) {
                HuaweiPayOrderApi.cancelOrder(cancelTempOrderId, new EmptyDataCallback("HuaweiPay"));
                invokeFailedCallback(code, msg);
            } else {
                invokeFailedCallback(-1, "cancelOrderPay error: orderInfo is empty");
                HuaweiPayLog.e("cancelOrderPay error: orderInfo is empty");
            }
        } catch (Exception e) {
            invokeFailedCallback(-1, "cancelOrderPay error: " + e);
            HuaweiPayLog.e("cancelOrderPay error, {}", e);
        } finally {
            cancelTempOrderId = "";
        }
    }

    @Override
    public String payChannel() {
        return "huawei";
    }

    @Override
    public boolean valid() {
        return client != null && isEnvReady != null;
    }

    static void track(String step, WespyGoods wespyGoods) {
        track(step, wespyGoods, null, null);
    }

    static void track(String step, WespyGoods wespyGoods, String reason) {
        track(step, wespyGoods, null, reason);
    }

    static void track(String step, WespyGoods wespyGoods, String orderId, String reason) {
        Map<String, Object> trackProperty = new HashMap<>();
        trackProperty.put("step", step);
        trackProperty.put("product", wespyGoods.goods_name);
        trackProperty.put("goods_id", wespyGoods.goods_id);
        trackProperty.put("pay_platform", 14);
        trackProperty.put("status", TextUtils.isEmpty(reason) ? 1 : 0);
        trackProperty.put("reason", reason == null ? "" : reason);
        int id = 0;
        try {
            id = Integer.parseInt(orderId);
        } catch (Exception e) {
            //ignore e
        }
        trackProperty.put("order_id", id);
        TrackUtil.trackEvent(TrackEvent.PAY_PROCESS, new JSONObject(trackProperty));
    }

    static void trackPay(String orderId, String reason) {
        Map<String, Object> trackProperty = new HashMap<>();
        trackProperty.put("step", "request_to_pay");
        int id = 0;
        try {
            id = Integer.parseInt(orderId);
        } catch (Exception e) {
            //ignore e
        }
        trackProperty.put("order_id", id);
        trackProperty.put("pay_platform", 14);
        trackProperty.put("status", TextUtils.isEmpty(reason) ? 1 : 0);
        trackProperty.put("reason", reason == null ? "" : reason);
        TrackUtil.trackEvent(TrackEvent.PAY_PROCESS, new JSONObject(trackProperty));
    }
}
